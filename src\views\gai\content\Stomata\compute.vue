<template>
	<div class="active-m" ref="activeMainDiv"></div>
	<div class="Rbody">
		<div class="headerC">
			<div class="toptext flex">
				<img src="/@/assets/home.png" class="imgS" @click="goHome" />
				{{ taskName }}
			</div>
			<!--头部右边的头像-->
			<div class="righttext">
				<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
					<span class="logolayout">
						<img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" class="avatarl" />
						{{ username }}
					</span>
					<template #dropdown>
						<el-dropdown-menu>
							<!-- <el-dropdown-item command="/FootLast">首页</el-dropdown-item> -->
							<el-dropdown-item command="personal">{{ $t('user.dropdown2') }}</el-dropdown-item>
							<el-dropdown-item divided command="logOut">{{ $t('user.dropdown5') }}</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<personal-drawer ref="personalDrawerRef"></personal-drawer>
			</div>
		</div>
		<div class="content">
			<div class="aside">
				<div class="Mdropdown">
					<button @click="toggleDropdown" class="model-button">输入模型</button>
					<div v-if="isOpen" class="Mdropdown-content">
						<div class="flex-col1">
							<div v-for="(button, index) in ['模具型胶面']" :key="index" class="upload-item">
								<el-upload class="upload-demo" :auto-upload="false" :show-file-list="false" :on-change="(file) => handleFileUpload(index, file)">
									<el-button class="no-purple file-button">
										<span class="button-text">{{ uploadedFileNames[index] || button }}</span>
									</el-button>
								</el-upload>
							</div>
						</div>
						<div class="flex-col">
							<!-- <el-text class="text1" @click="showInsolesLibrary = true">模具通用库</el-text> -->
							<!-- <el-text class="text2" @click="showFootShapeLibrary = true">脚型通用库</el-text> -->
						</div>
					</div>
				</div>

				<!-- 通用库选择组件 -->
				<InsolesLibrary v-model="showInsolesLibrary" :userId="userIds" @confirm="handleInsolesConfirm" />
				<FootShapeLibrary v-model="showFootShapeLibrary" :userId="userIds" @confirm="handleFootShapeConfirm" />

				<foot-from ref="userDialogRef" @close="showFootFrom = false" @refresh="fetchData" />

				<shape-from ref="ShapeDialogRef" @close="showFootFrom = false" @refresh="fetchDatafoot" />

				<LineLatticeDialog ref="lineLatticeDialogRef" @confirm="handleLineLatticeConfirm" @cancel="handleLineLatticeCancel" />

				<div class="Pdropdown">
					<button @click="PtoggleDropdown" class="model-button">参数配置</button>
					<div v-if="isOpenP" class="Pdropdown-content">
						<div class="side-style">
							<!-- <div>
								生成方式：<el-select v-model="definitionName" placeholder="请选择" style="width: 150px">
									<el-option v-for="item in deFinitOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</div> -->
							<div>
								模具系列：<el-select v-model="ShoeSize" placeholder="请选择" style="width: 150px">
									<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</div>
							<div>
								气孔方向：<el-select v-model="FootSize" placeholder="请选择" style="width: 150px">
									<el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" class="selectS" />
								</el-select>
							</div>
							<div class="slider-container">
								<div class="slider-label">
									气孔矩阵：
									<el-input-number v-model="LatticeParam" :precision="0" :step="1" :max="10" :min="0" style="width: 120px; margin-left: 10px" />
								</div>
								<el-slider v-model="LatticeParam" :min="0" :max="10" :step="1" style="margin-top: 8px" />
							</div>

							<div class="slider-container">
								<div class="slider-label">
									气孔头部长度：
									<el-input-number v-model="EdgeWidth" :precision="3" :step="0.001" :max="1" :min="0" style="width: 120px; margin-left: 10px" />
								</div>
								<el-slider v-model="EdgeWidth" :min="0" :max="1" :step="0.001" style="margin-top: 8px" />
							</div>
							<div class="slider-container">
								<div class="slider-label">
									气孔头部大小：
									<el-input-number v-model="UnitSize" :precision="3" :step="0.001" :max="1" :min="0" style="width: 120px; margin-left: 10px" />
								</div>
								<el-slider v-model="UnitSize" :min="0" :max="1" :step="0.001" style="margin-top: 8px" />
							</div>

							<div class="slider-container">
								<div class="slider-label">
									气孔尾部大小：
									<el-input-number v-model="Rparam" :precision="1" :step="0.1" :max="1" :min="0" style="width: 120px; margin-left: 10px" />
								</div>
								<el-slider v-model="Rparam" :min="0" :max="1" :step="0.1" style="margin-top: 8px" />
							</div>

							<!-- <el-button @click="test">测试</el-button> -->
							<div class="bgbutton">
								<el-tooltip content="计算完成后才可再次提交" placement="top" v-if="!isValid">
									<el-button class="computeC" @click="sumbitCompute" id="compute" :disabled="!isValid">提交计算</el-button>
								</el-tooltip>
								<el-button class="computeC" @click="sumbitCompute" id="compute" :disabled="!isValid" v-else>提交计算</el-button>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="moudle-region">
				<div class="m-region">
					<img src="/src/assets/polygon.png" class="img-style" />
					<div class="row">
						<div class="mould-names">
							<div
								v-for="(mouldName, nameIndex) in ['模具型胶面']"
								:key="nameIndex"
								class="mould-name"
								:class="{ active: activeIndex === nameIndex }"
								@click="setActiveIndex(nameIndex)"
							>
								{{ mouldName }}
							</div>
							<!--stl模型显示区域-->
							<div class="square" ref="square"></div>
							<!-- 根据activeIndex显示对应的按钮 -->
							<div class="analysis-container">
								<div v-if="activeIndex === 0" class="analysis-group">
									<div class="analysis-content"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- <div class="loader" ref="loader"></div> -->
			<!-- aaa -->
			<div class="demo-progress">
				<el-progress type="circle" :percentage="percentage2" :color="colors" v-if="loader" :width="60" />
			</div>
			<!-- 模型显示区域-->
			<div class="left-regin">
				<div class="switchStyle">
					<button @click="switchToTopView()" class="switchTo"><img src="/@/assets/lookDownOn.png" class="button-icon" />俯视</button>
					<button @click="switchToBottomView()" class="switchTo">
						<img src="/@/assets/lookDownOn.png" class="button-icon" style="transform: rotate(180deg)" />仰视
					</button>
					<button @click="switchToFrontView()" class="switchTo"><img src="/@/assets/front.png" class="button-icon" />前视</button>
					<button @click="switchToLeftSideView()" class="switchTo">
						<img src="/@/assets/flank.png" class="button-icon" style="transform: rotate(180deg)" />左视
					</button>
					<button @click="switchToSideView()" class="switchTo"><img src="/@/assets/flank.png" class="button-icon" />右视</button>
					<button @click="switchToBackView()" class="switchTo"><img src="/@/assets/back.png" class="button-icon" />后视</button>
				</div>
				<div id="objectControls"></div>
				<div class="active-main"></div>
			</div>
		</div>
		<el-collapse v-if="resultBotton" class="result-values" accordion>
			<el-collapse-item class="text-resultbox">
				<template #title>
					<span class="text-result">结果</span>
				</template>
				<div style="display: flex; margin-top: 10px">
					<div class="result-text">气孔</div>
					<button id="downloadButton" class="load-button" @click="Countstandownload('stl')">
						<el-icon style="color: #c2abe5"> <Download /></el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="Countstandownload('3dm')">
						<el-icon style="color: #c2abe5"> <Download /></el-icon>3dm
					</button>
				</div>
				<!-- <div style="display: flex">
					<button id="downloadButton" class="load-button-all" @click="Alldownload('stl')">
						<el-icon style="color: #c2abe5"> <Download /> </el-icon>下载全部stl
					</button>
					<button id="downloadButton" class="load-button-all" @click="Alldownload('3dm')">
						<el-icon style="color: #c2abe5"> <Download /> </el-icon>下载全部3dm
					</button>
				</div> -->
			</el-collapse-item>
		</el-collapse>
	</div>
</template>

<script setup lang="ts">
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Rhino3dmLoader } from 'three/examples/jsm/loaders/3DMLoader';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
// import rhino3dm from 'https://unpkg.com/rhino3dm@7.15.0/rhino3dm.module.js';
// import { RhinoCompute } from 'https://www.unpkg.com/compute-rhino3d@0.13.0-beta/compute.rhino3d.module.js';
import rhino3dm from 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/rhino3dm.module.js';
import { RhinoCompute } from 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/compute.rhino3d.module.js';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader';
import { EXRLoader } from 'three/addons/loaders/EXRLoader.js';
import JSZip from 'jszip';
// 移除未使用的导入
import { STLExporter } from 'three/examples/jsm/exporters/STLExporter.js';
import { ref, onMounted, nextTick, watch, computed } from 'vue';
import { Search, Loading } from '@element-plus/icons-vue';
import {
	uploadGHFile,
	uploadCompute,
	getGHFileById,
	uploadStl,
	uploadAddress,
	searchFootLast,
	searchFootShape,
	getDataType,
	deleteFootLast,
	deleteFootShape,
	getDeleteById,
	uploadSettings,
	downloadAnalysisFile,
	downloadPoreFile,
	// rhinoCompute,
} from '/@/api/gai/foot';

// import { useUserInfo } from '/@/stores/userInfo';
import { ElMessageBox, ElMessage } from 'element-plus';
import mittBus from '/@/utils/mitt';
import { logout } from '/@/api/login';
import { Session } from '/@/utils/storage';
import { useI18n } from 'vue-i18n';
import { userList } from '/@/api/admin/user';
// 移除未使用的导入
// const stores = useUserInfo();
const { t } = useI18n();
// const { userInfos } = storeToRefs(stores);
const personalDrawerRef = ref();
const PersonalDrawer = defineAsyncComponent(() => import('/@/views/admin/system/user/personal.vue'));
const FootFrom = defineAsyncComponent(() => import('/@/views/gai/components/footLastDialog.vue'));
const ShapeFrom = defineAsyncComponent(() => import('/@/views/gai/components/footShapeDialog.vue'));
const InsolesLibrary = defineAsyncComponent(() => import('/@/components/InsolesLibrary/index.vue'));
const FootShapeLibrary = defineAsyncComponent(() => import('/@/components/FootShapeLibrary/index.vue'));
const LineLatticeDialog = defineAsyncComponent(() => import('/@/views/gai/components/lineLatticeDialog.vue'));
// 控制 FootFrom 组件的显示
const showFootFrom = ref(false);
// 控制通用库组件的显示
const showInsolesLibrary = ref(false);
const showFootShapeLibrary = ref(false);
const taskId = ref();
const taskName = ref();
const taskIdR = ref();
const route = useRoute();
const avatar = ref();
const username = ref();
const userIds = ref();
const saveSettings = ref<{
	checkbox_0: boolean;
	color_0: string;
	opacity_0: string;
}>({
	checkbox_0: true,
	color_0: '#73CE82',
	opacity_0: '1',
});
const defaultSettings = ref({
	checkbox_0: true,
	color_0: '#73CE82',
	opacity_0: '1',
});
const definitionName = ref('pore');
// 移除未使用的变量
const Toggle = ref(true);
const isValid = ref(true);
const deFinitOptions = [
	{
		value: 'pore',
		label: '气孔生成',
	},
];
const options = [
	{
		value: '0',
		label: '内仁',
	},
	{
		value: '1',
		label: '底膜',
	},
];
const options1 = [
	{
		value: '0',
		label: 'Z',
	},
	{
		value: '1',
		label: '-Z',
	},
];

// 新增参数选项配置
const spreadOptions = [
	{
		value: 0,
		label: '四边形',
	},
	{
		value: 1,
		label: '三角形',
	},
	{
		value: 2,
		label: '六边形',
	},
];

const lineTypeOptions = [
	{
		value: 0,
		label: 'Polyline',
	},
	{
		value: 1,
		label: 'Nurbscurve',
	},
	{
		value: 2,
		label: 'InterCenter',
	},
	{
		value: 3,
		label: 'EdgeCenter',
	},
	{
		value: 4,
		label: '(Edge+Face)Center',
	},
	{
		value: 5,
		label: '(V+Face)Center',
	},
];

const latticeTypeOptions = [
	{
		value: 0,
		label: '参数晶格',
	},
	{
		value: 1,
		label: '线晶格',
	},
	{
		value: 2,
		label: '极小曲面',
	},
];

const functionOptions = [
	{
		value: 0,
		label: 'Gyroid',
	},
	{
		value: 1,
		label: 'SchwarzP',
	},
	{
		value: 2,
		label: 'Diamond',
	},
	{
		value: 3,
		label: 'Neovius',
	},
	{
		value: 4,
		label: 'I-WP',
	},
	{
		value: 5,
		label: 'Scherk1',
	},
	{
		value: 6,
		label: 'Scherk2',
	},
	{
		value: 7,
		label: 'Scherk3',
	},
	{
		value: 8,
		label: 'Dprime',
	},
	{
		value: 9,
		label: 'Lidinoid',
	},
	{
		value: 10,
		label: 'SplitP',
	},
];
const ShoeSize = ref(options[0].value);
const FootSize = ref(options1[0].value);
// 这些变量暂时注释掉，因为未使用
// const StanFootLast = ref<number>(7);
// const StanFootLast1 = ref<string>('7.00');
// const GGCS = ref<number>(0);
// const GGCS1 = ref<string>('0.0');
// const GirthControl = ref<number>(1);
// const GirthControl1 = ref<string>('1.0');
// const FootHeaderSpace = ref<number>(0);
// const FootHeaderSpace1 = ref<string>('0.0');
// const TarsalCir = ref<number>(0);
// const TarsalCir1 = ref<string>('0.0');

const ggtjcs = ref<number>(0);
// const ggtjcs1 = ref<string>('0.0'); // 未使用，暂时注释

// 新增参数配置
const EdgeSwitch = ref<boolean>(true);
const EdgeWidth = ref<number>(0.234);
const UnitSize = ref<number>(0.125);
const LatticeParam = ref<number>(4);
const SpreadOption = ref<number>(2);
const LineType = ref<number>(0);
const ParameterT = ref<number>(0.5);
const LatticeType = ref<number>(1);
const Rparam = ref<number>(0.4);
const Function = ref<number>(0);

const ToggleValue = ref();
const FootLastValue = ref();
const FunsionLastValue = ref();
const ShoeSizeValue = ref();
// 未使用的变量，暂时注释
// const StanFootLastValue = ref();
const FootSizeValue = ref();
// const GGCSValue = ref();
// const GirthControlValue = ref();
// const TarsalCirValue = ref();
// const FootHeaderSpaceValue = ref();

// 新增参数的Value变量
const EdgeSwitchValue = ref();
const EdgeWidthValue = ref();
const UnitSizeValue = ref();
const LatticeParamValue = ref();
const SpreadOptionValue = ref();
const LineTypeValue = ref();
const ParameterTValue = ref();
const LatticeTypeValue = ref();
const RparamValue = ref();
const FunctionValue = ref();
const CrvCellValue = ref();
const settingsData = ref({
	id: route.query.projectId,
	settings: '',
});
const uploadedFile = ref({
	doc: '',
	Csdoc: '',
	Coudoc: '',
	Standoc: '',
	Latticedoc: '',
});
const Rhino = ref({
	id: '',
	rhinoFile: '',
});
const FootLastDialog = ref(false);
const FootShapeDialog = ref(false);
const typeData = ref();
const selectedOptions = ref<{ [key: string]: string[] }>({});
interface SelectData {
	// [key: string]: string | number; // 支持动态属性和值为 string 或 number
	[key: string]: string | number | string[] | number[]; // 允许字符串、数字、字符串数组、数字数组
}

const selectData = ref<SelectData>({});
const images = ref<string[]>([]);
const totalItems = ref(0);

const selectedImage = ref<string | null>(null);
const selectedFootLastFile = ref<string | null>(null);
const selectedShoeItem = ref<any>(null); // 用于存储选中项的详细信息
const stlFile = ref([]);
const address = ref('D:/Minio/gai/');
// const footL = ref('D:/Rhino文件/做好的gh文件/095D.stl');
// const footF = ref('D:/Rhino文件/做好的gh文件/SJFR.stl');
const footL = ref();
const footF = ref();
const FootLast = ref();
const FunsionLast = ref();
const imagesWithFiles = ref();
const userDialogRef = ref();
const ShapeDialogRef = ref();
const hoveringIndex = ref();
// const userId = ref('1');
const page = ref({
	current: 1, // 当前页
	size: 9, // 每页条数
});

// 定义需要手动输入的字段 (使用 ename)
const footLastInputFields = ['brand', 'leatherShoes', 'grountPattern', 'lastBottomGirth', 'heelHeight'];
const footShapeInputFields = ['footLength', 'bottomGirth'];

// 判断字段是否为输入字段
const isInputField = (ename: string, dialogType: 'last' | 'shape') => {
	if (dialogType === 'last') {
		return footLastInputFields.includes(ename);
	} else if (dialogType === 'shape') {
		return footShapeInputFields.includes(ename);
	}
	return false;
};

const commonFlag = ref(true);
const privateFlag = ref(false);

// 线晶格库相关变量
const lineLatticeDialogRef = ref();
const selectedLineLattice = ref<any>(null);
const CrvCell = ref<string>(''); // 线晶格库参数字段
const selectedLineLatticeFileName = ref<string>(''); // 保存选中的文件名称

const percentage2 = ref(0);

const colors = [
	{ color: '#f56c6c', percentage: 20 },
	{ color: '#e6a23c', percentage: 40 },
	{ color: '#6f7ad3', percentage: 60 },
	{ color: '#1989fa', percentage: 80 },
	{ color: '#5cb87a', percentage: 100 },
];

const setFlag = async (type) => {
	// 重置选中的图片
	selectedImage.value = null;

	if (type === 'common') {
		commonFlag.value = true;
		privateFlag.value = false;
		await fetchData();
	} else if (type === 'private') {
		commonFlag.value = false;
		privateFlag.value = true;
		await fetchData();
	}
	if (type === 'commonS') {
		commonFlag.value = true;
		privateFlag.value = false;
		await fetchDatafoot();
	} else if (type === 'privateS') {
		commonFlag.value = false;
		privateFlag.value = true;
		await fetchDatafoot();
	}
};

const openFootLastDialog = async () => {
	commonFlag.value = true;
	privateFlag.value = false;
	const type = '0';
	FootLastDialog.value = true;
	const result = await getDataType(type);
	typeData.value = result.data;
	// 清空旧的筛选条件
	selectedOptions.value = {};
	selectData.value = {};
	// 重置选中的图片
	selectedImage.value = null;

	await fetchData();
};

// 打开线晶格库弹窗
const openLineLatticeDialog = () => {
	if (lineLatticeDialogRef.value) {
		lineLatticeDialogRef.value.openDialog(userIds.value, CrvCell.value);
	}
};

// 路径标准化函数
const normalizePath = (path: string): string => {
	if (!path) return '';
	// 将反斜杠统一转换为正斜杠，这样在JSON序列化时不会产生双反斜杠
	return path.replace(/\\/g, '/');
};

// 线晶格库选择确认
const handleLineLatticeConfirm = (selectedItem: any) => {
	if (selectedItem) {
		selectedLineLattice.value = selectedItem;
		// 标准化路径格式，避免双反斜杠问题
		CrvCell.value = normalizePath(selectedItem.file); // 将选中的线晶格库file字段标准化后赋值给CrvCell参数
		selectedLineLatticeFileName.value = selectedItem.fileName; // 保存文件名称
		ElMessage.success(`已选择线晶格: ${selectedItem.fileName}`);
	}
};

// 线晶格库选择取消
const handleLineLatticeCancel = () => {
	// 可以在这里处理取消逻辑
};
const isLoading = ref(false);

const fetchData = async (params: SelectData = {}) => {
	isLoading.value = true;
	try {
		const queryParams = { ...params };
		if (commonFlag.value) {
			queryParams.userId = 1;
		} else {
			queryParams.userId = userIds.value;
		}
		const result = await searchFootLast(page.value, queryParams);
		if (result.ok && result.data) {
			totalItems.value = result.data.total;
			imagesWithFiles.value = result.data.records.map((record: any) => ({
				image: record.image,
				file: record.file,
				id: record.id,
				fileName: record.fileName,
				brand: record.brand,
				category: record.category,
				headShape: record.headShape,
				heelHeight: record.heelHeight,
				shoeSize: record.shoeSize,
				shoeType: record.shoeType,
				grountPattern: record.grountPattern,
				lastBottomGirth: record.lastBottomGirth,
				leatherShoes: record.leatherShoes,
				sportsShoesType: record.sportsShoesType,
				style: record.style,
			}));

			// 自动选择第一项
			selectFirstItem();
		}
	} catch (error) {
		console.error('Error fetching data:', error);
	} finally {
		isLoading.value = false;
	}
};
const openFootShapeDialog = async () => {
	commonFlag.value = true;
	privateFlag.value = false;
	const type = '1';
	FootShapeDialog.value = true;
	const result = await getDataType(type);
	typeData.value = result.data.filter((item: any) => !['183', '184', '185'].includes(item.id));
	// 清空旧的筛选条件
	selectedOptions.value = {};
	selectData.value = {};
	// 重置选中的图片
	selectedImage.value = null;
	await fetchDatafoot();
};
const fetchDatafoot = async (params: SelectData = {}) => {
	isLoading.value = true;
	try {
		const queryParams = { ...params };
		if (commonFlag.value) {
			queryParams.userId = 1;
		} else {
			queryParams.userId = userIds.value;
		}
		const result = await searchFootShape(page.value, queryParams);
		if (result.ok && result.data) {
			totalItems.value = result.data.total;
			imagesWithFiles.value = result.data.records.map((record: any) => ({
				image: record.image,
				file: record.file,
				id: record.id,
				fileName: record.fileName,
				category: record.category,
				weightIndex: record.weightIndex,
				footarch: record.footarch,
				age: record.age,
				shoeSize: record.shoeSize,
				footWidth: record.footWidth,
				bottomGirth: record.bottomGirth,
				footLength: record.footLength,
				heightInstep: record.heightInstep,
				heelStraight: record.heelStraight,
				halluxValgus: record.halluxValgus,
				footShape: record.footShape,
				exceptionItem: record.exceptionItem,
			}));

			// 自动选择第一项
			selectFirstItem();
		}
	} catch (error) {
		console.error('Error fetching data:', error);
	} finally {
		isLoading.value = false;
	}
};

// 处理页码变化
const handlePageChange = async (newPage: number) => {
	page.value.current = newPage;

	// 重置选中的图片
	selectedImage.value = null;

	// 根据当前打开的对话框类型调用相应的函数
	if (FootLastDialog.value) {
		// 鞋垫通用库对话框
		const searchParams = { ...selectData.value };

		// 处理选择的选项
		for (const [key, values] of Object.entries(selectedOptions.value)) {
			if (values && values.length > 0) {
				const item = typeData.value.find((type: any) => type.id === key);
				if (item && item.ename) {
					const selectedNames = values
						.map((valueId) => {
							const child = item.children.find((child: any) => child.id === valueId);
							return child ? child.nameType : null;
						})
						.filter((name) => name !== null);
					if (selectedNames.length > 0) {
						searchParams[item.ename] = selectedNames;
					}
				}
			}
		}

		// 设置用户ID
		if (commonFlag.value) {
			searchParams.userId = 1;
		} else {
			searchParams.userId = userIds.value;
		}

		await fetchData(searchParams);
	} else {
		// 脚型通用库对话框
		const searchParams = { ...selectData.value };

		// 处理选择的选项
		for (const [key, values] of Object.entries(selectedOptions.value)) {
			if (values && values.length > 0) {
				const item = typeData.value.find((type: any) => type.id === key);
				if (item && item.ename) {
					const selectedNames = values
						.map((valueId) => {
							const child = item.children.find((child: any) => child.id === valueId);
							return child ? child.nameType : null;
						})
						.filter((name) => name !== null);
					if (selectedNames.length > 0) {
						searchParams[item.ename] = selectedNames;
					}
				}
			}
		}

		// 设置用户ID
		if (commonFlag.value) {
			searchParams.userId = 1;
		} else {
			searchParams.userId = userIds.value;
		}

		await fetchDatafoot(searchParams);
	}
};
const imageRows = computed(() => {
	const rows: { image: string; file: string; fileName: string; id: string }[][] = [];

	if (!Array.isArray(imagesWithFiles.value)) {
		return rows; // 返回一个空的行数组
	}

	const pagedItems = imagesWithFiles.value;
	for (let i = 0; i < pagedItems.length; i += 3) {
		rows.push(pagedItems.slice(i, i + 3));
	}
	return rows;
});

const updateSelectData = () => {
	// 清空 selectData
	Object.keys(selectData.value).forEach((key) => {
		selectData.value[key] = [];
	});

	// const excludedIds = new Set([183, 184, 185]);

	// 根据 selectedOptions 更新 selectData
	Object.entries(selectedOptions.value).forEach(([key, values]) => {
		if (Array.isArray(values)) {
			const selectedNames = values
				.map((value) => {
					// 查找 typeData 中与 selectedOptions 值对应的 nameType
					const item = typeData.value.find((type) => type.id === key);
					if (item) {
						const child = item.children.find((child) => child.id === value);
						return child ? child.nameType : null;
					}
					return null;
				})
				.filter((name) => name !== null);
			// 更新 selectData 对应字段
			const typeName = typeData.value.find((type) => type.id === key)?.ename;
			if (typeName) {
				selectData.value[typeName] = selectedNames;
			}
		}
	});

	selectData.value.userId = 1;
};

const addfootLast = async (id: string) => {
	userDialogRef.value.openDialog(id);
};

const deleteImage = async (items) => {
	try {
		await ElMessageBox.confirm('确定要删除这个鞋垫吗？', '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		// 用户确认删除
		const ids = [items.id];
		// 过滤掉需要删除的图片
		imagesWithFiles.value = imagesWithFiles.value.filter((item) => item.image !== items.imageSrc);

		const result = await deleteFootLast(ids);
		if (result) {
			ElMessage.success('删除成功');
			await fetchData();
		}
	} catch (error) {
		// 用户取消删除
		if (error === 'cancel') {
		}
	}
};

const deleteImageShape = async (items) => {
	try {
		await ElMessageBox.confirm('确定要删除这个脚型文件吗？', '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		// 用户确认删除
		const ids = [items.id];
		// 过滤掉需要删除的图片
		imagesWithFiles.value = imagesWithFiles.value.filter((item) => item.image !== items.imageSrc);

		const result = await deleteFootShape(ids);
		if (result) {
			ElMessage.success('删除成功');
			await fetchDatafoot();
		}
	} catch (error) {
		// 用户取消删除
		if (error === 'cancel') {
		}
	}
};
const addfootShape = async (id) => {
	ShapeDialogRef.value.openDialog(id);
};

const selectImage = (image: string) => {
	const selectedItem = imagesWithFiles.value.find((item) => item.image === image);
	if (selectedItem) {
		selectedImage.value = image;
		selectedFootLastFile.value = selectedItem.file;
		selectedShoeItem.value = selectedItem;
		if (FootLastDialog.value) {
			uploadedFileNames.value[0] = selectedItem.fileName;
		} else if (FootShapeDialog.value) {
			uploadedFileNames.value[1] = selectedItem.fileName;
		}
	}
};
const inputFields = ['brand', 'grountPattern', 'lastBottomGirth', 'heelHeight']; // 定义需要输入框的字段
const search = async () => {
	const searchParams: SelectData = {};

	// 处理下拉选择的值
	Object.entries(selectedOptions.value).forEach(([key, values]) => {
		if (values && values.length > 0) {
			const item = typeData.value.find((type: any) => type.id === key);
			if (item && item.ename) {
				const selectedNames = values
					.map((valueId) => {
						const child = item.children.find((child: any) => child.id === valueId);
						return child ? child.nameType : null;
					})
					.filter((name) => name !== null);
				if (selectedNames.length > 0) {
					searchParams[item.ename] = selectedNames; // API 可能需要数组
				}
			}
		}
	});

	// 处理输入框的值 (过滤空值)
	Object.entries(selectData.value).forEach(([key, value]) => {
		if (value !== '' && value !== null && value !== undefined) {
			// 如果是需要手动输入的字段，将其值包装成数组
			if ((FootLastDialog.value && footLastInputFields.includes(key)) || (FootShapeDialog.value && footShapeInputFields.includes(key))) {
				searchParams[key] = [value]; // 包装成数组
			} else {
				// 其他输入字段（如果未来有的话）可能不需要包装，或者根据后端要求处理
				searchParams[key] = value;
			}
		}
	});

	if (commonFlag.value) {
		searchParams.userId = 1;
	} else {
		searchParams.userId = userIds.value;
	}

	if (FootLastDialog.value) {
		await fetchData(searchParams);
	} else {
		await fetchDatafoot(searchParams);
	}
};
const reset = async () => {
	// 清空下拉选择和输入框的值
	selectedOptions.value = {};
	selectData.value = {};
	// 重置选中的图片
	selectedImage.value = null;
	// 重新获取数据
	if (FootLastDialog.value) {
		await fetchData();
	} else {
		await fetchDatafoot();
	}
};
const sumbit = async (type: string) => {
	// 清除之前的解析结果
	if (anaDoc && type === 'last') {
		anaDoc.delete();
		anaDoc = new rhino.File3dm();
	}
	if (sceneAnay && type === 'last') {
		sceneAnay.clear();
	}
	if (FootLastDialog.value) {
		// Handle index 0
		updateStlFile(0, selectedFootLastFile.value);
		setActiveIndex(0);
	} else {
		// Handle index 1
		if (footL.value == undefined) {
			updateStlFile(0, '');
		}

		updateStlFile(1, selectedFootLastFile.value);
		setActiveIndex(1);
	}

	updateFootprints();
	assignStlFileToStlConfig();

	// Close dialogs based on the state
	if (FootLastDialog.value) {
		FootLastDialog.value = false;
	} else {
		FootShapeDialog.value = false;
	}
};

function updateStlFile(index, file) {
	const existingIndex = stlFile.value.findIndex((value) => value.index === index);
	if (existingIndex !== -1) {
		stlFile.value.splice(existingIndex, 1);
	}
	stlFile.value.splice(index, 0, { data: file, index });
}

function updateFootprints() {
	const { footLValue, footFValue } = calculateFootprints(stlFile.value, address);
	footL.value = footLValue;
	footF.value = footFValue;
}

function calculateFootprints(stlFiles, address) {
	let footLValue = '';
	let footFValue = '';

	stlFiles.forEach((file, index) => {
		const data = file.data;
		const indexs = file.index;
		if (data) {
			const parts = data.split('/');
			const filename = parts[parts.length - 1];
			const footprint = address.value + filename;
			if (indexs == 0) {
				footLValue = footprint;
			} else if (indexs == 1) {
				footFValue = footprint;
			}
		}
	});

	return { footLValue, footFValue };
}

// 处理鞋垫通用库确认选择
const handleInsolesConfirm = (data: { file: string; fileName: string; item: any }) => {
	// 清除之前的解析结果
	if (anaDoc) {
		anaDoc.delete();
		anaDoc = new rhino.File3dm();
	}
	if (sceneAnay) {
		sceneAnay.clear();
	}

	// Handle index 0
	updateStlFile(0, data.file);
	setActiveIndex(0);
	uploadedFileNames.value[0] = data.fileName;

	updateFootprints();
	assignStlFileToStlConfig();
};

// 处理脚型通用库确认选择
const handleFootShapeConfirm = (data: { file: string; fileName: string; item: any }) => {
	// Handle index 1
	if (footL.value == undefined) {
		updateStlFile(0, '');
	}

	updateStlFile(1, data.file);
	setActiveIndex(1);
	uploadedFileNames.value[1] = data.fileName;

	updateFootprints();
	assignStlFileToStlConfig();
};

onMounted(async () => {
	showSpinner(false);
	// init();
	const projectId = route.query.projectId;
	const projectName = route.query.taskName;
	const projectIdR = route.query.taskId;
	//任务id
	taskId.value = projectId;
	taskName.value = projectName;
	//任务关联的项目id
	taskIdR.value = projectIdR;
	const result = await userList();
	avatar.value = result.data.sysUser.avatar;
	username.value = result.data.sysUser.username;
	userIds.value = result.data.sysUser.userId;
	await getParamConfig();
});

const resultBotton = ref(false);
// const moudleConfig = ref()
const rhinoAddress = ref();
const getParamConfig = async () => {
	const result = await getGHFileById(taskId.value);
	if (!result.data) {
		return; // 如果没有数据，提前返回
	}
	const paramConfig = result.data.paramConfig ? JSON.parse(result.data.paramConfig) : null;
	const computeResult = result.data.computeResult ? JSON.parse(result.data.computeResult) : null;
	const stlMoudleConfig = result.data.stlMouldfile ? JSON.parse(result.data.stlMouldfile) : null;
	let rhinoFile;
	let rhinoFileString;
	if (typeof result.data.rhinoFile == 'string' && result.data.rhinoFile) {
		rhinoFile = JSON.parse(result.data.rhinoFile);
		if (rhinoFile.length > 1) {
			rhinoFileString = JSON.parse(rhinoFile);
		}
	}

	if (result.data.settings) {
		saveSettings.value = JSON.parse(result.data.settings);
	}

	if (paramConfig && stlMoudleConfig) {
		stlFile.value.splice(0, 0, stlMoudleConfig.mouldfile1);
		stlFile.value.splice(1, 0, stlMoudleConfig.mouldfile2);
		assignStlFileToStlConfig();
		const { footLValue, footFValue } = calculateFootprints(stlFile.value, address);
		footL.value = footLValue;
		footF.value = footFValue;

		FootLast.value = footL.value;
		FunsionLast.value = footF.value;

		definitionName.value = paramConfig.definitionName;
		Toggle.value = paramConfig.toggle;
		ShoeSize.value = paramConfig.shoeSize;
		FootSize.value = paramConfig.footSize;

		// 恢复新增参数
		if (paramConfig.edgeSwitch !== undefined) EdgeSwitch.value = paramConfig.edgeSwitch;
		if (paramConfig.edgeWidth !== undefined) EdgeWidth.value = paramConfig.edgeWidth;
		if (paramConfig.unitSize !== undefined) UnitSize.value = paramConfig.unitSize;
		if (paramConfig.latticeParam !== undefined) LatticeParam.value = paramConfig.latticeParam;
		if (paramConfig.spreadOption !== undefined) SpreadOption.value = paramConfig.spreadOption;
		if (paramConfig.lineType !== undefined) LineType.value = paramConfig.lineType;
		if (paramConfig.parameterT !== undefined) ParameterT.value = paramConfig.parameterT;
		if (paramConfig.latticeType !== undefined) LatticeType.value = paramConfig.latticeType;
		if (paramConfig.rparam !== undefined) Rparam.value = paramConfig.rparam;
		if (paramConfig.function !== undefined) Function.value = paramConfig.function;
		if (paramConfig.crvCell !== undefined) {
			// 恢复时也进行路径标准化
			CrvCell.value = normalizePath(paramConfig.crvCell);
			// 恢复选中的文件名称
			if (paramConfig.selectedLineLatticeFileName !== undefined) {
				selectedLineLatticeFileName.value = paramConfig.selectedLineLatticeFileName;
			}
			// 如果有保存的CrvCell值，设置selectedLineLattice显示信息
			if (paramConfig.crvCell) {
				selectedLineLattice.value = {
					fileName: paramConfig.selectedLineLatticeFileName || '已选择的线晶格',
					file: normalizePath(paramConfig.crvCell),
				};
			}
		}

		// 恢复上传的文件名
		if (paramConfig.uploadedFileNames && Array.isArray(paramConfig.uploadedFileNames)) {
			uploadedFileNames.value = paramConfig.uploadedFileNames;
		}

		ToggleValue.value = Toggle.value;
		FootLastValue.value = FootLast.value;
		FunsionLastValue.value = FunsionLast.value;
		ShoeSizeValue.value = ShoeSize.value;
		FootSizeValue.value = FootSize.value;

		// 恢复新增参数的Value变量
		EdgeSwitchValue.value = EdgeSwitch.value;
		EdgeWidthValue.value = EdgeWidth.value;
		UnitSizeValue.value = UnitSize.value;
		LatticeParamValue.value = LatticeParam.value;
		SpreadOptionValue.value = SpreadOption.value;
		LineTypeValue.value = LineType.value;
		ParameterTValue.value = ParameterT.value;
		LatticeTypeValue.value = LatticeType.value;
		RparamValue.value = Rparam.value;
		FunctionValue.value = Function.value;
		CrvCellValue.value = CrvCell.value;

		if (computeResult) {
			FootMeasurements.value.ShoeSize = computeResult.ShoeSize;
			FootMeasurements.value.AdjustQuantity = computeResult.AdjustQuantity;
		}

		if (stlFile.value[0] !== '') {
			setActiveIndex(0);
		}

		if (rhinoFileString && rhinoFileString.doc !== undefined) {
			const fileUrl = rhinoFileString.doc;

			const fileExists = await isValidFile(fileUrl);

			if (fileExists) {
				rhinoAddress.value = fileUrl;
				uploadedFile.value = rhinoFileString;
				resultBotton.value = true;
				initRhinoFromSavedConfig();
			} else {
				console.error('文件不存在或无法访问:', fileUrl);
				// 如果需要，显示错误信息给用户
				// ElMessage.error({
				// 	message: '指定的文件不存在或无法访问',
				// 	duration: 3000, // 设置提示持续时间为 2 秒
				// });
			}
		} else {
			console.error('rhinoFileString is undefined or doc is not available');
		}
	} else {
		console.log('没有结果');
		init(); // 如果没有配置，也需要初始化场景
		return;
	}
};
const checkFileExistence = async (url) => {
	try {
		const response = await fetch(url, { method: 'HEAD' });

		if (response.ok) {
			// 文件存在
			return true;
		} else if (response.status === 404) {
			// 文件不存在
			return false;
		} else {
			// 其他错误，例如网络问题或服务端错误
			console.error(`HTTP 错误: ${response.status}`);
			return false;
		}
	} catch (error) {
		console.error('检查文件时发生错误:', error);
		return false;
	}
};
const isValidFile = async (url) => {
	const isExist = await checkFileExistence(url);
	return isExist;
};

const router = useRouter();
const goHome = () => {
	router.push({ path: '/StomataQuest', query: { projectId: taskIdR.value.toString() } });
	//router.go(-1)
};

const onHandleCommandClick = (path: string) => {
	if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('user.logOutTitle'),
			message: t('user.logOutMessage'),
			showCancelButton: true,
			confirmButtonText: t('user.logOutConfirm'),
			cancelButtonText: t('user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('user.logOutExit');
					setTimeout(() => {
						done();
						setTimeout(() => {
							instance.confirmButtonLoading = false;
						}, 300);
					}, 700);
				} else {
					done();
				}
			},
		})
			.then(async () => {
				// 关闭全部的标签页
				mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 3, ...router }));
				// 调用后台接口
				await logout();
				// 清除缓存/token等
				Session.clear();
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				// window.location.reload();
				router.push('/login');
			})
			.catch(() => {});
	} else if (path === 'personal') {
		// 打开个人页面
		personalDrawerRef.value.open();
	} else {
		router.push(path);
	}
};

const isOpen = ref(true);
const toggleDropdown = () => {
	isOpen.value = !isOpen.value;
};
const isOpenP = ref(true);
const PtoggleDropdown = () => {
	isOpenP.value = !isOpenP.value;
};
const isOpenResult = ref(false);
const PtoggleDropdownResult = () => {
	isOpenResult.value = !isOpenResult.value;
};

const paramConfig = computed(() => ({
	toggle: Toggle.value,
	shoeSize: ShoeSize.value,
	footSize: FootSize.value,
	definitionName: definitionName.value,
	// 新增参数配置
	edgeSwitch: EdgeSwitch.value,
	edgeWidth: EdgeWidth.value,
	unitSize: UnitSize.value,
	latticeParam: LatticeParam.value,
	spreadOption: SpreadOption.value,
	lineType: LineType.value,
	parameterT: ParameterT.value,
	latticeType: LatticeType.value,
	rparam: Rparam.value,
	function: Function.value,
	crvCell: CrvCell.value, // 线晶格库参数
	selectedLineLatticeFileName: selectedLineLatticeFileName.value, // 线晶格库文件名称
	uploadedFileNames: uploadedFileNames.value, // 添加文件名数组到配置中
}));

const stlconfig = ref<{
	[key: string]: { data: string | null; index: number } | string; // 允许空字符串
}>({
	mouldfile1: '',
	mouldfile2: '',
	mouldfile3: '',
	mouldfile4: '',
	mouldfile5: '',
	mouldfile6: '',
});

const form = computed(() => ({
	id: taskId.value,
	paramConfig: JSON.stringify(paramConfig.value),
	stlMouldfile: JSON.stringify(stlconfig.value),
}));
const assignStlFileToStlConfig = () => {
	// 确保 stlFile.value 是数组
	if (!Array.isArray(stlFile.value)) {
		console.error('stlFile.value is not an array:', stlFile.value);
		// 可以选择重置 stlconfig 或抛出错误
		stlconfig.value = { mouldfile1: '', mouldfile2: '', mouldfile3: '', mouldfile4: '', mouldfile5: '', mouldfile6: '' };
		return;
	}

	for (let i = 0; i < stlFile.value.length; i++) {
		const fileItem = stlFile.value[i];
		// 确保 fileItem 是对象并且有 index 属性
		if (typeof fileItem === 'object' && fileItem !== null && typeof fileItem.index === 'number') {
			const key = `mouldfile${fileItem.index + 1}`;
			// 确保 key 是 stlconfig 的有效键
			if (key in stlconfig.value) {
				stlconfig.value[key] = fileItem;
			} else {
				console.warn(`Invalid key generated: ${key}`);
			}
		} else {
			console.warn(`Invalid item in stlFile.value at index ${i}:`, fileItem);
		}
	}

	// 清空未使用的 mouldfile 槽位
	for (let i = 1; i <= 6; i++) {
		const key = `mouldfile${i}`;
		// 检查当前槽位是否被填充
		const isFilled = stlFile.value.some(
			(fileItem: any) => typeof fileItem === 'object' && fileItem !== null && `mouldfile${fileItem.index + 1}` === key
		);
		if (!isFilled && key in stlconfig.value) {
			stlconfig.value[key] = ''; // 如果未填充，则设置为空字符串
		}
	}
};
const assignStlFileToStl = () => {
	const fileMapping = { 0: 'mouldfile1', 1: 'mouldfile2' };

	for (const file of stlFile.value) {
		const key = fileMapping[file.index];
		if (key) {
			stlconfig.value[key] = file;
		}
	}

	// Clear remaining mouldfile slots if needed
	for (const index of [0, 1]) {
		if (!stlFile.value.some((file) => file.index === index)) {
			const key = fileMapping[index];
			stlconfig.value[key] = '';
		}
	}
};

const activeIndex = ref<number | null>(0);

const stlAddress = ref();
//上传文件地址
// const stlFile = ref([])
const handleFileUpload = async (index: number, file: any) => {
	if (!file) return;

	if (!checkAllowedFileTypes(file.raw)) {
		ElMessage.error('只允许上传后缀名为3dm、stl、obj或stp的文件');
		return;
	}

	const formData = new FormData();
	formData.append('mouldFile', file.raw);

	const result = await uploadStl(formData);

	if (result.ok) {
		// 清除之前的解析结果
		if (anaDoc && index === 0) {
			anaDoc.delete();
			anaDoc = new rhino.File3dm();
		}
		if (sceneAnay && index === 0) {
			sceneAnay.clear();
		}

		// 更新文件名显示
		uploadedFileNames.value[index] = file.name;

		// 根据上传的文件类型重置对应的解析状态
		if (index === 0) {
			lastAnalysisStatus.value = {
				loading: false,
				finished: false,
				success: false,
			};
		} else if (index === 1) {
			footAnalysisStatus.value = {
				loading: false,
				finished: false,
				success: false,
			};
		}

		// 删除同index的旧文件（如果存在）
		const existingFileIndex = stlFile.value.findIndex((file) => file.index === index);
		if (existingFileIndex !== -1) {
			stlFile.value.splice(existingFileIndex, 1);
		}

		// 添加新文件
		stlFile.value.push({ data: result.data, index: index });

		// 更新当前显示
		setActiveIndex(index);

		// 更新 stlconfig 对象的属性
		assignStlFileToStlConfig();

		ElMessage.success({
			message: '上传成功',
			duration: 1500,
		});

		const { footLValue, footFValue } = calculateFootprints(stlFile.value, address);
		footL.value = footLValue;
		footF.value = footFValue;
	} else {
		ElMessage.error('上传失败');
	}
};

// 辅助函数：检查文件类型是否符合要求
const checkAllowedFileTypes = (file: File): boolean => {
	const allowedTypes = ['3dm', 'stl', 'obj', 'stp'];
	const fileType = getFileExtension(file.name).toLowerCase();
	return allowedTypes.includes(fileType);
};

// 辅助函数：获取文件后缀名
const getFileExtension = (filename: string): string => {
	return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
};

const handleFileButtonClick = (index: number, event: MouseEvent) => {
	event.preventDefault();
	const input = document.getElementById('fileInput-' + index) as HTMLInputElement;
	input.click();
};

const setActiveIndex = (index: number) => {
	activeIndex.value = index;

	// 清除当前场景中的所有模型
	if (sceneStl) {
		while (sceneStl.children.length > 0) {
			sceneStl.remove(sceneStl.children[0]);
		}
	}
	if (sceneAnay && index === 1) {
		while (sceneAnay.children.length > 0) {
			sceneAnay.remove(sceneAnay.children[0]);
		}
	}

	// 修改stl显示逻辑，根据当前选中的标签页显示对应的模型
	const currentFile = stlFile.value.find((file) => file.index === index);
	if (currentFile && currentFile.data) {
		stlAddress.value = currentFile.data;
		const fileMode = stlAddress.value.split('.').pop();

		let colorFlage = index === 1 ? 1 : 0;
		initStl(fileMode, colorFlage);

		// 如果已经有解析结果，重新渲染解析模型
		if (anaDoc && anaDoc.objects().count > 0 && index === 0) {
			nextTick(() => {
				initAnay();
			});
		}
	} else {
		// 如果没有找到对应的文件，清空显示区域
		stlAddress.value = null;
		if (rendererStl) {
			rendererStl.clear();
		}
		ElMessage.info('未上传文件');
	}
};
const intervalId = ref();
const timing = () => {
	if (intervalId.value) {
		clearInterval(intervalId.value); // 清除上一个定时器
	}
	resetProgress();
	const intervalDuration = 500;
	const maxProgress = 100;
	const progressDuration = 90 * 1000;
	const progressIncrement = 95 / (progressDuration / intervalDuration);
	const maxProgressDuringInterval = 95;
	const errorTimeout = 6 * 60 * 1000;

	let progress = 0;
	let hasReached90Seconds = false;
	const startTime = Date.now();

	intervalId.value = setInterval(async () => {
		const elapsedTime = Date.now() - startTime;

		// 更新进度条
		if (elapsedTime < progressDuration) {
			progress += progressIncrement;
			if (progress > maxProgressDuringInterval) {
				progress = maxProgressDuringInterval;
			}

			if (Math.floor(progress) !== Math.floor(percentage2.value)) {
				percentage2.value = Math.floor(progress);
			}
		} else {
			hasReached90Seconds = true;
			if (Math.floor(percentage2.value) !== maxProgressDuringInterval) {
				percentage2.value = Math.floor(maxProgressDuringInterval);
			}
		}

		if (elapsedTime > errorTimeout) {
			clearInterval(intervalId.value);
			showSpinner(false);
			ElMessage.error('计算超时，请重试！');
			isValid.value = true;
			return;
		}

		try {
			const result = await getGHFileById(taskId.value);
			const paramConfig = JSON.parse(result.data.paramConfig);
			//const computeResult = JSON.parse(result.data.computeResult);
			const stlMoudleConfig = JSON.parse(result.data.stlMouldfile);
			let rhinoFile;
			let rhinoFileString;
			if (typeof result.data.rhinoFile == 'string' && result.data.rhinoFile) {
				rhinoFile = JSON.parse(result.data.rhinoFile);
				if (rhinoFile.length > 1) {
					rhinoFileString = JSON.parse(rhinoFile);
				}
			}
			if (paramConfig && stlMoudleConfig && rhinoFileString) {
				clearInterval(intervalId.value);
				percentage2.value = maxProgress;
				setTimeout(() => {
					showSpinner(false);
				}, 500);
			}
		} catch (error) {
			console.error('Error fetching data:', error);
		}

		if (hasReached90Seconds && Math.floor(progress) === maxProgressDuringInterval) {
			percentage2.value = Math.floor(maxProgressDuringInterval);
		}
	}, intervalDuration);
};
const resetProgress = () => {
	percentage2.value = 0; // 重置进度条
	isValid.value = true; // 清除之前的有效状态
	showSpinner(true); // 显示加载
};

// 恢复配置时的进度条计时器
const restoreIntervalId = ref();

// 启动10秒进度条计时器（用于恢复配置）
const startRestoreProgressTimer = () => {
	if (restoreIntervalId.value) {
		clearInterval(restoreIntervalId.value); // 清除上一个定时器
	}

	percentage2.value = 0; // 重置进度条
	showSpinner(true); // 显示加载状态

	const intervalDuration = 100; // 100ms更新一次
	const progressDuration = 15 * 1000; // 15秒总时长
	const progressIncrement = 100 / (progressDuration / intervalDuration); // 每次增加的进度

	let progress = 0;
	const startTime = Date.now();

	restoreIntervalId.value = setInterval(() => {
		const elapsedTime = Date.now() - startTime;

		if (elapsedTime < progressDuration) {
			progress += progressIncrement;
			if (progress > 95) {
				// 最多到95%，等待真正完成
				progress = 95;
			}
			percentage2.value = Math.floor(progress);
		} else {
			// 10秒后如果还没完成，停在95%
			percentage2.value = 95;
		}
	}, intervalDuration);
};

// 立即完成进度（模型加载完成时调用）
const finishRestoreProgress = () => {
	if (restoreIntervalId.value) {
		clearInterval(restoreIntervalId.value);
		restoreIntervalId.value = null;
	}
	percentage2.value = 100; // 立即设为100%

	// 稍微延迟一下再隐藏，让用户看到100%
	setTimeout(() => {
		showSpinner(false);
	}, 300);
};
let scene, camera, renderer, controls;
//提交计算
const sumbitCompute = async () => {
	// 首先，彻底初始化/重置场景
	init();

	// 验证必要的输入文件和参数
	// 使用 footL.value 和 footF.value 进行文件存在性检查
	console.log('footL.value', footL.value);
	if (footL.value == null && footL.value == '') {
		ElMessage.error('请上传必要的气孔文件');
		return; // 如果文件不完整，则不继续
	}

	const resultUploadGH = await uploadGHFile(form.value);

	if (JSON.stringify(saveSettings.value) !== JSON.stringify(defaultSettings.value)) {
		settingsData.value.id = taskId.value;
		settingsData.value.settings = JSON.stringify(saveSettings.value);
		await uploadSettings(settingsData.value);
	} else {
		settingsData.value.id = taskId.value;
		settingsData.value.settings = JSON.stringify(defaultSettings.value);
		await uploadSettings(settingsData.value);
	}

	let rhinoFileString;
	const result2 = await getGHFileById(taskId.value);
	if (result2.data.rhinoFile && typeof result2.data.rhinoFile === 'string') {
		const rhinoFile = JSON.parse(result2.data.rhinoFile);
		if (rhinoFile.length > 1) {
			rhinoFileString = JSON.parse(rhinoFile);
		}
		if (rhinoFileString) {
			const result3 = await getDeleteById(taskId.value);
		}
	}

	console.log('提交计算');

	FootLastValue.value = footL.value;
	FunsionLastValue.value = footF.value;
	ShoeSizeValue.value = ShoeSize.value;
	FootSizeValue.value = FootSize.value;

	// 新增参数赋值
	EdgeSwitchValue.value = EdgeSwitch.value;
	EdgeWidthValue.value = EdgeWidth.value;
	UnitSizeValue.value = UnitSize.value;
	LatticeParamValue.value = LatticeParam.value;
	SpreadOptionValue.value = SpreadOption.value;
	LineTypeValue.value = LineType.value;
	ParameterTValue.value = ParameterT.value;
	LatticeTypeValue.value = LatticeType.value;
	RparamValue.value = Rparam.value;
	FunctionValue.value = Function.value;
	CrvCellValue.value = CrvCell.value;

	timing();
	isValid.value = false;
	// console.log('definitionName ', definitionName.value);
	await compute();
	console.log('结束计算');
};

const analysis = async (item) => {
	const status = item === 'lastDataAnalysis' ? lastAnalysisStatus : footAnalysisStatus;

	try {
		// 重置状态
		status.value = {
			loading: true,
			finished: false,
			success: false,
		};

		await loadURL(item);
		await computeAnalysis(item);

		// 检查分析结果
		if (item === 'lastDataAnalysis') {
			const hasResult =
				FootLastAnalysis.value.CockingForward ||
				FootLastAnalysis.value.LastLong ||
				FootLastAnalysis.value.LengthOfLast ||
				FootLastAnalysis.value.WidthOfLast ||
				FootLastAnalysis.value.SoleGith ||
				FootLastAnalysis.value.ShoeToeShape ||
				(FootLastAnalysis.value.PossibleShoeSize && Object.keys(FootLastAnalysis.value.PossibleShoeSize).length > 0);

			status.value.success = hasResult;
		} else {
			const hasResult = FootDataAnalysis.value.FootLength || FootDataAnalysis.value.FootWidth || FootDataAnalysis.value.ToeGirth;

			status.value.success = hasResult;
		}

		// 更新状态
		status.value.loading = false;
		status.value.finished = true;
	} catch (error) {
		console.error('Analysis failed:', error);
		status.value = {
			loading: false,
			finished: true,
			success: false,
		};
	}
};
let anaDoc;
let sceneAnay, cameraAnay, rendererAnay, controlsAnay;
async function computeAnalysis(item) {
	let footLast = footL.value;
	let footData = footF.value;
	let GGTJCS = ggtjcs.value;
	let param1, param2;
	if (item === 'lastDataAnalysis') {
		param1 = new RhinoCompute.Grasshopper.DataTree('FootLastFile');
		param1.append([0], [footLast]);
		param2 = new RhinoCompute.Grasshopper.DataTree('GGTJCS');
		param2.append([1], [GGTJCS]);
	} else {
		param1 = new RhinoCompute.Grasshopper.DataTree('FootFile');
		param1.append([0], [footData]);
	}
	let trees = [];
	trees.push(param1);
	if (item === 'lastDataAnalysis') {
		trees.push(param2);
	}

	let res;
	if (item === 'lastDataAnalysis') {
		res = await RhinoCompute.Grasshopper.evaluateDefinition(definitionFootL.value, trees);
		// console.log('我是收集前的数据', res);
		collectResultsFootLastAnalysis(res);
	} else {
		res = await RhinoCompute.Grasshopper.evaluateDefinition(definitionFootData.value, trees);
		// console.log('我是收集前的数据', res);
		collectResultsFootDataAnalysis(res);
	}
	// ElMessage.error('加载失败');
}
const loadedObjectsAna = ref([]);
const loadedObjectAna = ref(null); // 添加一个新的引用来存储组合对象

const collectResultsFootLastAnalysis = (responseJson) => {
	if (anaDoc !== undefined) anaDoc.delete();
	const values = responseJson.values;
	anaDoc = new rhino.File3dm();
	// 定义数据映射关系
	const dataMapping = {
		0: {
			path: 'CockingForward',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		1: {
			path: 'LastLong',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		2: {
			path: 'LengthOfLast',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		3: {
			path: 'WidthOfLast',
			treePath: '{0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		5: {
			path: 'SoleGith',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		6: {
			path: 'PostiveShoeLast',
			treePath: '{0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		7: {
			path: 'LastBottomSideLine',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		8: {
			path: 'SoleCirLine',
			treePath: '{0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		9: {
			path: 'ShoeToeShape',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
	};

	// 使用映射处理数据
	Object.entries(dataMapping).forEach(([index, config]) => {
		const value = values[index];
		const branch = values[index].InnerTree[config.treePath];
		if (value?.InnerTree) {
			// 首先尝试使用完整路径
			if (value.InnerTree[config.treePath]?.[0]?.data) {
				FootLastAnalysis.value[config.path] = value.InnerTree[config.treePath][0].data;
			}
			// 如果完整路径没有数据，尝试使用简单路径
			else if (value.InnerTree[config.simplePath]?.[0]?.data) {
				FootLastAnalysis.value[config.path] = value.InnerTree[config.simplePath][0].data;
			}
		}
		if (branch) {
			for (let j = 0; j < branch.length; j++) {
				// ...load rhino geometry into doc
				const rhinoObject = decodeItem(branch[j]);
				if (rhinoObject !== null) {
					anaDoc.objects().add(rhinoObject, null);
					modelData.value.push(anaDoc.toByteArray());
				}
			}
		}
	});

	// 特殊处理values[4]
	if (values[4]) {
		// 初始化一个对象来按路径存储鞋码值
		const shoeSizeGroups = {};

		// 遍历values[4]的InnerTree中的所有路径
		for (const path in values[4].InnerTree) {
			const dataArray = values[4].InnerTree[path];
			// 为每个路径创建一个数组
			shoeSizeGroups[path] = [];

			// 遍历该路径下的所有数据
			for (const item of dataArray) {
				if (item?.data !== undefined) {
					shoeSizeGroups[path].push(item.data);
				}
			}
		}

		// 将分组后的鞋码值存储到FootLastAnalysis中
		FootLastAnalysis.value.PossibleShoeSize = shoeSizeGroups;
	}
	// 调用渲染函数
	nextTick(() => {
		initAnay();
	});
};
async function initAnay() {
	// 检查容器是否存在
	const container = document.querySelector('.square');
	if (!container) {
		console.warn('找不到渲染容器');
		return;
	}

	// 清理旧的渲染器和场景
	if (rendererAnay) {
		rendererAnay.dispose();
	}
	if (sceneAnay) {
		sceneAnay.clear();
	}
	if (controlsAnay) {
		controlsAnay.dispose();
	}

	const buffer = new Uint8Array(anaDoc.toByteArray()).buffer;
	// set up loader for converting the results to threejs
	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');
	const API = {
		color: 0xffffff, // sRGB
		exposure: 1.0,
	};

	const manager = new THREE.LoadingManager(rendererAnay);
	const loaderEXR = new EXRLoader(manager);
	const matcap = loaderEXR.load('https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/040full.exr');

	const newScene = new THREE.Scene();

	// 创建相机
	const newCamera = new THREE.PerspectiveCamera(60, 250 / 250, 0.1, 1000);
	newCamera.position.set(15, -20, 5);

	// 创建渲染器
	const newRenderer = new THREE.WebGLRenderer({ antialias: true });
	newRenderer.setPixelRatio(window.devicePixelRatio);
	newRenderer.setSize(250, 250);
	container.innerHTML = ''; // 清空旧的渲染器
	container.appendChild(newRenderer.domElement);

	// 创建控件
	const newControls = new OrbitControls(newCamera, newRenderer.domElement);
	newControls.minDistance = 30;

	// 创建背景纹理
	const canvas = document.createElement('canvas');
	const canvasWidth = 250;
	const canvasHeight = 250;
	canvas.width = canvasWidth;
	canvas.height = canvasHeight;
	const context = canvas.getContext('2d');
	const color1 = '#d0d0d0';
	const color2 = '#ffffff';
	for (let x = 0; x < canvasWidth; x += 20) {
		for (let y = 0; y < canvasHeight; y += 20) {
			const color = (x + y) % 40 === 0 ? color2 : color1;
			context.fillStyle = color;
			context.fillRect(x, y, 20, 20);
		}
	}
	const texture = new THREE.CanvasTexture(canvas);
	newScene.background = texture;

	// 添加光源
	const directionalLight = new THREE.DirectionalLight(0xffffff, 2);
	directionalLight.target.position.set(0, 0, 0);
	newScene.add(directionalLight);

	const ambientLight = new THREE.AmbientLight();
	newScene.add(ambientLight);

	// load rhino doc into three.js scene
	loader.parse(
		buffer,
		function (object) {
			object.traverse((child) => {
				if (child.isMesh) {
					const meshMaterial = new THREE.MeshPhysicalMaterial({
						side: THREE.DoubleSide,
						roughness: 1,
						clearcoat: 1.0,
						clearcoatRoughness: 0.1,
						polygonOffset: true,
						polygonOffsetFactor: 1,
						polygonOffsetUnits: 1,
						color: 0xffffff, // 显式设置白色
					});
					child.material = meshMaterial;
				}
			});

			// 清除之前的对象
			loadedObjectsAna.value.forEach((obj) => newScene.remove(obj));
			loadedObjectsAna.value = [];

			// 创建一个组来包含所有对象
			const group = new THREE.Group();

			// 将所有对象添加到组中
			object.children.forEach((child) => {
				// 先处理所有网格对象
				if (child.isMesh) {
					loadedObjectsAna.value.push(child);
					group.add(child);
				}
			});

			// 获取所有线条，然后平均分成两组
			const allLineObjects = object.children.filter(
				(child) =>
					child.type === 'NurbsCurve' ||
					child.type === 'Curve' ||
					child.type === 'NurbsCurve3D' ||
					child.type === 'PolylineCurve' ||
					child.isLine ||
					child.type === 'Line'
			);

			// 分别处理两组线条
			allLineObjects.forEach((child, index) => {
				const points = [];
				if (child.geometry && child.geometry.attributes && child.geometry.attributes.position) {
					const positions = child.geometry.attributes.position.array;
					for (let i = 0; i < positions.length; i += 3) {
						points.push(new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]));
					}
				}

				if (points.length > 1) {
					const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);

					// 基于索引判断，奇数索引为红色虚线，偶数索引为黑色实线
					if (index % 2 === 0) {
						const lineMaterial = new THREE.LineDashedMaterial({
							color: 0xff0000,
							dashSize: 2,
							gapSize: 1,
							linewidth: 2,
						});
						const line = new THREE.Line(lineGeometry, lineMaterial);
						line.computeLineDistances(); // 必须计算线段距离以显示虚线
						group.add(line);
						loadedObjectsAna.value.push(line);
					} else {
						// 黑色实线
						const lineMaterial = new THREE.LineBasicMaterial({
							color: 0x000000,
							linewidth: 2,
						});
						const line = new THREE.Line(lineGeometry, lineMaterial);
						group.add(line);
						loadedObjectsAna.value.push(line);
					}
				}
			});

			// 保存组对象的引用
			loadedObjectAna.value = group;

			if (group.children.length > 0) {
				// 设置组的缩放
				group.scale.set(0.1, 0.1, 0.1);

				// 计算包围盒
				const boundingBox = new THREE.Box3().setFromObject(group);
				const size = new THREE.Vector3();
				boundingBox.getSize(size);

				const modelCenter = new THREE.Vector3();
				boundingBox.getCenter(modelCenter);
				newControls.target.copy(modelCenter);

				// 将组添加到场景
				newScene.add(group);

				// 设置渲染顺序，确保线条在最上层
				group.children.forEach((child, index) => {
					if (child instanceof THREE.Line) {
						child.renderOrder = index + 1000; // 线条渲染顺序靠后
					} else {
						child.renderOrder = index;
					}
				});
			} else {
				console.error('Failed to load model: No valid objects found');
			}
		},
		function (error) {
			console.error('An error occurred while loading the model:', error);
		}
	);

	// 替换旧的场景、相机、渲染器和控件
	sceneAnay = newScene;
	cameraAnay = newCamera;
	rendererAnay = newRenderer;
	controlsAnay = newControls;

	// 监听窗口大小调整
	window.addEventListener('resize', onWindowResizeAnay, false);

	animateAnay();
}

const collectResultsFootDataAnalysis = (responseJson) => {
	const values = responseJson.values;
	for (let i = 0; i < values.length; i++) {
		// ...iterate through data tree structure...
		for (const path in values[i].InnerTree) {
			if (values[0] && values[0].InnerTree['{0}']) {
				FootDataAnalysis.value.FootLength = values[0].InnerTree['{0}'][0]?.data;
			}
			if (values[1] && values[1].InnerTree['{0}']) {
				FootDataAnalysis.value.FootWidth = values[1].InnerTree['{0}'][0]?.data;
			}

			if (values[2] && values[2].InnerTree['{0}']) {
				FootDataAnalysis.value.ToeGirth = values[2].InnerTree['{0}'][0]?.data;
			}
		}
	}
	// console.log('我是分析的值FootDataAnalysis', FootDataAnalysis.value);
};
/**
 * Shows or hides the loading spinner
 */
const loader = ref(false);

function showSpinner(enable) {
	if (enable) {
		loader.value = true;
	} else {
		loader.value = false;
	}
}
const modelData = ref([]);
//compute方法区
//下载文件
function Alldownload(format = '3dm') {
	const zip = new JSZip(); // 创建一个新的压缩包

	// 创建一个数组来存储所有的 Promise
	const promises = [];

	// 添加所有文件到压缩包中，并将每个文件的 Promise 存储在 promises 数组中
	promises.push(addFileToZip(zip, '气孔.' + format, uploadedFile.value.Csdoc || Csdoc, format));

	// 等待所有文件都被添加到压缩包中
	Promise.all(promises)
		.then(() => {
			// 所有文件添加完毕后生成并下载 zip 文件
			zip
				.generateAsync({ type: 'blob' })
				.then(function (content) {
					const link = document.createElement('a');
					link.href = window.URL.createObjectURL(content);
					link.download = '脚转楦生成文件' + format + '.zip'; // 根据格式调整文件名
					link.click();
				})
				.catch((error) => {
					console.error('Error generating zip file:', error);
				});
		})
		.catch((error) => {
			console.error('Error adding files to zip:', error);
		});
}

function addFileToZip(zip, fileName, fileData, format = '3dm') {
	return new Promise((resolve, reject) => {
		if (fileData) {
			let buffer;
			if (format === 'stl') {
				// 获取 stl 格式数据
				if (typeof fileData === 'string') {
					// 如果 fileData 是 URL，使用 fetch 获取文件内容
					fetch(fileData)
						.then((response) => response.blob())
						.then((blob) => {
							convert3dmToStl(fileData, fileName)
								.then((stlBuffer) => {
									// 将转换后的 STL 数据添加到 zip 中
									let blob = new Blob([stlBuffer], { type: 'application/octet-stream' });
									zip.file(fileName.replace('.3dm', '.stl'), blob);
									resolve(); // 文件成功添加到 zip
								})
								.catch((error) => {
									console.error(`Error converting ${fileName} to STL:`, error);
									reject(error); // 转换错误时 reject
								});
						})
						.catch((error) => {
							console.error(`Error adding ${fileName} to zip:`, error);
							reject(error); // 出现错误时 reject
						});
				} else {
					// 如果 fileData 是字节数据
					buffer = fileData.toByteArray(); // 假设 toByteArray() 已定义
					zip.file(fileName, buffer);
					resolve(); // 文件成功添加到 zip
				}
			} else {
				if (typeof fileData === 'string') {
					// 如果 fileData 是 URL，使用 fetch 获取文件内容
					fetch(fileData)
						.then((response) => response.blob())
						.then((blob) => {
							zip.file(fileName, blob);
							resolve(); // 文件成功添加到 zip
						})
						.catch((error) => {
							console.error(`Error adding ${fileName} to zip:`, error);
							reject(error); // 出现错误时 reject
						});
				} else {
					// 如果 fileData 是字节数据
					buffer = fileData.toByteArray(); // 假设 toByteArray() 已定义
					zip.file(fileName, buffer);
					resolve(); // 文件成功添加到 zip
				}
			}
		} else {
			resolve(); // 如果没有文件数据也解决 Promise
		}
	});
}
async function Countstandownload(format = '3dm') {
	if (uploadedFile.value.Csdoc) {
		if (format === 'stl') {
			let blob = await convert3dmToStl(uploadedFile.value.Csdoc, '气孔.' + format);
			saveByteArray('气孔.' + format, blob);
		} else {
			downloadFile(uploadedFile.value.Csdoc, '气孔.' + format, format);
		}
	} else {
		let buffer;
		if (format === 'stl') {
			buffer = await convert3dmToStl(Csdoc, '气孔.' + format);
		} else {
			buffer = Csdoc.toByteArray();
		}
		saveByteArray('气孔.' + format, buffer);
	}
}

async function Countdownload(format = '3dm') {
	if (uploadedFile.value.Coudoc) {
		if (format === 'stl') {
			let blob = await convert3dmToStl(uploadedFile.value.Coudoc, '生成鞋垫.' + format);
			saveByteArray('生成鞋垫.' + format, blob);
		} else {
			downloadFile(uploadedFile.value.Coudoc, '生成鞋垫.' + format, format);
		}
	} else {
		let buffer;
		if (format === 'stl') {
			buffer = await convert3dmToStl(Coudoc, '生成鞋垫.' + format);
		} else {
			buffer = Coudoc.toByteArray();
		}
		saveByteArray('生成鞋垫.' + format, buffer);
	}
}

async function Standownload(format = '3dm') {
	if (uploadedFile.value.Standoc) {
		if (format === 'stl') {
			let blob = await convert3dmToStl(uploadedFile.value.Standoc, '变动云图.' + format);
			saveByteArray('变动云图.' + format, blob);
		} else {
			downloadFile(uploadedFile.value.Standoc, '变动云图.' + format, format);
		}
	} else {
		let buffer;
		if (format === 'stl') {
			buffer = await convert3dmToStl(Standoc, '变动云图.' + format);
		} else {
			buffer = Standoc.toByteArray();
		}
		saveByteArray('变动云图.' + format, buffer);
	}
}
async function Latticedownload(format = '3dm') {
	if (uploadedFile.value.Latticedoc) {
		if (format === 'stl') {
			let blob = await convert3dmToStl(uploadedFile.value.Latticedoc, '晶格.' + format);
			saveByteArray('晶格.' + format, blob);
		} else {
			downloadFile(uploadedFile.value.Latticedoc, '晶格.' + format, format);
		}
	} else {
		let buffer;
		if (format === 'stl') {
			buffer = await convert3dmToStl(Latticedoc, '晶格.' + format);
		} else {
			buffer = Latticedoc.toByteArray();
		}
		saveByteArray('晶格.' + format, buffer);
	}
}

async function convert3dmToStl(inputFilePath, filename) {
	const response = await fetch(inputFilePath);
	const arrayBuffer = await response.arrayBuffer();
	rhino = await rhino3dm();
	const file3dm = rhino.File3dm.fromByteArray(new Uint8Array(arrayBuffer));
	const file = file3dm;
	const meshes = [];

	// 收集所有网格
	const objects = file.objects();
	for (let i = 0; i < objects.count; i++) {
		const obj = objects.get(i);
		const geometry = obj.geometry();

		if (geometry instanceof rhino.Mesh) {
			meshes.push(geometry);
		} else if (geometry instanceof rhino.Surface) {
			const mesh = geometry.toMesh();
			meshes.push(mesh);
		} else if (geometry instanceof rhino.Curve) {
			const mesh = geometry.toNurbsSurface()?.toMesh();
			if (mesh) meshes.push(mesh);
		}
	}

	// 创建新的场景
	const scene = new THREE.Scene();

	// 处理每个网格
	meshes.forEach((mesh) => {
		try {
			// 创建 Three.js 几何体
			const geometry = new THREE.BufferGeometry();
			const vertices = [];
			const indices = [];

			// 获取顶点
			const vertexCount = mesh.vertices().count;
			for (let i = 0; i < vertexCount; i++) {
				const vertex = mesh.vertices().get(i);
				vertices.push(vertex[0], vertex[1], vertex[2]);
			}

			// 获取面
			const faceCount = mesh.faces().count;
			for (let i = 0; i < faceCount; i++) {
				const face = mesh.faces().get(i);
				if (face.length === 3) {
					// 三角形面
					indices.push(face[0], face[1], face[2]);
				} else if (face.length === 4) {
					// 四边形面，分解为两个三角形
					indices.push(face[0], face[1], face[2]);
					indices.push(face[0], face[2], face[3]);
				}
			}

			// 设置几何体属性
			geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
			geometry.setIndex(indices);
			geometry.computeVertexNormals();

			// 创建材质和网格
			const material = new THREE.MeshStandardMaterial({
				color: 0xcccccc,
				metalness: 0.5,
				roughness: 0.5,
			});
			const threeMesh = new THREE.Mesh(geometry, material);
			scene.add(threeMesh);
		} catch (error) {
			console.error('Error processing mesh:', error);
		}
	});

	// 确保场景中有对象
	if (scene.children.length === 0) {
		throw new Error('No valid meshes found to export');
	}

	// 导出为 STL
	const exporter = new STLExporter();
	const stlString = exporter.parse(scene, { binary: true });

	// 返回二进制 blob
	return new Blob([stlString], { type: 'application/octet-stream' });
}

function downloadFile(url, fileName, format) {
	fetch(url)
		.then((response) => response.blob())
		.then((blob) => {
			let link = document.createElement('a');
			link.href = window.URL.createObjectURL(blob);
			link.download = fileName; // 使用 format 作为文件后缀
			link.click();
		})
		.catch((error) => {
			console.error('Error downloading file:', error);
		});
}

function saveByteArray(fileName, byte) {
	let blob = new Blob([byte], { type: 'application/octet-stream' });
	let link = document.createElement('a');
	link.href = window.URL.createObjectURL(blob);
	link.download = fileName;
	link.click();
}

let rhino;
const definition = ref();
const definitionFootL = ref();
const definitionFootData = ref();
let doc, Csdoc, Coudoc, Standoc, Footdoc, FLdoc, FBdoc, Latticedoc;
const loadedObjects = ref([]);
rhino3dm()
	.then(async (m) => {
		console.log('Loaded rhino3dm.');
		rhino = m; // global

		init();

		RhinoCompute.url = 'http://************:80/';
		RhinoCompute.apiKey = 'c8cdc5f3d46143b664d72d039b5832fc';
		// const result = await doRhino();
		// if (result) {
		// 	console.log('============doRhino成功=========', result);
		// }
		// RhinoCompute.url = 'http://localhost:6500/';
		// RhinoCompute.apiKey = '';

		// 初始加载时获取资源
		await loadDefinitionResource();
	})
	.catch((error) => {
		console.error('Failed to load rhino3dm:', error);
		ElMessage.error('加载rhino3dm库失败，请刷新页面重试');
	});

// 添加一个函数来加载资源
const loadDefinitionResource = async () => {
	try {
		// 使用后端代理API获取生成方式文件，而不是直接fetch外部URL
		const response = await downloadPoreFile(definitionName.value);

		// 处理响应，判断是直接的ArrayBuffer还是包含data的响应对象
		let arrayBuffer;
		if (response instanceof ArrayBuffer) {
			// 直接是ArrayBuffer
			arrayBuffer = response;
		} else if (response.data instanceof ArrayBuffer) {
			// 是响应对象，data是ArrayBuffer
			arrayBuffer = response.data;
		} else {
			throw new Error('响应数据格式不正确，期望ArrayBuffer类型');
		}

		definition.value = new Uint8Array(arrayBuffer);
		if (arrayBuffer || definition.value) {
			console.log('计算之前-------');
			// init();
		} else {
			ElMessage.error('加载错误,Rhino计算服务未开启');
		}
	} catch (error) {
		console.error('加载资源出错:', error);
		ElMessage.error('加载资源失败，请检查网络连接');
	}
};

// 监听definitionName的变化
watch(definitionName, async (newValue, oldValue) => {
	if (newValue !== oldValue && newValue) {
		// console.log('生成方式已变更为:', newValue);
		// 更新selectedType的值，使其匹配当前选中的选项
		const optionIndex = deFinitOptions.findIndex((option) => option.value === newValue);
		if (optionIndex !== -1) {
			selectedType.value = optionIndex;
		}
		await loadDefinitionResource();
	}
});

async function loadURL(item) {
	try {
		RhinoCompute.url = 'http://************:80/';
		RhinoCompute.apiKey = 'c8cdc5f3d46143b664d72d039b5832fc';
		// RhinoCompute.url = 'http://localhost:6500/';
		// RhinoCompute.apiKey = '';
		let url;
		let response;
		if (item === 'lastDataAnalysis') {
			// url = lastDataAnalysis;
			response = await downloadAnalysisFile('last');
		} else {
			// url = footDataAnalysis;
			response = await downloadAnalysisFile('foot');
		}

		// let res = await fetch(url);
		// console.log('Fetch 结果:', res);

		// 使用后端代理API获取分析文件，而不是直接fetch外部URL

		// 处理响应，判断是直接的ArrayBuffer还是包含data的响应对象
		let arrayBuffer;
		if (response instanceof ArrayBuffer) {
			// 直接是ArrayBuffer
			arrayBuffer = response;
		} else if (response.data instanceof ArrayBuffer) {
			// 是响应对象，data是ArrayBuffer
			arrayBuffer = response.data;
		} else {
			throw new Error('响应数据格式不正确，期望ArrayBuffer类型');
		}

		// let buffer = await res.arrayBuffer();
		if (item === 'lastDataAnalysis') {
			definitionFootL.value = new Uint8Array(arrayBuffer);
		} else {
			definitionFootData.value = new Uint8Array(arrayBuffer);
		}

		// init();
	} catch (error) {
		console.error('加载模型时出错:', error);
		ElMessage.error('加载模型时出错，请检查网络连接和 URL 设置.');
	}
}

// 声明包含多个属性的响应式对象
const FootMeasurements = ref({
	ShoeSize: '',
	AdjustQuantity: '',
});

const FootLastAnalysis = ref({
	CockingForward: '',
	LastLong: '',
	LengthOfLast: '',
	WidthOfLast: '',
	SoleGith: '',
	PossibleShoeSize: '',
	ShoeToeShape: '',
});

const FootDataAnalysis = ref({
	FootLength: '',
	FootWidth: '',
	ToeGirth: '',
});

const FootMeasure = computed(() => ({
	ShoeSize: FootMeasurements.value.ShoeSize,
	AdjustQuantity: FootMeasurements.value.AdjustQuantity,
}));

const FootMeasureForm = computed(() => ({
	id: taskId.value,
	computeResult: JSON.stringify(FootMeasure.value),
}));
//计算方法
async function compute() {
	// 参数验证和清理
	let insoles = FootLastValue.value;
	let stanShoe = ShoeSizeValue.value;
	let codeAdjust = FootSizeValue.value;
	let footShape = FunsionLastValue.value;
	let Switch = true;

	// 验证必要参数
	if (!insoles || !footShape) {
		ElMessage.error('缺少必要的文件参数');
		return;
	}

	// 确保数值参数有有效值
	const edgeSwitch = EdgeSwitchValue.value ?? true;
	const edgeWidth = EdgeWidthValue.value ?? 0.234;
	const unitSize = UnitSizeValue.value ?? 0.125;
	const latticeParam = LatticeParamValue.value ?? 1;
	const spreadOption = SpreadOptionValue.value ?? 2;
	const lineType = LineTypeValue.value ?? 0;
	const parameterT = ParameterTValue.value ?? 0.5;
	const latticeType = LatticeTypeValue.value ?? 1;
	const rparam = RparamValue.value ?? 0.4;
	const functionValue = FunctionValue.value ?? 0;
	const crvCell = CrvCellValue.value ?? '';

	// 验证线晶格库参数（必选）
	if (!crvCell || crvCell.trim() === '') {
		ElMessage.error('请选择线晶格库');
		showSpinner(false);
		isValid.value = true;
		return;
	}

	try {
		let param1 = new RhinoCompute.Grasshopper.DataTree('StanShoe');
		param1.append([0], [stanShoe]);
		let param2 = new RhinoCompute.Grasshopper.DataTree('Insoles');
		param2.append([1], [insoles]);
		let param3 = new RhinoCompute.Grasshopper.DataTree('CodeAdjust');
		param3.append([2], [codeAdjust]);
		let param4 = new RhinoCompute.Grasshopper.DataTree('LatticeParam');
		param4.append([3], [latticeParam]);
		let param5 = new RhinoCompute.Grasshopper.DataTree('EdgeWidth');
		param5.append([4], [edgeWidth]);
		let param6 = new RhinoCompute.Grasshopper.DataTree('UnitSize');
		param6.append([5], [unitSize]);
		let param7 = new RhinoCompute.Grasshopper.DataTree('Rparam');
		param7.append([6], [rparam]);
		let param8 = new RhinoCompute.Grasshopper.DataTree('Switch');
		param8.append([7], [Switch]);

		// Add all params to an array
		let trees = [];
		trees.push(param1);
		trees.push(param2);
		trees.push(param3);
		trees.push(param4);
		trees.push(param5);
		trees.push(param6);
		trees.push(param7);
		trees.push(param8);

		// Call RhinoCompute
		const res = await RhinoCompute.Grasshopper.evaluateDefinition(definition.value, trees);

		// console.log("我是返回res.values[0].InnerTree['{}']", Object.keys(res.values[0].InnerTree).length === 0 ? 1 : 0);
		if (res && res.values && res.values[0] && Object.keys(res.values[0].InnerTree).length > 0) {
			console.log('我是收集前的数据', res);
			collectResults(res);
		} else {
			if (intervalId.value) {
				clearInterval(intervalId.value); // 清除定时器
			}
			showSpinner(false);
			isValid.value = true;
			ElMessage.error('计算结果为空或格式错误');
		}
	} catch (error) {
		console.error('RhinoCompute调用失败:', error);
		if (intervalId.value) {
			clearInterval(intervalId.value);
		}
		showSpinner(false);
		isValid.value = true;
		ElMessage.error('计算服务调用失败，请检查参数设置和网络连接');
		return;
	}
}
const loadedObject = ref();
/**
 * Parse response
 */
function collectResults(responseJson) {
	// clear doc
	if (doc !== undefined) doc.delete();

	const values = responseJson.values;

	doc = new rhino.File3dm();
	Csdoc = new rhino.File3dm();
	// Coudoc = new rhino.File3dm();
	// Standoc = new rhino.File3dm();
	// Latticedoc = new rhino.File3dm();
	// Footdoc = new rhino.File3dm();
	// FLdoc = new rhino.File3dm();
	// FBdoc = new rhino.File3dm();

	// 在处理新对象之前，清空已加载对象的数组和相关UI
	loadedObjects.value = [];
	const objectControls = document.getElementById('objectControls');
	if (objectControls) {
		objectControls.innerHTML = ''; // 清空旧的控件
	}

	// for each output (RH_OUT:*)...
	for (let i = 0; i < values.length; i++) {
		// ...iterate through data tree structure...
		for (const path in values[i].InnerTree) {
			const branch = values[i].InnerTree[path];
			// console.log('branch值-----', branch);
			// if (values[0] && values[0].InnerTree['{0;0;0}']) {
			// 	FootMeasurements.value.ShoeSize = values[0].InnerTree['{0;0;0}'][0]?.data;
			// } else {
			// 	FootMeasurements.value.ShoeSize = '7';
			// }

			// if (values[1] && values[1].InnerTree['{0}']) {
			// 	FootMeasurements.value.AdjustQuantity = values[1].InnerTree['{0}'][0]?.data;
			// } else {
			// 	FootMeasurements.value.AdjustQuantity = '0.770999';
			// }

			for (let j = 0; j < branch.length; j++) {
				const rhinoObject = decodeItem(branch[j]);
				if (rhinoObject !== null) {
					doc.objects().add(rhinoObject, null);
					//console.log('rhinoObject==', rhinoObject, i);
					modelData.value.push(doc.toByteArray());
					doc.objects().delete(rhinoObject); // Delete from temp doc after getting bytes
					if (i === 1) {
						Csdoc.objects().add(rhinoObject, null);
					}
					// if (i === 3) {
					// 	Coudoc.objects().add(rhinoObject, null);
					// }
					// if (i === 4) {
					// 	Standoc.objects().add(rhinoObject, null);
					// }
					// // 添加Lattice mesh处理（应该在索引5，如果不对请调整）
					// if (i === 5) {
					// 	Latticedoc.objects().add(rhinoObject, null);
					// }
				}
			}
		}
	}
	computeResult();
	docUpload();

	if (doc.objects().count < 1) {
		console.error('No rhino objects to load!');
		showSpinner(false);
		isValid.value = true;
		return;
	}

	const buffer = new Uint8Array(doc.toByteArray()).buffer;
	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');

	const API = {
		color: 0xffffff,
		exposure: 1.0,
	};

	const manager = new THREE.LoadingManager(renderer);
	const loaderEXR = new EXRLoader(manager);
	const matcap = loaderEXR.load('https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/040full.exr');

	loader.parse(
		buffer,
		function (object) {
			object.traverse((child) => {
				if (child.isMesh) {
					// 获取当前mesh在父对象中的索引
					const meshIndex = child.parent ? Array.from(child.parent.children).indexOf(child) : -1;

					// 第三个模型（索引为2，对应"变动云图"）不应用材质
					// if (meshIndex !== 2) {
					const skinMaterial = new THREE.MeshMatcapMaterial({
						color: new THREE.Color().setHex(API.color).convertSRGBToLinear(),
						matcap: matcap,
						side: THREE.DoubleSide,
					});
					child.material = skinMaterial;
					// }
				} else if (child.isLine) {
					const lineIndex = child.parent ? Array.from(child.parent.children).indexOf(child) : -1;
					let color;
					let linewidth;
					if (lineIndex === 8 || lineIndex === 9) {
						color = new THREE.Color(0xffd700);
						linewidth = 10;
					} else if (lineIndex === 10 || lineIndex === 11) {
						color = new THREE.Color(0x800080);
						linewidth = 10;
					} else {
						color = new THREE.Color(0xff0000);
						linewidth = 3;
					}
					const material = new THREE.LineBasicMaterial({
						color: color,
						linewidth: linewidth,
						transparent: true,
						opacity: 1.0,
					});
					child.material = material;
				}
			}); // Removed the incorrect second argument 'false'

			object.children.forEach((child, index) => {
				if (child.isMesh) {
					loadedObjects.value.push(child);
					// scene.add(child); // Add the main object group once, not individual children here
				}
			});

			loadedObject.value = object;

			scene.add(object);
			generateObjectControls();
			zoomCameraToSelection(camera, controls, object.children);

			showSpinner(false);
			isValid.value = true;
			resultBotton.value = true;
		},
		function (error) {
			console.error('An error occurred while loading the model:', error);
			showSpinner(false);
			isValid.value = true; // Allow re-submission on error
		}
	);
}

const computeResult = async () => {
	if (FootMeasurements.value.ShoeSize && FootMeasurements.value.AdjustQuantity) {
		const result = await uploadCompute(FootMeasureForm.value);
		if (result.data) {
			console.log('结果上传成功');
		} else {
			console.log('结果上传失败');
		}
	}
};

const uploadedFileUrls = ref([]);

const docUpload = async () => {
	try {
		// 定义需要上传的文件数组和对应的文件名
		const filesToUpload = [
			{ doc: doc.toByteArray(), fileName: 'doc.3dm' },
			{ doc: Csdoc.toByteArray(), fileName: 'Csdoc.3dm' },
			// { doc: Coudoc.toByteArray(), fileName: 'Coudoc.3dm' },
			// { doc: Standoc.toByteArray(), fileName: 'Standoc.3dm' },
			// { doc: Latticedoc.toByteArray(), fileName: 'Latticedoc.3dm' },
		];

		// 循环处理每个文件
		for (let fileData of filesToUpload) {
			let blob = new Blob([fileData.doc], { type: 'application/octet-stream' });
			const formData = new FormData();
			// formData.append('id', taskId.value);
			formData.append('mouldFile', blob, fileData.fileName);
			// 上传文件并获取结果
			const result = await uploadStl(formData);

			// 如果上传成功，将文件地址添加到全局变量数组
			if (result.data) {
				uploadedFileUrls.value.push(result.data);
				// console.log(`${fileData.fileName} 上传成功`);
			} else {
				// console.log(`${fileData.fileName} 上传失败`);
			}
		}
		// 在此处可以进行上传完成后的逻辑处理，例如更新界面或其他操作
		console.log('所有文件上传完成');
		// console.log('上传成功的文件地址数组:', uploadedFileUrls.value);
		uploadedFile.value.doc = uploadedFileUrls.value[0];
		uploadedFile.value.Csdoc = uploadedFileUrls.value[1];
		// uploadedFile.value.Coudoc = uploadedFileUrls.value[2];
		// uploadedFile.value.Standoc = uploadedFileUrls.value[3];
		// uploadedFile.value.Latticedoc = uploadedFileUrls.value[4];
		uploadedFileUrls.value = [];
		Rhino.value.id = taskId.value;
		Rhino.value.rhinoFile = JSON.stringify(uploadedFile.value);

		const resultR = await uploadAddress(Rhino.value);
		if (resultR) {
			console.log('文件上传成功！！！');
		} else {
			console.log('文件上传失败！！！');
		}
	} catch (error) {
		console.error('上传文件发生错误:', error);
	}
};
const selectedType = ref(0);

function generateObjectControls() {
	const objectControls = document.getElementById('objectControls');
	objectControls.innerHTML = ''; // 清空之前的内容
	objectControls.style.display = 'flex'; // 设置为flex布局
	objectControls.style.flexDirection = 'column'; // 垂直列显示
	objectControls.style.alignItems = 'center'; // 上下居中
	objectControls.style.justifyContent = 'center'; // 左右居中
	objectControls.style.background = 'rgba(255, 255, 255, 0.3)'; // 设置背景色为半透明白色
	objectControls.style.position = 'absolute';
	objectControls.style.left = '570px';
	objectControls.style.top = '80px';
	objectControls.style.width = '220px';
	objectControls.style.height = '260px';
	objectControls.style.borderRadius = '10px';

	// 每个分组内的子标签名称
	const labels = ['气孔'];

	// 遍历默认设置，初始化每个对象
	loadedObjects.value.forEach((obj, index) => {
		const controlContainer = document.createElement('div');
		controlContainer.style.display = 'flex';
		controlContainer.style.position = 'relative';
		controlContainer.style.marginBottom = '10px';

		const label = document.createElement('div');
		label.style.color = '#7472F1';
		label.style.marginRight = '100px'; // 调整标签文字与复选框之间的距离
		label.style.marginBottom = '-50px';
		label.style.bottom = '-15px';
		label.appendChild(document.createTextNode(labels[index]));
		controlContainer.appendChild(label);
		objectControls.appendChild(controlContainer);

		// 获取默认设置
		let defaultCheckboxState = defaultSettings.value[`checkbox_${index}`]; // 获取默认的复选框状态

		// 获取保存的设置值
		const savedCheckboxState = saveSettings.value[`checkbox_${index}`];
		const savedColor = saveSettings.value[`color_${index}`];
		const savedOpacity = saveSettings.value[`opacity_${index}`];

		// 最终使用的复选框状态、颜色、透明度（优先使用保存的设置）
		let finalCheckboxState = savedCheckboxState !== undefined ? savedCheckboxState : defaultCheckboxState;

		const finalColor = savedColor || defaultSettings.value[`color_${index}`];
		const finalOpacity = savedOpacity || defaultSettings.value[`opacity_${index}`];

		// 创建复选框并设置状态
		const checkbox = document.createElement('input');
		checkbox.type = 'checkbox';
		checkbox.checked = finalCheckboxState;
		checkbox.id = 'checkbox_' + index;

		// 创建用于显示自定义复选框状态的图像
		const checkboxImage = document.createElement('img');
		checkboxImage.src = checkbox.checked
			? 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/open_eyes.png'
			: 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/close_eyes.png'; // 根据复选框状态显示图像
		checkboxImage.style.cursor = 'pointer'; // 鼠标悬停时显示手型
		checkboxImage.style.marginLeft = '180px';
		checkboxImage.style.marginBottom = '30px';
		checkboxImage.style.width = '16px';
		checkboxImage.style.height = '10px';

		// 添加点击事件处理程序
		checkboxImage.onclick = () => {
			checkbox.checked = !checkbox.checked; // 切换复选框的状态
			checkboxImage.src = checkbox.checked
				? 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/open_eyes.png'
				: 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/close_eyes.png'; // 根据新状态更新图像
			toggleObjectVisibility(index, checkbox.checked);
			saveSetting('checkbox_' + index, checkbox.checked); // 保存复选框状态
		};

		objectControls.appendChild(checkboxImage);
		toggleObjectVisibility(index, checkbox.checked);

		// 恢复颜色设置并更新模型
		const colorInputContainer = document.createElement('div');
		colorInputContainer.style.display = 'flex';
		colorInputContainer.style.alignItems = 'center';
		colorInputContainer.style.marginLeft = '90px';
		colorInputContainer.style.marginTop = '-50px';
		colorInputContainer.style.justifyContent = 'center'; // 水平居中

		const colorInput = document.createElement('input');
		colorInput.type = 'color';
		colorInput.value = finalColor; // 使用默认颜色
		colorInput.oninput = (e) => {
			const target = e.target;
			setObjectColor(index, target.value); // 更新模型颜色
			saveSetting('color_' + index, target.value); // 保存颜色设置
		};

		colorInputContainer.appendChild(colorInput);
		objectControls.appendChild(colorInputContainer);
		setObjectColor(index, finalColor); // 应用默认颜色到模型

		// 恢复透明度设置并更新模型
		const opacityInput = document.createElement('input');
		opacityInput.type = 'range';
		opacityInput.min = 0;
		opacityInput.max = 1;
		opacityInput.step = 0.1;
		opacityInput.value = finalOpacity; // 使用默认透明度
		opacityInput.style.background = '#7472F1'; // 修改进度条颜色
		opacityInput.style.border = 'none'; // 去除进度条边框
		opacityInput.style.height = '5px'; // 设置进度条高度
		opacityInput.style.marginTop = '5px';
		opacityInput.style.marginBottom = '5px';
		opacityInput.oninput = (e) => {
			const target = e.target;
			setObjectOpacity(index, parseFloat(target.value)); // 更新模型透明度
			saveSetting('opacity_' + index, target.value); // 保存透明度设置
		};

		objectControls.appendChild(opacityInput);
		setObjectOpacity(index, finalOpacity); // 应用默认透明度到模型
	});
}

function toggleObjectVisibility(index, isVisible) {
	if (loadedObjects.value[index]) {
		loadedObjects.value[index].visible = isVisible;
	}
}

function setObjectColor(index, color) {
	if (loadedObjects.value[index]) {
		loadedObjects.value[index].material.color.set(color); // 更新材质颜色
	}
}

function setObjectOpacity(index, opacity) {
	if (loadedObjects.value[index]) {
		loadedObjects.value[index].material.opacity = opacity;
		loadedObjects.value[index].material.transparent = opacity < 1;
		loadedObjects.value[index].material.needsUpdate = true; // 更新材质
	}
}

function getStoredSetting(key: keyof typeof saveSettings.value, defaultValue: any): any {
	const value = saveSettings.value[key];
	return value !== undefined ? value : defaultValue;
}

async function saveSetting(key: keyof typeof saveSettings.value, value: any): Promise<void> {
	saveSettings.value[key] = value;
	settingsData.value.id = taskId.value;
	settingsData.value.settings = JSON.stringify(saveSettings.value);

	try {
		// 假设 uploadSettings 返回一个 Promise
		const result = await uploadSettings(settingsData.value);
		if (result.data) {
		}
	} catch (error) {
		console.error('保存设置失败', error);
	}
}

/**
 * Attempt to decode data tree item to rhino geometry
 */
function decodeItem(item) {
	const data = JSON.parse(item.data);
	if (item.type === 'System.String') {
		try {
			return rhino.DracoCompression.decompressBase64String(data);
		} catch {}
	} else if (typeof data === 'object') {
		const decoded = rhino.CommonObject.decode(data);
		// 如果是曲线对象，确保它被正确解码
		if (decoded && decoded.objectType === 'Curve') {
			return decoded;
		}
		return decoded;
	}
	return null;
}
const backgroundTexture = ref(null);

function createBackground(width: number, height: number) {
	// if (backgroundTexture.value) { // Always create a new background on init
	// 	return backgroundTexture.value;
	// }

	const canvas = document.createElement('canvas');
	canvas.width = width;
	canvas.height = height;
	const context = canvas.getContext('2d');
	const gradient = context.createLinearGradient(0, 0, 0, canvas.height);
	gradient.addColorStop(0, '#cdbaed');
	gradient.addColorStop(1, '#e3e9ee');
	context.fillStyle = gradient;
	context.fillRect(0, 0, canvas.width, canvas.height);

	backgroundTexture.value = new THREE.CanvasTexture(canvas);
	return backgroundTexture.value;
}

//加载3dm
function init() {
	console.log('初始化场景');

	// 彻底清理旧的渲染器和场景
	const container = document.querySelector('.active-main');
	if (renderer && renderer.domElement) {
		if (container && container.contains(renderer.domElement)) {
			container.removeChild(renderer.domElement);
		}
		renderer.dispose();
		renderer = null;
	}
	if (scene) {
		scene.traverse((object) => {
			if (object.isMesh) {
				object.geometry?.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach((material) => material.dispose());
				} else {
					object.material?.dispose();
				}
			}
		});
		scene.clear();
		scene = null;
	}
	if (controls) {
		controls.dispose();
		controls = null;
	}
	// camera is not explicitly set to null here, as it's recreated.
	// However, ensure any event listeners or other references are cleared if necessary.
	camera = null; // Explicitly nullify camera

	loadedObjects.value = []; // 清空已加载对象数组
	if (backgroundTexture.value) {
		backgroundTexture.value.dispose(); // Dispose old texture
		backgroundTexture.value = null; // 清除背景纹理缓存
	}

	THREE.Object3D.DEFAULT_UP = new THREE.Vector3(0, 0, 1);

	scene = new THREE.Scene();
	const width = innerWidth;
	const height = innerHeight;
	scene.background = createBackground(width, height);

	camera = new THREE.PerspectiveCamera(70, width / height, 1, 1000);
	camera.position.set(0, -200, 30);

	renderer = new THREE.WebGLRenderer({ antialias: true });
	renderer.setPixelRatio(window.devicePixelRatio);
	renderer.setSize(width, height);
	if (container) {
		// Ensure container exists before appending
		container.appendChild(renderer.domElement);
	}

	controls = new OrbitControls(camera, renderer.domElement);
	controls.enablePan = false;

	const directionalLight = new THREE.DirectionalLight(0xffffff);
	directionalLight.intensity = 1;
	scene.add(directionalLight);

	const ambientLight = new THREE.AmbientLight();
	scene.add(ambientLight);

	// Remove existing listener before adding a new one to prevent duplicates
	window.removeEventListener('resize', onWindowResize);
	window.addEventListener('resize', onWindowResize, false);
	isAnimating.value = true;
	animate();
}
//调整位置
//俯视效果
function switchToTopView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(100, -300, 3500);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

//仰视效果
function switchToBottomView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(100, -300, -3500);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

//正面效果
function switchToFrontView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(200, -1000, -50);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

//左侧面效果
function switchToLeftSideView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			// camera.position.set(100, 1000, 0);
			camera.position.set(-100, 0, 50);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}
//右侧面效果
function switchToSideView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(2000, 0, 200);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}
//背面效果
function switchToBackView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(100, 1000, 0);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

function onWindowResize() {
	camera.aspect = window.innerWidth / window.innerHeight;
	camera.updateProjectionMatrix();
	renderer.setSize(window.innerWidth, window.innerHeight);
	animate();
}

// 添加状态控制
const isAnimating = ref(true);

function animate() {
	if (!isAnimating.value) return;

	if (renderer && scene && camera) {
		requestAnimationFrame(animate);
		controls?.update();
		renderer.render(scene, camera);
	}
}

function stopAnimation() {
	isAnimating.value = false;
}

/**
 * Helper function that behaves like rhino's "zoom to selection", but for three.js!
 */
function zoomCameraToSelection(camera, controls, selection, fitOffset = 1.2) {
	const box = new THREE.Box3();

	for (const object of selection) {
		if (object.isLight) continue;
		box.expandByObject(object);
	}

	const size = box.getSize(new THREE.Vector3());
	const center = box.getCenter(new THREE.Vector3());

	const maxSize = Math.max(size.x, size.y, size.z);
	const fitHeightDistance = maxSize / (2 * Math.atan((Math.PI * camera.fov) / 360));
	const fitWidthDistance = fitHeightDistance / camera.aspect;
	const distance = fitOffset * Math.max(fitHeightDistance, fitWidthDistance);

	const minDistance = Math.max(50, distance * 0.6); // 最小缩放距离（保证一定的可视距离）修改0.6可改变缩放的最大值，值越大缩放的最大值越小
	const maxDistance = Math.max(200, distance * 2); // 最大缩放距离（保证不会太远）
	controls.minDistance = minDistance;
	controls.maxDistance = maxDistance;

	// 更新相机位置
	const direction = controls.target.clone().sub(camera.position).normalize().multiplyScalar(distance);
	// controls.minDistance = 200;
	// controls.MaxDistance = 200;
	controls.target.copy(center);

	camera.near = 0.1;
	camera.far = 1000;
	camera.updateProjectionMatrix();
	camera.position.copy(controls.target).sub(direction);

	// 更新控制器
	controls.update();
}
//加载3dm文件
async function initRhino() {
	console.log('开始渲染3dm');
	timing();
	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');
	// loader.load(rhinoAddress.value, function (object) {
	const API = {
		color: 0xffffff, // sRGB
		exposure: 1.0,
	};

	const manager = new THREE.LoadingManager(renderer);
	const loaderEXR = new EXRLoader(manager);
	const matcap = loaderEXR.load('https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/040full.exr');
	// load rhino doc into three.js scene
	loader.load(rhinoAddress.value, function (object) {
		object.traverse((child) => {
			if (child.isMesh) {
				// 获取当前mesh在父对象中的索引
				const meshIndex = child.parent ? Array.from(child.parent.children).indexOf(child) : -1;

				// 第三个模型（索引为2，对应"变动云图"）不应用材质
				// if (meshIndex !== 2) {
				const skinMaterial = new THREE.MeshMatcapMaterial({
					color: new THREE.Color().setHex(API.color).convertSRGBToLinear(),
					matcap: matcap,
					side: THREE.DoubleSide,
				});
				const materialstlMesh = new THREE.MeshPhysicalMaterial({
					side: THREE.DoubleSide,
					// map: texture,
					roughness: 1, //表面粗糙度
					clearcoat: 1.0,
					clearcoatRoughness: 0.1,
					polygonOffset: true,
					polygonOffsetFactor: 1, // 调整描边模型的深度
					polygonOffsetUnits: 1,
				});

				child.material = skinMaterial;
				// }
			} else if (child.isLine) {
				// 获取线条索引
				const lineIndex = child.parent ? Array.from(child.parent.children).indexOf(child) : -1;

				let color;
				let linewidth;
				if (lineIndex === 8 || lineIndex === 9) {
					color = new THREE.Color(0xffd700); // 黄色
					linewidth = 10;
				} else if (lineIndex === 10 || lineIndex === 11) {
					color = new THREE.Color(0x800080); // 紫色
					linewidth = 10;
				} else {
					color = new THREE.Color(0xff0000); // 默认红色
					linewidth = 3;
				}

				// 直接修改现有线条材质
				const material = new THREE.LineBasicMaterial({
					color: color,
					linewidth: linewidth,
					transparent: true,
					opacity: 1.0,
				});

				child.material = material;
			}
		}, false);

		loadedObjects.value.forEach((obj) => scene.remove(obj));
		loadedObjects.value = [];

		object.children.forEach((child, index) => {
			if (child.isMesh) {
				loadedObjects.value.push(child);
				// scene.add(child);
			}
		});

		loadedObject.value = object;

		// zoom to extents
		zoomCameraToSelection(camera, controls, object.children);

		// 生成控制对象显示的复选框和材质控制
		generateObjectControls();

		// add object graph from rhino model to three.js scene
		scene.add(object);
		showSpinner(false);
	});
}
//加载3dm文件 - 专门用于从保存配置恢复时，显示10秒进度条
async function initRhinoFromSavedConfig() {
	console.log('从保存配置恢复3dm模型');

	// 启动10秒进度条（包含加载状态）
	startRestoreProgressTimer();

	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');

	const API = {
		color: 0xffffff, // sRGB
		exposure: 1.0,
	};

	const manager = new THREE.LoadingManager(renderer);
	const loaderEXR = new EXRLoader(manager);
	const matcap = loaderEXR.load('https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/040full.exr');

	// load rhino doc into three.js scene
	loader.load(rhinoAddress.value, function (object) {
		object.traverse((child) => {
			if (child.isMesh) {
				// 获取当前mesh在父对象中的索引
				const meshIndex = child.parent ? Array.from(child.parent.children).indexOf(child) : -1;

				// 第三个模型（索引为2，对应"变动云图"）不应用材质
				// if (meshIndex !== 2) {
				const skinMaterial = new THREE.MeshMatcapMaterial({
					color: new THREE.Color().setHex(API.color).convertSRGBToLinear(),
					matcap: matcap,
					side: THREE.DoubleSide,
				});
				const materialstlMesh = new THREE.MeshPhysicalMaterial({
					side: THREE.DoubleSide,
					// map: texture,
					roughness: 1, //表面粗糙度
					clearcoat: 1.0,
					clearcoatRoughness: 0.1,
					polygonOffset: true,
					polygonOffsetFactor: 1, // 调整描边模型的深度
					polygonOffsetUnits: 1,
				});

				child.material = skinMaterial;
				// }
			} else if (child.isLine) {
				// 获取线条索引
				const lineIndex = child.parent ? Array.from(child.parent.children).indexOf(child) : -1;

				let color;
				let linewidth;
				if (lineIndex === 8 || lineIndex === 9) {
					color = new THREE.Color(0xffd700); // 黄色
					linewidth = 10;
				} else if (lineIndex === 10 || lineIndex === 11) {
					color = new THREE.Color(0x800080); // 紫色
					linewidth = 10;
				} else {
					color = new THREE.Color(0xff0000); // 默认红色
					linewidth = 3;
				}

				// 直接修改现有线条材质
				const material = new THREE.LineBasicMaterial({
					color: color,
					linewidth: linewidth,
					transparent: true,
					opacity: 1.0,
				});

				child.material = material;
			}
		}, false);

		loadedObjects.value.forEach((obj) => scene.remove(obj));
		loadedObjects.value = [];

		object.children.forEach((child, index) => {
			if (child.isMesh) {
				loadedObjects.value.push(child);
				// scene.add(child);
			}
		});

		loadedObject.value = object;

		// zoom to extents
		zoomCameraToSelection(camera, controls, object.children);

		// 生成控制对象显示的复选框和材质控制
		generateObjectControls();

		// add object graph from rhino model to three.js scene
		scene.add(object);

		// 模型真正渲染完成后立即完成进度并隐藏加载状态
		finishRestoreProgress();
		console.log('保存配置的3dm模型加载完成');
	});
}

//加载stl文件
let sceneStl, cameraStl, rendererStl, controlsStl;

async function initStl(fileMode, colorFlage) {
	console.log('开始渲染stl');

	// 创建新场景
	const newScene = new THREE.Scene();

	// 创建相机
	const newCamera = new THREE.PerspectiveCamera(60, 250 / 250, 0.1, 1000);
	newCamera.position.set(15, -20, 5);

	// 创建渲染器
	const newRenderer = new THREE.WebGLRenderer({ antialias: true });
	newRenderer.setPixelRatio(window.devicePixelRatio);
	newRenderer.setSize(250, 250);
	const container = document.querySelector('.square');
	container.innerHTML = ''; // 清空旧的渲染器
	container.appendChild(newRenderer.domElement);

	// 创建控件
	const newControls = new OrbitControls(newCamera, newRenderer.domElement);
	newControls.minDistance = 30;

	// 创建背景纹理
	const canvas = document.createElement('canvas');
	const canvasWidth = 250;
	const canvasHeight = 250;
	canvas.width = canvasWidth;
	canvas.height = canvasHeight;
	const context = canvas.getContext('2d');
	const color1 = '#d0d0d0';
	const color2 = '#ffffff';
	for (let x = 0; x < canvasWidth; x += 20) {
		for (let y = 0; y < canvasHeight; y += 20) {
			const color = (x + y) % 40 === 0 ? color2 : color1;
			context.fillStyle = color;
			context.fillRect(x, y, 20, 20);
		}
	}
	const texture = new THREE.CanvasTexture(canvas);
	newScene.background = texture;

	// 添加光源
	const directionalLight = new THREE.DirectionalLight(0xffffff, 2);
	directionalLight.target.position.set(0, 0, 0);
	newScene.add(directionalLight);

	const ambientLight = new THREE.AmbientLight();
	newScene.add(ambientLight);

	// 加载模型
	let loader = null;
	if (fileMode === 'stl') {
		loader = new STLLoader();
	} else if (fileMode === 'obj') {
		loader = new OBJLoader();
	} else if (fileMode === 'mtl') {
		loader = new MTLLoader();
	} else if (fileMode === 'stp') {
		loader = new STLLoader();
	} else if (fileMode === '3dm') {
		loader = new Rhino3dmLoader();
		loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');
	}

	if (loader === null) {
		ElMessage.error('没有找到匹配的加载器');
	} else {
		loader.load(
			stlAddress.value,
			function (object) {
				let materialstlMesh;
				if (colorFlage == 1) {
					materialstlMesh = new THREE.MeshNormalMaterial({ wireframe: true });
				} else {
					materialstlMesh = new THREE.MeshPhysicalMaterial({
						side: THREE.DoubleSide,
						roughness: 1,
						clearcoat: 1.0,
						clearcoatRoughness: 0.1,
						polygonOffset: true,
						polygonOffsetFactor: 1,
						polygonOffsetUnits: 1,
					});
				}

				let mesh = null;
				if (fileMode === 'stl' || fileMode === 'stp') {
					mesh = new THREE.Mesh(object, materialstlMesh);
				} else if (fileMode === 'obj' || fileMode === 'mtl') {
					const geometry = object.children[0].geometry;
					mesh = new THREE.Mesh(geometry, materialstlMesh);
				} else if (fileMode === '3dm') {
					object.traverse((child) => {
						if (child.isMesh) {
							child.material = materialstlMesh;
						}
					});
					mesh = object;
				}

				if (mesh) {
					mesh.scale.set(0.1, 0.1, 0.1);
					const boundingBox = new THREE.Box3().setFromObject(mesh);
					const size = new THREE.Vector3();
					boundingBox.getSize(size);

					const modelCenter = new THREE.Vector3();
					boundingBox.getCenter(modelCenter);
					newControls.target.copy(modelCenter);

					newScene.add(mesh);
				} else {
					console.error('Failed to load model: Invalid file type');
				}
			},
			undefined,
			function (error) {
				console.error('Failed to load STL model:', error);
			}
		);
	}

	// 替换旧的场景、相机、渲染器和控件
	sceneStl = newScene;
	cameraStl = newCamera;
	rendererStl = newRenderer;
	controlsStl = newControls;

	// 监听窗口大小调整
	window.addEventListener('resize', onWindowResizeStl, false);

	animateStl();
}

function onWindowResizeStl() {
	cameraStl.aspect = 250 / 250;
	cameraStl.updateProjectionMatrix();
	rendererStl.setSize(250, 250);
	animateStl();
}
function onWindowResizeAnay() {
	cameraAnay.aspect = 250 / 250;
	cameraAnay.updateProjectionMatrix();
	rendererAnay.setSize(250, 250);
	animateAnay();
}

function animateStl() {
	requestAnimationFrame(animateStl);
	controlsStl.update();
	rendererStl.render(sceneStl, cameraStl);
}
function animateAnay() {
	if (!rendererAnay || !sceneAnay || !cameraAnay) return; // 添加检查

	requestAnimationFrame(animateAnay);
	controlsAnay.update();
	rendererAnay.render(sceneAnay, cameraAnay);
}

// 添加状态控制对象
const lastAnalysisStatus = ref({
	loading: false,
	finished: false,
	success: false,
});

const footAnalysisStatus = ref({
	loading: false,
	finished: false,
	success: false,
});

// 修改监听逻辑
watch(
	() => footL.value,
	() => {
		// 只重置鞋楦的解析状态
		lastAnalysisStatus.value = {
			loading: false,
			finished: false,
			success: false,
		};
	}
);

watch(
	() => footF.value,
	() => {
		// 只重置脚模的解析状态
		footAnalysisStatus.value = {
			loading: false,
			finished: false,
			success: false,
		};
	}
);

// 格式化值（去除引号）
const formatValue = (value: string) => {
	if (!value) return '';
	return value.replace(/['"]/g, '');
};

// 格式化鞋码表数据
const formatSizeTableData = (sizeData: any) => {
	if (!sizeData) return [];

	const tableData = [];

	// 遍历所有路径获取数据
	for (const path in sizeData) {
		const values = sizeData[path];
		const row: any = {
			footLength: '', // 脚长
			worldSize1: '', // 世界鞋号(号差1)
			worldSize2: '', // 世界鞋号(号差2)
			euroSize: '', // 欧洲鞋号
			ukSize: '', // 英国鞋号
			usSizeMen: '', // 美国男鞋号
			usSizeWomen: '', // 美国女鞋号
		};

		// 从值中提取数字
		for (const value of values) {
			const formattedValue = formatValue(value);
			if (formattedValue.includes('脚长')) {
				row.footLength = formattedValue.split('：')[1];
			} else if (formattedValue.includes('世界鞋号/中国鞋号/日本鞋号号差1')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.worldSize1 = match[1];
			} else if (formattedValue.includes('世界鞋号/中国鞋号/日本鞋号号差2')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.worldSize2 = match[1];
			} else if (formattedValue.includes('欧洲鞋号')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.euroSize = match[1];
			} else if (formattedValue.includes('英国鞋号')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.ukSize = match[1];
			} else if (formattedValue.includes('美国鞋号男')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.usSizeMen = match[1];
			} else if (formattedValue.includes('美国鞋号女')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.usSizeWomen = match[1];
			}
		}

		// 只有当行中有数据时才添加到表格中
		if (row.footLength || row.worldSize1 || row.worldSize2 || row.euroSize || row.ukSize || row.usSizeMen || row.usSizeWomen) {
			tableData.push(row);
		}
	}

	return tableData;
};

onBeforeUnmount(() => {
	// 停止所有动画
	stopAnimation();

	// 清理定时器
	if (intervalId.value) {
		clearInterval(intervalId.value);
	}
	if (restoreIntervalId.value) {
		clearInterval(restoreIntervalId.value);
	}

	// 清理Three.js资源
	disposeThreeJsResources();

	// 移除事件监听器
	window.removeEventListener('resize', onWindowResize);
	window.removeEventListener('resize', onWindowResizeStl);
	window.removeEventListener('resize', onWindowResizeAnay);

	// 清理rhino3dm资源
	[doc, anaDoc, Csdoc, Coudoc, Standoc, Footdoc, FLdoc, FBdoc, Latticedoc].forEach((doc) => {
		if (doc) doc.delete();
	});
});

function disposeThreeJsResources() {
	// 首先停止所有动画
	stopAnimation();

	// 清理渲染器
	if (renderer) {
		renderer.dispose();
		renderer.forceContextLoss();
		renderer.domElement?.remove();
		renderer = null;
	}

	// 清理场景中的对象
	if (scene) {
		scene.traverse((object) => {
			if (object.isMesh) {
				object.geometry?.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach((material) => material.dispose());
				} else {
					object.material?.dispose();
				}
			}
		});
		scene.clear();
		scene = null;
	}

	// 清理控制器
	if (controls) {
		controls.dispose();
		controls = null;
	}

	// 清理相机
	camera = null;

	// 清理加载器和其他资源
	if (loadedObjects.value) {
		loadedObjects.value = [];
	}
}

// 在 setup 中添加
const uploadedFileNames = ref(['', '']);

// 李宁选项所需的特殊参数
const StanFootNumber = ref<number>();
const TipToeThickAd = ref<number>(0);
const ToeThickAd = ref<number>(0);
const HeelCenterWAd = ref<number>(0);

// 文本框对应的字符串值
const TipToeThickAd1 = ref<string>('0.0');
const ToeThickAd1 = ref<string>('0.0');
const HeelCenterWAd1 = ref<string>('0.0');

// 对应的值引用
const StanFootNumberValue = ref();
const TipToeThickAdValue = ref();
const ToeThickAdValue = ref();
const HeelCenterWAdValue = ref();

// 自动选择第一个项目的辅助函数
const selectFirstItem = () => {
	if (imagesWithFiles.value && imagesWithFiles.value.length > 0) {
		const firstItem = imagesWithFiles.value[0];
		if (firstItem && firstItem.image) {
			selectImage(firstItem.image);
		}
	}
};
</script>

<style scoped>
.headerC {
	height: 60px;
	width: 100%;
	background: #ffffff;
	display: flex;
	align-items: center;
}

.content {
	/* background: linear-gradient(to bottom right, #e0e0fe, #e0e1fc); */
	width: 100%;
	height: 100vh;
}

.imgS {
	width: 18px;
	height: 18px;
	margin-top: 2px;
	margin-right: 4px;
}

.toptext {
	margin-left: 50px;
	font-family: Alibaba PuHuiTi 3;
	font-size: 16px;
	font-weight: 500;
}

.righttext {
	position: absolute;
	display: flex;
	right: 40px;
	align-content: center;
	background-color: #ffffff;
	z-index: 2;
}

.logolayout {
	display: flex;
	font-size: 17px;
	margin-right: 10px;
}

.avatarl {
	border-radius: 40px;
	width: 25px;
	height: 25px;
	margin-top: -4px;
	margin-right: 2px;
}

.loginlogo {
	width: 169px;
	height: 32px;
}

.content {
	display: flex;
}

.aside {
	/* max-width: 480px; */
	max-width: 280px;
	background-color: #f9f9ff;
}

.active-main {
	/* margin-right: 570px; */
	/* position: absolute; */
	background: linear-gradient(to bottom right, #e8dff7, #e3e9ee) !important;
}

.Mdropdown {
	position: relative;
	display: inline-block;
	width: 280px;
	/* width: 90vw; */
}
.Mdropdown-content {
	display: flex;
	/* flex-direction: column; */
	justify-content: center;
	padding: 10px 0px;
	margin-left: -20px;
	/* margin-top: 10px; */
}
.flex-col1 {
	display: flex;
	flex-direction: column;
	/* margin-top: 10px; */
	/* padding: 10px 20px; */
}

.model-button {
	position: relative;
	border: 1px solid #ccc;
	padding: 8px 16px;
	width: 100vw;
	max-width: 250px;
	background-color: white;
	cursor: pointer;
}

.model-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	width: 4px;
	background-color: #6463ff;
}

.no-purple {
	width: 150px;
	background: rgba(100, 99, 255, 0.2);
	box-sizing: border-box;
	/* 填充/主色 */
	border: 1px solid #6463ff;
	color: #6463ff;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 800;
	margin-left: -2px;
	margin-top: 10px;
	cursor: pointer;
}

.no-purple::before {
	content: none;
}

.purple {
	background: #6463ff;
	border-radius: 5px;
	justify-content: center;
	align-items: center;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: normal;
	color: #ffffff;
	width: 180px;
	margin-top: 10px;
	margin-left: 10px;
}

.Pdropdown {
	position: relative;
	display: inline-block;
}

.Pdropdown-content {
	display: flex;
	flex-direction: column;
	overflow: auto;
	max-height: 850px;
	max-width: 280px;
	padding: 1px 10px;
}

@media only screen and (max-width: 520px) {
	.Pdropdown-content {
		max-height: 50px;
		height: 100%;
	}
	/* .analysis-result {
		max-height: 70px;
	} */
	.squareAna-info {
		max-height: 70px;
	}
}

@media only screen and (min-width: 521px) and (max-width: 1119px) {
	.Pdropdown-content {
		max-height: 180px;
		height: 100%;
	}
	/* .analysis-result {
		max-height: 150px;
	} */
	.squareAna-info {
		max-height: 150px;
	}
}

@media only screen and (min-width: 1120px) and (max-width: 1569px) {
	.Pdropdown-content {
		max-height: 350px;
		height: 100%;
	}
	/* .analysis-result {
		max-height: 250px;
	} */
	.squareAna-info {
		max-height: 200px;
	}
}

@media only screen and (min-width: 1370px) and (max-width: 1500px) {
	.squareAna-info {
		max-height: 400px;
		height: 100%;
	}
}
/* 浏览器125% */
@media only screen and (min-width: 1500px) and (max-width: 1699px) {
	.squareAna-info {
		max-height: 400px;
		height: 100%;
	}
	.Pdropdown-content {
		max-height: 500px;
		height: 100%;
	}
}

@media only screen and (min-width: 1756px) {
	.Pdropdown-content {
		max-height: 700px;
		height: 100%;
	}
	.squareAna-info {
		max-height: 460px;
		height: 100%;
	}
}
/* 浏览器110% */
@media only screen and (min-width: 1800px) {
	.Pdropdown-content {
		max-height: 600px;
		height: 100%;
	}
}
/* 浏览器100% */
@media only screen and (min-width: 2048px) {
	.Pdropdown-content {
		max-height: 690px;
		height: 100%;
	}
}

.computeC {
	position: fixed;
	width: 220px;
	background-color: #6463ff;
	color: #ffffff;
	/* right: 15px; */
	bottom: 15px;
}

.bgbutton {
	background-color: #ffffff;
}

.flex {
	padding: 8px 0;
}

.inputS {
	width: 50px;
	height: 30px;
	border: 1px solid #e5e5e5;
	border-radius: 5px;
	margin-left: 20px;
	/* margin-top: -20px; */
	font-family: Alibaba PuHuiTi 3;
	font-size: 12px;
	font-weight: normal;
	text-align: center;
}

.container {
	position: relative;
}

.overlay {
	z-index: 1;
	width: 100%;
}

.header-button {
	display: flex;
	position: absolute;
	top: 70px;
	right: 10px;
	background-color: #ffffff;
	width: 240px;
	justify-content: space-between;
	padding: 4px 20px;
	align-items: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	/* 添加阴影效果 */
	border-left: 2px solid #6463ff;
	cursor: pointer;
	/* 左侧边框为紫色，宽度为4像素 */
}

.text-result {
	font-family: Alibaba PuHuiTi 3;
	font-size: 15px;
	font-weight: 600;
}

.load-button {
	background: #ffffff;
	box-sizing: border-box;
	/* 描边/浅紫 */
	border: 1px solid #9795f5;
	color: #9795f5;
	width: 55px;
	height: 20px;
	margin-top: 10px;
	margin-left: 5px;
	display: flex;
	justify-content: center;
	align-items: center;
	/* 创建一个内阴影效果，边框颜色为 #3d3d3d */
}

.body {
	margin: 0;
	font-family: Arial, Helvetica, sans-serif;
}

.loader {
	border: 5px solid #f3f3f3;
	border-top: 5px solid #3d3d3d;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	animation: spin 1s linear infinite;
	position: absolute;
	top: 50%;
	left: 63%;
	z-index: 2;
}
.progress-bar {
	height: 100%;
	background-color: #3d3d3d;
	width: 0;
	transition: width 0.1s linear;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.buttonS {
	border: 1px solid transparent;
	border-radius: 2px;
	color: #6463ff;
	/* 设置文字颜色为 #6463FF */
	box-shadow: inset 0 0 0 1px #6463ff;
	width: 100px;
	height: 25px;
	/* background-color: transparent; */
}

.bgc {
	background-color: #e8def7;
}

.boder {
	/* position: absolute; */
	height: 30px;
	width: 150px;
	border: 1px solid var(--el-border-color);
	border-radius: 5px;
	/* margin-top: 20px;
  margin-right: 10px; */
}

.side-style {
	display: flex;
	flex-direction: column;
	line-height: 38px;
}

.testl {
	margin-left: 10px;
}

.result-values {
	display: flex;
	flex-direction: column;
	position: absolute;
	top: 70px;
	right: 10px;
	background-color: #ffffff;
	width: 240px;
	/* height: 40px; */
	/* justify-content: space-between; */
	padding: 4px 17px;
	/* align-items: center; */
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	/* 添加阴影效果 */
	border-left: 2px solid #6463ff;
}
.el-collapse {
	--el-collapse-header-height: 30px;
}
.text-resultbox {
	display: flex;
	flex-direction: column;
	margin-left: 5px;
}

.result-text {
	/* margin-right: 17px; */
	margin-top: 5px;
	color: #7472f1;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 600;
	padding: 5px 0;
	width: 80px;
}

.img-style {
	position: relative;
	margin-left: -22px;
}

.delete-button {
	width: 180px;
	margin-left: 10px;
	margin-top: 5px;
}

.regin-box {
	width: 250px;
	height: 300px;
	margin-top: 10px;
	display: inline-block;
	margin: 10px;
}

.square {
	margin-top: -15px;
	width: 250px;
	height: 250px;
	border-radius: 10px;
	background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
	background-size: 20px 20px, 20px 20px, 20px 20px, 20px 20px;
	background-position: 0px 0px, 0px 10px, 10px 0px, 10px 10px;
}
.squareAna {
	margin-top: -7px;
	margin-bottom: 15px;
	width: 250px;
	height: 250px;
	border-radius: 10px;
	background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
	background-size: 20px 20px, 20px 20px, 20px 20px, 20px 20px;
	background-position: 0px 0px, 0px 10px, 10px 0px, 10px 10px;
}

.m-region {
	/* max-width: 300px; */
	/* width: 1600px; */
	height: 964px;
	padding: 20px 25px;
	border-left: 1px solid #ccc;
	overflow: auto;
	max-height: 940px;
}

.moudle-region {
	background: linear-gradient(270deg, #ffffff 0%, #f5f7ff 100%);
	width: 310px;
	height: 100vh;
	box-sizing: border-box;
	border-width: 0px 2px 0px 0px;
	border-style: solid;
	border-color: #ffffff;
	position: absolute;
	margin-left: 250px;
}

.textR {
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 550;
}

.load-button-all {
	background: #ffffff;
	color: #9795f5;
	box-sizing: border-box;
	border: 1px solid #9795f5;
	margin-top: 5px;
	width: 115px;
	height: 25px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 5px;
}

.row {
	display: flex;
}

.mould-names {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 20px;
	margin-top: -10px;
}

.mould-name {
	margin-right: 40px;
	margin-bottom: 10px;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
	transition: color 0.3s ease;
}

.mould-name:hover {
	color: #6463ff;
}

.mould-name.active {
	color: #6463ff;
	position: relative;
}

.mould-name.active::after {
	content: '';
	position: absolute;
	bottom: -5px;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #6463ff;
}

.analysis-button {
	background-color: #6463ff;
	border-color: #6463ff;
	font-size: 14px;
	height: 32px;
	padding: 0 16px;
	border-radius: 4px;
	transition: all 0.3s ease;
	width: 100px;
}

.analysis-button:hover {
	background-color: #7472f1;
	border-color: #7472f1;
	transform: translateY(-1px);
	box-shadow: 0 2px 4px rgba(100, 99, 255, 0.2);
}

.analysis-button:active {
	transform: translateY(0);
}

.switchTo {
	background: transparent;
	border: none;
	margin-left: 10px;
	padding: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #7472f1;
}
.switchTo:hover {
	background: rgba(100, 99, 255, 0.2);
	border-radius: 5px;
}
.switchStyle {
	position: absolute;
	display: flex;
	margin-top: -40px;
	width: 100vw;
	max-width: 1800px;
	justify-content: center;
}
/* .left-regin {
	max-width: 500vw;
	background-color: #6463ff;
} */
.button-icon {
	margin-right: 5px; /* 调整图像与文本之间的间距 */
}

.active {
	color: #6463ff;
}
.flex-col {
	display: flex;
	flex-direction: column;
	margin-top: 17px;
	margin-left: 5px;
	/* padding: 20px 0; */
	/* padding: 10px 20px; */
}

.text1 {
	color: #6463ff; /* 设置文字颜色 */
	cursor: pointer; /* 设置鼠标悬停时的手指效果 */
	transition: color 0.3s, text-decoration 0.3s; /* 添加过渡效果 */
}
.text2 {
	color: #6463ff; /* 设置文字颜色 */
	cursor: pointer; /* 设置鼠标悬停时的手指效果 */
	transition: color 0.3s, text-decoration 0.3s; /* 添加过渡效果 */
	margin-top: 19px;
}
.text1:hover,
.text2:hover {
	color: #4a4aff; /* 鼠标悬停时文字颜色变化 */
	text-decoration: underline; /* 添加下划线 */
}
.custom-dialog .el-dialog__wrapper {
	position: fixed;
	top: 100px;
	right: 100px;
	bottom: auto;
	left: auto;
	height: 500px;
}
.dialog-content {
	display: flex;
	height: 580px;
	margin-top: -10px;
}
.el-overlay {
	display: none; /* Hide the overlay */
}

.left-section {
	flex: 3;
	padding: 5px;
	border-right: 1px solid #ddd; /* Optional for visual separation */
	/* background: #000; */
}

.right-section {
	flex: 7;
	padding: 5px;
	width: 100%;
	/* background: #cb5151; */
}

.dialog-footer {
	/* padding: 10px; */
	text-align: right;
	/* margin-top: -10px; */
}
.search-style {
	margin-right: 700px;
}
.reset-style {
	left: 150px;
	position: absolute;
}
.add-style {
	left: 350px;
	position: absolute;
}
.select-style {
	width: 200px;

	/* margin-top: 20px; */
}
.item {
	margin-bottom: 10px;
}

.label {
	display: block;
	margin-bottom: 5px;
}
.container {
	display: flex;
	align-items: center;
	gap: 20px;
	margin-top: 20px;
}

.label-column {
	flex: 3.5;
	margin-top: 10px;
}
.select-column {
	flex: 6.5;
}

.label-item,
.select-item {
	display: flex;
	align-items: center;
}
.label-item {
	display: flex;
	align-items: center;
	margin-bottom: 30px;
	/* padding: 2px 0; */
}
.el-select .el-select__tags {
	overflow: hidden;
	text-overflow: ellipsis;
}
.select-item {
	margin-bottom: 20px;
}
.right-section {
	padding: 16px;
}

.image-gallery {
	display: flex;
	flex-direction: column;
}

.image-row {
	display: flex;
	margin-bottom: 8px;
}

/* .gallery-image:last-child {
	margin-right: 0;
} */
.el-pagination {
	/* margin-top: 16px; */
	display: flex;
	justify-content: center;
}
.gallery-grid {
	display: flex;
	flex-direction: column; /* 垂直排列每行 */
	gap: 10px;
}
.gallery-row {
	display: flex; /* 横向排列图片 */
	gap: 10px; /* 图片之间的间距 */
}
.gallery-image {
	width: 200px;
	height: 130px;
	/* margin-right: 8px; */
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
	margin-top: -10px;
	/* box-sizing: border-box; */
}
.gallery-image.selected {
	border: 2px solid #007bff; /* 选中的边框颜色 */
}
.pagina-tyle {
	position: absolute;
	top: 580px;
	right: 0;
}
.top-degin {
	display: flex;
	justify-content: center;
	width: 1000px;
	position: absolute;
	top: 30px;
}
.el-button.active {
	background-color: #6463ff; /* 按钮的背景颜色 */
	color: white; /* 按钮文字颜色 */
}
.center {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 200px;
	margin-bottom: 200px;
}
.image-container {
	position: relative;
	display: inline-block;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
	width: 200px;
	height: 130px;
	margin: 10px 0; /* 10px 的上下间距 */
	/* padding: 10px; */
}

.gallery-image {
	width: 200px;
	height: 130px;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
}

.delete-buttonF {
	position: absolute;
	top: -15px; /* 调整按钮的位置 */
	right: 1px;
}
.update-buttonF {
	position: absolute;
	top: -15px; /* 调整按钮的位置 */
	right: 17px;
}

/* .delete-buttonF:hover {
	background: darkred;
} */
.desc-style {
	display: flex;
	justify-content: center;
	align-content: center;
	margin-top: 100px;
}
.filename-style {
	display: inline-block;
}
.file-name {
	width: 200px;
	display: flex;
	justify-content: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.demo-progress {
	position: absolute;
	top: 50%;
	left: 63%;
	font-size: 28px;
}
.left-style {
	margin-left: 20px;
}
.header-icon {
	margin-bottom: 5px;
}

.success-icon {
	color: #67c23a;
	margin-right: 4px;
	font-size: 16px;
}

.error-icon {
	color: #f56c6c;
	margin-right: 4px;
	font-size: 16px;
}

/* 调整loading spinner的颜色 */
.analysis-button :deep(.el-loading-spinner) {
	color: #ffffff;
}

/* 调整icon的垂直对齐 */
.analysis-button .el-icon {
	vertical-align: middle;
	margin-top: -2px;
}

.analysis-container {
	display: flex;
	align-items: center;
	gap: 12px;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 4px;
	font-size: 14px;
	margin-left: 10px;
}

.success-text {
	color: #67c23a;
}

.error-text {
	color: #f56c6c;
}

.is-loading {
	animation: rotating 2s linear infinite;
	color: #409eff;
	font-size: 16px;
}

@keyframes rotating {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.analysis-group {
	display: flex;

	align-items: center;
	gap: 12px;
}

.analysis-content {
	display: flex;
	flex-direction: column;
	gap: 16px;
	margin-left: -14px;
}

.analysis-result {
	margin-left: -10px;
	margin-top: -15px;
	padding: 8px;
	background: #f8f9fa;
	border-radius: 4px;
	width: 100%; /* 改为100%宽度，使其自适应父容器 */
	min-width: 310px; /* 设置最小宽度 */
	max-width: 100%; /* 确保不会超出父容器 */
	overflow-x: auto; /* 添加水平滚动条 */
}

.result-table {
	display: flex;
	flex-direction: column;
	/* gap: 4px; */
	min-width: 280px; /* 设置最小宽度确保内容不会挤压 */
}

.table-row {
	display: flex;
	align-items: center;
	padding: 5px 0;
	width: 100%;
}

.table-cell {
	padding: 1px 2px;
	margin-bottom: 5px;
	font-size: 12px;
	white-space: nowrap; /* 防止文字换行 */
	margin-left: 15px;
}

.table-cell.label {
	min-width: 70px; /* 改为最小宽度 */
	flex-shrink: 0; /* 防止标签被压缩 */
}

.size-table {
	margin-top: 8px;
	width: 100%;
	position: relative; /* 添加相对定位 */
	overflow: hidden;
}

.size-table :deep(.el-table) {
	font-size: 11px;
	width: 100% !important;
	overflow-x: auto !important; /* 强制显示横向滚动条 */
}

.size-table :deep(.el-table__body-wrapper) {
	overflow-x: auto !important; /* 强制显示横向滚动条 */
	overflow-y: auto !important; /* 强制显示纵向滚动条 */
	max-height: 300px;
	min-height: 100px; /* 添加最小高度 */
}

/* 自定义滚动条样式 */
.size-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
	width: 6px;
	height: 6px;
}

.size-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
	background: #dcdfe6;
	border-radius: 3px;
}

.size-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
	background: #f5f7fa;
}

/* 确保表格列不会被挤压 */
.size-table :deep(.el-table__header) {
	table-layout: fixed;
}

.size-table :deep(.el-table__body) {
	table-layout: fixed;
}

/* 确保表格单元格内容不会被截断 */
.size-table :deep(.el-table .cell) {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	line-height: 1.2; /* 调整行高使文字更紧凑 */
}

/* 调整表格头部样式 */
.size-table :deep(.el-table__header-wrapper) {
	overflow: visible !important; /* 确保表头提示不会被截断 */
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
	.analysis-result {
		margin-left: 0;
		width: 100%;
	}

	.size-table :deep(.el-table) {
		font-size: 10px; /* 在小屏幕上进一步减小字体 */
	}
}

@media screen and (max-width: 768px) {
	.table-cell {
		font-size: 11px;
	}

	.size-table :deep(.el-table) {
		font-size: 9px;
	}
}
.button-status-group {
	display: flex;
	margin-top: -10px;
}
.button-text {
	display: inline-block;
	max-width: 140px; /* 留出一些边距 */
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.squareAna-info {
	width: 300px;
	height: 460px;
	overflow: auto;
	margin-left: -5px;
}
.regin-w {
	height: 110px;
}
.dialog-main-content {
	display: flex;
	justify-content: center;
}
.right-preview {
	margin-left: 20px;
	width: 220px;
}
.center-section {
	width: 620px;
}

/* 新的滑动控件样式 */
.slider-container {
	margin-bottom: 15px;
}

.slider-label {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.slider-label p {
	margin: 0;
	font-size: 14px;
	color: #606266;
}

/* 自定义el-slider样式 */
.slider-container :deep(.el-slider) {
	margin-top: 8px;
}

.slider-container :deep(.el-slider__runway) {
	background-color: #e4e7ed;
}

.slider-container :deep(.el-slider__bar) {
	background-color: #409eff;
}

.slider-container :deep(.el-slider__button) {
	border-color: #409eff;
}

.slider-container :deep(.el-slider__button:hover) {
	transform: scale(1.2);
}

/* 自定义el-input-number样式 */
.slider-container :deep(.el-input-number) {
	width: 120px;
}

.slider-container :deep(.el-input-number .el-input__inner) {
	text-align: center;
}
</style>
