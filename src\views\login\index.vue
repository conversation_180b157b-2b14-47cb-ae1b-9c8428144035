<template>
	<div class="select-none">
		<!-- <div class="tenant">
      <tenant/>
    </div> -->
		<img :src="bg" class="wave" />
		<div class="flex-c absolute right-5 top-3"></div>
		<div class="login-container">
			<div class="loginBox">
				<div class="login-form">
					<!-- 根据域名判断显示不同的logo -->
					<div v-if="isLiningDomain" class="text-logo">李宁脚转楦定制化系统</div>
					<img v-else :src="logo" class="logo" />
					<register v-if="loginType === LoginTypeEnum.REGISTER" @change="changeLoginType" />
					<password v-if="loginType === LoginTypeEnum.PASSWORD" @signInSuccess="signInSuccess" @change="changeLoginType" />
					<mobile v-if="loginType === LoginTypeEnum.MOBILE" @signInSuccess="signInSuccess" @change="changeLoginType" />
				</div>
			</div>
		</div>
		<!-- 页脚信息 -->
		<div class="login-footer">
			<div class="footer-content">
				<p class="footer-beian">
					<a href="https://beian.miit.gov.cn" target="_blank" rel="noopener noreferrer" class="beian-link"> 沪ICP备2025128389号 </a>
				</p>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="loginIndex">
// import { useThemeConfig } from '/@/stores/themeConfig';
import { NextLoading } from '/@/utils/loading';
import bg from '/@/assets/login/bg.png';
import logo from '/@/assets/login/LOGO.png';
// import { useI18n } from 'vue-i18n';
// import { formatAxis } from '/@/utils/formatTime';
import { useMessage } from '/@/hooks/message';
import { Session } from '/@/utils/storage';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { LoginTypeEnum } from '/@/api/login';

// 引入组件
const Password = defineAsyncComponent(() => import('./component/password.vue'));
const Mobile = defineAsyncComponent(() => import('./component/mobile.vue'));
// const Social = defineAsyncComponent(() => import('./component/social.vue'));
const Register = defineAsyncComponent(() => import('./component/register.vue'));
// const Tenant = defineAsyncComponent(() => import('./component/tenant.vue'));

// 定义变量内容
// const storesThemeConfig = useThemeConfig();
// const { themeConfig } = storeToRefs(storesThemeConfig);
// const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 登录方式
const loginType = ref(LoginTypeEnum.PASSWORD);

// 检查是否为李宁域名
const isLiningDomain = computed(() => {
	const currentDomain = window.location.host;
	return currentDomain.includes('lining.gaicloud.cn');
});

// 修改登录类型
const changeLoginType = (type: LoginTypeEnum) => {
	loginType.value = type;
};

// 获取布局配置信息
// const getThemeConfig = computed(() => {
//   return themeConfig.value;
// });

// 登录成功后的跳转处理事件
const signInSuccess = async () => {
	const isNoPower = await initBackEndControlRoutes();
	if (isNoPower) {
		useMessage().warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		// let currentTimeInfo = formatAxis(new Date());
		if (route.query?.redirect) {
			router.push('/gai');
			// path: <string>route.query?.redirect,
			// query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
			// });
		} else {
			router.push('/');
		}
		// 登录成功提示
		// const signInText = t('signInText');
		// useMessage().success(`${currentTimeInfo}，${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}
};

// 页面加载时
onMounted(() => {
	NextLoading.done();
});
</script>
<style scoped>
.login-container {
	justify-content: center;
	display: flex;
	align-items: center;
	text-align: center;
	width: 100vw;
	height: 100vh;
	/* display: grid;
	padding: 0 2rem; */
}

.loginBox {
	width: 400px;
	background: rgba(255, 255, 255, 0.5);
	border-radius: 10px;
	backdrop-filter: blur(10px);
}

.login-form {
	border-radius: 10px;
	padding: 20px;
}

.logo {
	position: relative;
	margin-top: 20px;
	left: 35%;
}

.text-logo {
	margin-top: 20px;
	margin-bottom: 20px;
	font-size: 24px;
	font-weight: bold;
	color: #2c3e50;
	text-align: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	letter-spacing: 2px;
}

.login-footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	padding: 15px 20px;
	z-index: 100;
}

.footer-content {
	text-align: center;
	max-width: 1200px;
	margin: 0 auto;
}

.footer-text {
	margin: 0 0 8px 0;
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}

.footer-beian {
	margin: 0;
	font-size: 14px;
	line-height: 1.4;
}

.beian-link {
	color: #666;
	text-decoration: none;
	transition: color 0.3s ease;
	border-bottom: 1px solid transparent;
}

.beian-link:hover {
	color: #409eff;
	border-bottom-color: #409eff;
}

.beian-link:focus {
	outline: 2px solid #409eff;
	outline-offset: 2px;
	border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.login-footer {
		padding: 12px 15px;
	}

	.footer-text,
	.footer-beian {
		font-size: 12px;
	}
}
</style>
