export default {
	post: {
		index: '#',
		importPostTip: ' import Post',
		postId: 'postId',
		postCode: 'postCode',
		postName: 'postName',
		postSort: 'postSort',
		remark: 'remark',
		delFlag: 'delFlag',
		createTime: 'createTime',
		createBy: 'createBy',
		updateTime: 'updateTime',
		updateBy: 'updateBy',
		tenantId: 'tenantId',
		inputpostIdTip: 'input postId',
		inputpostCodeTip: 'input postCode',
		inputpostNameTip: 'input postName',
		inputpostSortTip: 'input postSort',
		inputremarkTip: 'input remark',
		inputdelFlagTip: 'input delFlag',
		inputcreateTimeTip: 'input createTime',
		inputcreateByTip: 'input createBy',
		inputupdateTimeTip: 'input updateTime',
		inputupdateByTip: 'input updateBy',
		inputtenantIdTip: 'input tenantId',
	},
};
