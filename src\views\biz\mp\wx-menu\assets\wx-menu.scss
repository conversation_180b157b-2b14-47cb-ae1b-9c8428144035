.clearfix::after {
	content: '';
	display: table;
	clear: both;
}

div {
	text-align: left;
}

.weixin-hd {
	color: #fff;
	text-align: center;
	position: relative;
	bottom: 426px;
	left: 0px;
	width: 300px;
	height: 64px;
	background: transparent url('./assets/menu_head.png') no-repeat 0 0;
	background-position: 0 0;
	background-size: 100%;
}

.weixin-title {
	color: #fff;
	font-size: 14px;
	width: 100%;
	text-align: center;
	position: absolute;
	top: 33px;
	left: 0px;
}

.weixin-menu {
	background: transparent url('./assets/menu_foot.png') no-repeat 0 0;
	padding-left: 43px;
	font-size: 12px;
}

.menu_option {
	width: 40% !important;
}

.public-account-management {
	min-width: 1200px;
	width: 1200px;
	margin: 0 auto;

	.left {
		float: left;
		display: inline-block;
		width: 350px;
		height: 715px;
		background: url('./assets/iphone_backImg.png') no-repeat;
		background-size: 100% auto;
		padding: 518px 25px 88px;
		position: relative;
		box-sizing: border-box;

		/*第一级菜单*/
		.menu_main {
			.menu_bottom {
				position: relative;
				float: left;
				display: inline-block;
				box-sizing: border-box;
				width: 85.5px;
				text-align: center;
				border: 1px solid #ebedee;
				background-color: #fff;
				cursor: pointer;

				&.menu_addicon {
					height: 46px;
					line-height: 46px;
				}

				.menu_item {
					height: 44px;
					line-height: 44px;
					text-align: center;
					box-sizing: border-box;
					width: 100%;

					&.active {
						border: 1px solid #2bb673;
					}
				}

				.menu_subItem {
					height: 44px;
					line-height: 44px;
					text-align: center;
					box-sizing: border-box;

					&.active {
						border: 1px solid #2bb673;
					}
				}
			}

			i {
				color: #2bb673;
			}

			/*第二级菜单*/
			.submenu {
				position: absolute;
				width: 85.5px;
				bottom: 45px;

				.subtitle {
					background-color: #fff;
					box-sizing: border-box;
				}
			}
		}

		.save_div {
			margin-top: 15px;
			text-align: center;

			.save_btn {
				bottom: 20px;
				left: 100px;
			}
		}
	}

	/*右边菜单内容*/
	.right {
		float: left;
		width: 63%;
		background-color: #e8e7e7;
		padding: 20px;
		margin-left: 20px;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;

		.configure_page {
			.delete_btn {
				text-align: right;
				margin-bottom: 15px;
			}

			.menu_content {
				margin-top: 20px;
			}

			.configur_content {
				margin-top: 20px;
				background-color: #fff;
				padding: 20px 10px;
				border-radius: 5px;
			}

			.blue {
				color: #29b6f6;
				margin-top: 10px;
			}

			.applet {
				margin-bottom: 20px;

				span {
					width: 20%;
				}
			}

			.input_width {
				width: 40%;
			}

			.material {
				.input_width {
					width: 30%;
				}

				.el-textarea {
					width: 80%;
				}
			}
		}
	}

	.el-input {
		width: 70%;
		margin-right: 2%;
	}
}

.pagination {
	text-align: right;
	margin-right: 25px;
}

.select-item {
	width: 280px;
	padding: 10px;
	margin: 0 auto 10px auto;
	border: 1px solid #eaeaea;
}

.select-item2 {
	padding: 10px;
	margin: 0 auto 10px auto;
	border: 1px solid #eaeaea;
}

.ope-row {
	padding-top: 10px;
	text-align: center;
}

.item-name {
	font-size: 12px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: center;
}
