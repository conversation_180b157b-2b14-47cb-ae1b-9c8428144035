import request from '/@/utils/request';
//项目列表
export function FootList(query?: Object) {
  return request({
    url: '/gai/gaiProject/footPage',
    method: 'get',
    params: query,
  });
}
export function FunsionList(query?: Object) {
  return request({
    url: '/gai/gaiProject/funsionPage',
    method: 'get',
    params: query,
  });
}
//任务列表
export function TaskList(query?: Object) {
  return request({
    url: '/gai/gaiTask/page',
    method: 'get',
    params: query,
  });
}
//根据id查询项目
export function getProjectObj(id?: string) {
  return request({
    url: '/gai/gaiProject/' + id,
    method: 'get',
  });
}
//根据id查询任务
export function getTaskObj(taskId?: string, taskType?: string, current: number = 1, size: number = 8) {
  // 构建分页查询参数
  const params: any = {
    current,
    size,
  };

  // 如果有 taskType，将其作为查询参数
  if (taskType) {
    params['taskType'] = taskType;
  }

  // 发起请求，并传递分页参数
  return request({
    url: '/gai/gaiTask/foot/' + taskId,
    method: 'get',
    params: params,  // 请求参数
  });
}
export function getFunsionTaskObj(taskId?: string, current: number = 1, size: number = 8) {
  // 构建分页查询参数
  const params: any = {
    current,
    size,
  };

  // 如果有 taskId，也可以将 taskId 作为查询参数
  if (taskId) {
    params['taskId'] = taskId;
  }

  // 发起请求，并传递分页参数
  return request({
    url: '/gai/gaiTask/funsion/' + taskId,
    method: 'get',
    params: params,  // 请求参数
  });
}

export function searchProject(projectName, startTime, endTime, updateBy, projectType, current, size) {
  return request({
    url: '/gai/gaiProject/search',
    method: 'get',
    params: {
      projectName: projectName,
      startTime: startTime,
      endTime: endTime,
      updateBy: updateBy,
      projectType: projectType,
      current: current,
      size: size
    }
  });
}
export function funsionSearchProject(projectName, startTime, endTime, updateBy, current, size) {
  return request({
    url: '/gai/gaiProject/funsionSearch',
    method: 'get',
    params: {
      projectName: projectName,
      startTime: startTime,
      endTime: endTime,
      updateBy: updateBy,
      current: current,
      size: size
    }
  });
}

export function searchTask(taskName, startTime, endTime, updateBy, taskId, taskType, current, size) {
  return request({
    url: '/gai/gaiTask/search',
    method: 'get',
    params: {
      taskName: taskName,
      startTime: startTime,
      endTime: endTime,
      updateBy: updateBy,
      taskId: taskId,
      taskType: taskType,
      current: current,
      size: size
    }
  });
}
export function FunsionSearchTask(taskName, startTime, endTime, updateBy, taskId, current, size) {
  return request({
    url: '/gai/gaiTask/FunsionSearch',
    method: 'get',
    params: {
      taskName: taskName,
      startTime: startTime,
      endTime: endTime,
      updateBy: updateBy,
      taskId: taskId,
      current: current,
      size: size
    }
  });
}
//新增项目
export const addProjectObj = (obj: Object) => {
  return request({
    url: '/gai/gaiProject/save',
    method: 'post',
    data: obj,
  });
};
//新增任务
export const addTaskObj = (obj: Object) => {
  return request({
    url: '/gai/gaiTask/save',
    method: 'post',
    data: obj,
  });
};

//修改项目
export const putProjectObj = (obj: Object) => {
  return request({
    url: '/gai/gaiProject/update',
    method: 'put',
    data: obj,
  });
};
//修改任务
export const putTaskObj = (obj: Object) => {
  return request({
    url: '/gai/gaiTask/update',
    method: 'put',
    data: obj,
  });
};


//根据id删除项目
export const delProjectObj = (ids: Object) => {
  return request({
    url: '/gai/gaiProject/delete',
    method: 'delete',
    data: ids,
  });
};

//根据id删除任务
export const delTaskObj = (ids: Object) => {
  return request({
    url: '/gai/gaiTask/delete',
    method: 'delete',
    data: ids,
  });
};

//上传配置
export function uploadGHFile(obj: Object) {
  return request({
    url: '/gai/gaiTask/upload',
    method: 'post',
    data: obj
  });
}

//上传计算结果
export function uploadCompute(obj: Object) {
  return request({
    url: '/gai/gaiTask/uploadCompute',
    method: 'post',
    data: obj
  });
}



//通过id查询模型文件
export function getGHFileById(id) {
  return request({
    url: '/gai/gaiTask/getGHFileById',
    method: 'get',
    params: {
      id: id
    }
  });
}

//上传stl文件
export function uploadStl(obj: Object) {
  return request({
    url: '/gai/gaiTask/uploadStl',
    method: 'post',
    data: obj
  });
}

//上传3dm结果文件
export function uploadRhino(obj: Object) {
  return request({
    url: '/gai/gaiTask/uploadRhino',
    method: 'post',
    data: obj
  });
}
//上传3dm结果文件
export function uploadAddress(obj: Object) {
  return request({
    url: '/gai/gaiTask/uploadAddress',
    method: 'post',
    data: obj
  });
}

//搜索鞋楦
export function searchFootLast(page, footLastDTO) {
  return request({
    url: '/gai/footLast/searchFootLast',
    method: 'post',
    data: {
      ...footLastDTO,
      current: page.current,
      size: page.size
    }
  });
}
//搜索脚型
export function searchFootShape(page, footShapeDTO) {
  return request({
    url: '/gai/footLast/searchFootShape',
    method: 'post',
    data: {
      ...footShapeDTO,
      current: page.current,
      size: page.size
    }
  });
}

//搜索鞋垫
export function searchInsoles(page, insolesDTO) {
  return request({
    url: '/gai/insole/searchInsole',
    method: 'post',
    data: {
      ...insolesDTO,
      current: page.current,
      size: page.size
    }
  });
}


export function getDataType(type) {
  return request({
    url: '/gai/footLast/getDataType',
    method: 'get',
    params: {
      type: type,
    }
  });
}

export const updateByIdFootLast = (obj: Object) => {
  return request({
    url: '/gai/footLast/updateByIdFootLast',
    method: 'put',
    data: obj,
  });
};

export const getFootLastById = (id: string) => {
  return request({
    url: '/gai/footLast/footLast/' + id,
    method: 'get',
  });
};

export const saveFootLast = (obj: Object) => {
  return request({
    url: '/gai/footLast/saveFootLast',
    method: 'post',
    data: obj,
  });
};

export function uploadObj(obj?: Object) {
  return request({
    url: '/gai/gaiTask/uploadStl',
    method: 'post',
    data: obj,
  });
}
export function uploadSettings(obj?: Object) {
  return request({
    url: '/gai/gaiTask/uploadSettings',
    method: 'post',
    data: obj,
  });
}


export const deleteFootLast = (obj: Object) => {
  return request({
    url: '/gai/footLast/deleteFootLast',
    method: 'delete',
    data: obj,
  });
};

export const deleteFootShape = (obj: Object) => {
  return request({
    url: '/gai/footLast/deleteFootShape',
    method: 'delete',
    data: obj,
  });
};

export const updateByIdFootShape = (obj: Object) => {
  return request({
    url: '/gai/footLast/updateByIdFootShape',
    method: 'put',
    data: obj,
  });
};

export const getFootShapeById = (id: string) => {
  return request({
    url: '/gai/footLast/footShape/' + id,
    method: 'get',
  });
};


export const saveFootShape = (obj: Object) => {
  return request({
    url: '/gai/footLast/saveFootShape',
    method: 'post',
    data: obj,
  });
};

// 鞋垫相关接口
export const deleteInsoles = (obj: Object) => {
  return request({
    url: '/gai/insole/deleteInsoles',
    method: 'delete',
    data: obj,
  });
};

export const updateByIdInsoles = (obj: Object) => {
  return request({
    url: '/gai/insole/updateInsole',
    method: 'put',
    data: obj,
  });
};

export const getInsolesById = (id: string) => {
  return request({
    url: '/gai/insole/insole/' + id,
    method: 'get',
  });
};

export const saveInsoles = (obj: Object) => {
  return request({
    url: '/gai/insole/saveInsole',
    method: 'post',
    data: obj,
  });
};

export const getDeleteById = (id: string) => {
  return request({
    url: '/gai/gaiTask/delete/' + id,
    method: 'post',
  });
};

export const doRhino = () => {
  return request({
    url: '/gai/rhino/doRhino',
    method: 'post',
  });
};

//代理下载分析文件
export function downloadAnalysisFile(type: string) {
  return request({
    url: '/gai/footLast/downloadAnalysisFile',
    method: 'get',
    responseType: 'arraybuffer',  // 确保返回二进制数据
    params: {
      type: type,
    }
  });
}

//代理下载生成方式文件
export function downloadDefinitionFile(type: string) {
  return request({
    url: '/gai/footLast/downloadDefinitionFile',
    method: 'get',
    responseType: 'arraybuffer',  // 确保返回二进制数据
    params: {
      type: type,
    }
  });
}

//代理下载鞋垫生成方式文件
export function downloadInsolesFile(type: string) {
  return request({
    url: '/gai/footLast/downloadInsolesFile',
    method: 'get',
    responseType: 'arraybuffer',  // 确保返回二进制数据
    params: {
      type: type,
    }
  });
}

//代理下载气孔生成方式文件
export function downloadPoreFile(type: string) {
  return request({
    url: '/gai/footLast/downloadPoreFile',
    method: 'get',
    responseType: 'arraybuffer',  // 确保返回二进制数据
    params: {
      type: type,
    }
  });
}

//代理下载贴花生成方式文件
export function downloadDecalFile(type: string) {
  return request({
    url: '/gai/footLast/downloadDecalFile',
    method: 'get',
    responseType: 'arraybuffer',  // 确保返回二进制数据
    params: {
      type: type,
    }
  });
}


