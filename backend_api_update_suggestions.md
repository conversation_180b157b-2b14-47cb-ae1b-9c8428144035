# 后端 API 修改建议 - 纹理分组支持父子级

为了在前端实现纹理分组的父子级（树形）结构，需要对后端 API 进行以下调整。

## 1. 数据模型调整

在纹理分组的数据库表（或对应的模型）中，增加一个 `parentId` 字段，用于表示当前分组的父级分组 ID。

- **字段名**: `parentId`
- **类型**: 与分组 `id` 相同的类型 (例如 `VARCHAR`, `INT`, `UUID` 等)。
- **描述**: 指向父级分组的 `id`。如果一个分组是顶级分组，则 `parentId` 为 `null`。

**示例 `texture_group` 表结构:**

| 字段名      | 类型           | 描述               |
| ----------- | -------------- | ------------------ |
| `id`        | `VARCHAR(36)`  | 主键 ID            |
| `groupName` | `VARCHAR(255)` | 分组名称           |
| `userId`    | `INT`          | 用户 ID            |
| `parentId`  | `VARCHAR(36)`  | **(新增)** 父级 ID |
| `...`       | `...`          | 其他字段           |

## 2. API 接口调整

### GET `/gai/texture/groups` - 获取纹理分组列表

此接口目前返回的是一个扁平的分组列表。为了支持前端构建树形结构，**无需修改此接口**。前端会自行根据 `parentId` 将扁平列表转换为树形结构。

**请确保返回的每个分组对象都包含 `id`, `groupName`, 和 `parentId` 字段。**

**返回数据示例:**

```json
{
	"ok": true,
	"data": [
		{
			"id": "group-1",
			"groupName": "木纹",
			"userId": 1,
			"parentId": null
		},
		{
			"id": "group-2",
			"groupName": "橡木",
			"userId": 1,
			"parentId": "group-1"
		},
		{
			"id": "group-3",
			"groupName": "石材",
			"userId": 1,
			"parentId": null
		}
	]
}
```

### POST `/gai/texture/groups` - 新增纹理分组

此接口的请求体需要能够接收 `parentId` 字段。

**请求体 (Request Body) 示例:**

```json
{
	"groupName": "胡桃木",
	"userId": 1,
	"parentId": "group-1" // 可选，如果没有则为顶级分组
}
```

后端逻辑需要处理这个 `parentId` 并保存到数据库。

### PUT `/gai/texture/groups/{id}` - 修改纹理分组

此接口的请求体也需要能够接收 `parentId` 字段，以便支持移动分组到不同的父级下。

**请求体 (Request Body) 示例:**

```json
{
	"groupName": "深色胡桃木",
	"parentId": "group-1" // 可选，可以修改父级
}
```

### DELETE `/gai/texture/groups/{id}` - 删除纹理分组

删除分组时，需要考虑其子分组的处理策略：

1.  **策略一 (推荐):** 不允许删除包含子分组的分组。如果尝试删除，应返回错误提示，要求用户先删除所有子分组。
2.  **策略二:** 级联删除。删除一个分组时，其下的所有子分组（以及子孙分组）也一并删除。

请与产品确认期望的行为。

## 总结

核心改动是在 `texture_group` 实体中增加 `parentId`，并在 `新增` 和 `修改` 的接口中支持对该字段的写入。获取列表的接口只需确保返回了 `parentId` 即可。
