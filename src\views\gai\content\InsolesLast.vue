<template>
	<div>
		<div class="flex spaceB">
			<div class="toptext">项目中心</div>
			<el-button class="graycolor" @click="openDialog">+ &nbsp;&nbsp;创建项目</el-button>
			<el-dialog v-model="dialogVisible" title="创建项目" width="35%">
				<!-- 项目名称输入框 -->
				<el-form label-width="100px" :model="form" :rules="rules" ref="formCreateRef">
					<el-form-item label="项目名称" prop="projectName">
						<el-input v-model="form.projectName" placeholder="请输入项目名称，25个字以内"></el-input>
					</el-form-item>
					<!-- 项目介绍输入框 -->
					<el-form-item label="项目介绍" prop="projectDescription">
						<!-- <el-resize-observer @resize="handleResize"> -->
						<el-input type="textarea" v-model="form.projectDescription" placeholder="请输入项目介绍，80个字以内"></el-input>
						<!-- </el-resize-observer> -->
					</el-form-item>
				</el-form>
				<!-- 确认和取消按钮 -->
				<template v-slot:footer>
					<span class="dialog-footer">
						<el-button @click="dialogVisible = false">取消</el-button>
						<el-button type="primary" @click="createProject">确认</el-button>
					</span>
				</template>
			</el-dialog>
		</div>
		<page-container title="项目名称">
			<!-- 搜索栏 -->
			<template #button>
				<div class="align">
					<el-input v-model="input" style="width: 240px" placeholder="请输入项目名称" />
					<p class="align">日期范围</p>
					<div class="block">
						<el-date-picker
							v-model="dateRange"
							type="daterange"
							unlink-panels
							range-separator="-"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							:shortcuts="shortcuts"
						/>
					</div>
					<div class="bottonM">
						<el-button @click="search" class="buttonStyle" :icon="Search">搜索</el-button>
						<el-button @click="reset" class="buttonStyle">
							<el-icon :size="35"> <Refresh /> </el-icon>&nbsp;&nbsp;重置
						</el-button>
					</div>
				</div>
			</template>
		</page-container>
		<!-- 显示方块内容 -->
		<div class="project-container" v-if="projects.length > 0">
			<el-row :gutter="20">
				<el-col :span="12" v-for="project in projects" :key="project.id">
					<el-card class="box-card" @click="goFootQuest(project)">
						<div class="content-wrapper">
							<div class="project-info">
								<div class="project-button">
									<span class="project-name">{{ project.projectName }}</span>
									<div class="header-buttons">
										<el-button type="text" @click.stop="showEditDialog(project)" :icon="Edit"></el-button>
										<el-button type="text" @click.stop="showDeleteConfirm(project)" :icon="Delete"></el-button>
									</div>
								</div>
								<div class="flex">
									<p class="createT">{{ project.updateBy }}</p>
									<p class="createA">{{ formatDate(project.createTime) }}</p>
								</div>
								<p class="createB">
									<span class="project-description">{{ truncateDescription(project.projectDescription) }}</span>
								</p>
							</div>
							<div class="task-count">
								<p><b>任务数</b></p>
								<p class="task-number">
									{{ project.taskNum }}
								</p>
							</div>
						</div>
					</el-card>
					<!-- <div v-if="index !== projects.length - 1" class="spacer"></div> -->
				</el-col>
			</el-row>
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:page-sizes="[8, 16, 32, 64]"
				:small="small"
				:disabled="disabled"
				:background="background"
				layout="total, sizes, prev, pager, next, jumper"
				:total="totalItems"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				class="page-region"
			/>
		</div>
		<template v-else>
			<div class="empty">
				<el-empty description="暂无数据"></el-empty>
			</div>
		</template>

		<el-dialog title="编辑项目" v-model="editDialogVisible" width="35%">
			<el-form :model="form" :rules="rules" label-width="80px" ref="formEditRef">
				<el-form-item label="项目名称" prop="projectName">
					<el-input v-model="form.projectName"></el-input>
				</el-form-item>
				<el-form-item label="项目介绍" prop="projectDescription">
					<el-input type="textarea" v-model="form.projectDescription" placeholder="请输入项目介绍，80个字以内"></el-input>
				</el-form-item>
			</el-form>
			<template v-slot:footer>
				<div class="dialog-footer">
					<el-button @click="editDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="editProject">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { Search, Delete, Edit } from '@element-plus/icons-vue';
import { FootList, addProjectObj, putProjectObj, delProjectObj, searchProject } from '/@/api/gai/foot';
import { ref, onMounted, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
const PageContainer = defineAsyncComponent(() => import('/@/views/gai/components/PageContainer.vue'));
import { userList } from '/@/api/admin/user';
const input = ref('');
const dateRange = ref([]);
const dialogVisible = ref(false);
const editDialogVisible = ref(false);

interface Project {
	id: number;
	projectName: string;
	projectDescription: string;
	taskNum: number;
	projectType: string;
	createTime: string | null;
	updateTime: string;
	delFlag: string;
	username: string;
	updateBy: string;
}

const projects = ref<Project[]>([]);
const disabled = ref(false);
const background = ref(false);
const small = ref(false);
const totalItems = ref(0);
const currentPage = ref(1);
const pageSize = ref(8);

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	if ((input.value && input.value.trim() !== '') || (dateRange.value && dateRange.value.length === 2)) {
		search(); // 如果有输入内容或日期范围，调用 search
	} else {
		getProjectList(); // 否则调用 getProjectList
	}
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	if ((input.value && input.value.trim() !== '') || (dateRange.value && dateRange.value.length === 2)) {
		search(); // 如果有输入内容或日期范围，调用 search
	} else {
		getProjectList(); // 否则调用 getProjectList
	}
};

onMounted(async () => {
	getProjectList();
});
const getProjectList = async () => {
	try {
		const response = await FootList({
			current: currentPage.value,
			size: pageSize.value,
			projectType: '3',
		});
		const projectsData = response.data.records;
		totalItems.value = response.data.total;
		projects.value = projectsData;
	} catch (error) {
		console.error(error);
	}
};
//校验
const rules = ref({
	projectName: [
		{ required: true, message: '请输入项目名称', trigger: 'blur' },
		{ max: 25, message: '项目名称不能超过 25 个字', trigger: 'blur' },
	],
	projectDescription: [
		{ required: true, message: '请输入项目介绍', trigger: 'blur' },
		{ max: 80, message: '项目介绍不能超过 80 个字', trigger: 'blur' },
	],
});

const openDialog = () => {
	resetForm();
	dialogVisible.value = true;
};
const form = ref({
	id: 0,
	projectName: '',
	projectDescription: '',
	taskNum: 0,
	projectType: '3',
	updateBy: '',
});
const formCreateRef = ref();
const formEditRef = ref();

// 创建项目
const createProject = async () => {
	const isValid = await formCreateRef.value.validate();
	const result = await userList();
	const username = result.data.sysUser.username;
	form.value.taskNum = 0; // 设置默认任务数量
	form.value.projectType = '3'; // 设置默认项目类型
	form.value.updateBy = username;
	// const userResult = await userList();
	// const username = userResult.data.sysUser.username;
	if (isValid) {
		// 检查校验结果
		const result = await addProjectObj(form.value); //新增接口
		if (result.ok) {
			ElMessage.success('新增成功');
		} else {
			ElMessage.error('新增失败');
		}
		getProjectList();
		dialogVisible.value = false;
	} else {
		console.log('Validation failed. Please check your input.');
	}
};

const showEditDialog = (project) => {
	form.value = { ...project }; // 将当前项目的信息填充到编辑表单中
	editDialogVisible.value = true;
};
//编辑项目
const editProject = async () => {
	await formEditRef.value.validate();
	const reult = await putProjectObj(form.value);
	if (reult.ok) {
		ElMessage.success('项目已更新');
	} else {
		ElMessage.error('更新失败');
	}
	getProjectList();
	editDialogVisible.value = false;
};
//删除项目
const deleteProject = async (project) => {
	const result = await delProjectObj([project.id]);
	if (result.ok) {
		ElMessage.success('删除成功');
	} else {
		ElMessage.error('删除失败');
	}
	getProjectList();
};

const showDeleteConfirm = (project) => {
	ElMessageBox.confirm('此操作将永久删除该项目, 是否继续?', '警告', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deleteProject(project);
		})
		.catch(() => {
			// 用户取消操作
		});
};
// 截断项目介绍，限制长度为20个字符
const truncateDescription = (description) => {
	const maxWidth = 400; // 最大宽度，单位为像素
	const maxChars = Math.floor(maxWidth / 10); // 假设每个字符的宽度为 10 个像素（可以根据实际情况调整）
	if (description.length > maxChars) {
		return description.slice(0, maxChars) + '...';
	} else {
		return description;
	}
};
//跳转到鞋垫任务页面
const router = useRouter();
const goFootQuest = (project: Project) => {
	router.push({ path: '/InsolesQuest', query: { projectId: project.id.toString() } });
};

const handleResize = () => {
	// 处理输入框大小变化事件
};

//搜索方法
const search = async () => {
	// try {
	const formattedDateRange = (dateRange.value as Date[]).map((date) => date.toLocaleDateString('zh-CN'));
	// 检查input.value是否为空
	const projectName = input.value ? input.value : '';
	const startDate = formattedDateRange[0] ? formattedDateRange[0] + ' 00:00:00' : '';
	const endDate = formattedDateRange[1] ? formattedDateRange[1] + ' 23:59:59' : '';
	const userResult = await userList();
	const username = userResult.data.sysUser.username;
	const projectType = '3';
	const result = await searchProject(projectName, startDate, endDate, username, projectType, currentPage.value, pageSize.value);
	const projectsData = result.data.records;
	totalItems.value = result.data.total;
	projects.value = projectsData;
	console.log('projects.value', projects.value);
};

const formatDate = (dateString) => {
	const date = new Date(dateString);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
};
//重置方法
const reset = () => {
	input.value = ''; // 清空项目名称输入框
	dateRange.value = []; // 清空时间范围选择
	currentPage.value = 1;
	handleCurrentChange(1);
	getProjectList();
};

const resetForm = () => {
	Object.keys(form.value).forEach((key) => {
		form.value[key] = '';
	});
};

const shortcuts = [
	{
		text: '上个星期',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
			return [start, end];
		},
	},
	{
		text: '上个月',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
			return [start, end];
		},
	},
	{
		text: '上三个月',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
			return [start, end];
		},
	},
];
</script>

<style lang="scss" scoped>
.align {
	display: flex;
	align-items: center;
	padding: 0 30px;
}

.buttonStyle {
	background-color: #6463ff;
	color: azure;
}

.buttonStyle:hover {
	background-color: #a1a0ff;
}

.buttonStyle:active {
	background-color: #a1a0ff;
}

.bottonM {
	display: flex;
	align-items: center;
	margin-left: auto;
}

.bottonM .el-button {
	margin-left: 10px;
	border: none !important;
}

.toptext {
	font-family: Alibaba PuHuiTi 3;
	font-size: 20px;
	font-weight: 550;
	color: #333333;
	margin-left: 20px;
}

.spaceB {
	justify-content: space-between;

	.graycolor {
		background-color: #333333;
		color: #fff;
	}
}

.dialog-footer {
	text-align: center;
}

.content-wrapper {
	display: flex;
	justify-content: space-between;
}

.header-buttons {
	padding: 0 30px;
	margin-top: -5px;
}

.project-button {
	display: flex;
}

.project-info {
	flex: 9;
	padding: 10px;
}

.createT {
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: normal;
	color: #555555;
}

.createA {
	font-family: Alibaba PuHuiTi 3;
	font-size: 12px;
	font-weight: normal;
	line-height: 20px;
	color: #999999;
	margin-left: 30px;
}

.createB {
	font-family: Alibaba PuHuiTi 3;
	font-size: 13px;
	font-weight: normal;
	color: #555555;
	// margin-left: 30px;
	margin-top: 3px;
}

.task-count {
	flex: 1;
	padding: 10px;
	text-align: center;
	background-color: #f7f4fc;
}

.task-number {
	// font-size: 1.5em;
	font-family: Alibaba PuHuiTi 3;
	font-size: 36px;
	font-weight: normal;
	line-height: 22px;
	color: #6463ff;
	margin-top: 15px; // margin-right: 12px;
	cursor: pointer;
	/* 放大字体 */
}

@media (max-width: 700px) {
	.content-wrapper {
		flex-direction: column;
	}
}

.spacer {
	height: 10px;
	/* 设置间距高度 */
}

.project-description {
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.project-container {
	max-width: 100%;
	max-height: 700px;
	// height: 700vw;
	overflow-y: auto;
	overflow-x: hidden;
}
@media screen and (min-width: 1920px) {
	.project-container {
		height: 58vh; /* 在小屏幕下设置较小的高度 */
	}
}
@media screen and (max-width: 1760px) {
	.project-container {
		height: 58vh; /* 在小屏幕下设置较小的高度 */
	}
}

.project-name {
	font-family: Alibaba PuHuiTi 3;
	font-size: 18px;
	font-weight: 500;
}
.page-region {
	position: absolute;
	bottom: 10px;
	right: 10px;
}
.empty {
	margin-top: 200px;
}
</style>
