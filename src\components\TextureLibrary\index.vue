<template>
	<el-dialog
		v-model="visible"
		title="纹理通用库"
		width="1200"
		draggable
		:append-to-body="true"
		class="custom-dialog"
		:close-on-click-modal="true"
		overflow
	>
		<div class="dialog-content">
			<div class="top-degin">
				<el-button :class="{ active: commonFlag }" @click="setFlag('common')">通用库</el-button>
				<el-button :class="{ active: privateFlag }" @click="setFlag('private')">私有库</el-button>
			</div>
			<div class="dialog-main-content">
				<div class="left-section">
					<div class="group-container">
						<div class="group-header">
							<h4>纹理分组</h4>
							<el-button type="primary" :icon="Plus" @click="addGroup" v-if="privateFlag" size="small" circle />
						</div>
						<div class="group-list">
							<div class="all-groups" :class="{ 'is-active': selectedGroup === '全部' }" @click="selectTreeGroup('全部')">全部</div>
							<div v-if="treeGroups && treeGroups.length > 0">
								<el-tree
									ref="treeRef"
									:data="treeGroups"
									node-key="id"
									:props="{ children: 'children', label: 'groupName' }"
									:expand-on-click-node="false"
									:current-node-key="selectedGroupId"
									highlight-current
									@node-click="handleNodeClick"
									class="group-tree"
								>
									<template #default="{ node, data }">
										<span class="custom-tree-node">
											<span class="node-label">{{ data.groupName }}</span>
											<span class="actions" v-if="privateFlag">
												<el-button type="primary" link :icon="Edit" @click.stop="editGroup(data)" size="small" />
												<el-button type="primary" link :icon="Delete" @click.stop="deleteGroup(data)" size="small" />
											</span>
										</span>
									</template>
								</el-tree>
							</div>
							<div v-else class="no-groups">
								<p v-if="privateFlag">暂无私有分组</p>
								<p v-else>暂无通用分组</p>
							</div>
						</div>
					</div>
				</div>
				<div class="center-section">
					<div class="image-gallery">
						<div class="gallery-grid">
							<div v-if="isLoading" v-loading="isLoading" class="center"></div>
							<div v-for="(row, rowIndex) in imageRows" :key="rowIndex" class="gallery-row" v-else>
								<div
									v-for="(item, imageIndex) in row"
									:key="imageIndex"
									class="image-container"
									@mouseover="hoveringIndex = rowIndex + '-' + imageIndex"
									@mouseleave="hoveringIndex = null"
									@click="selectImage(item.imageUrl)"
								>
									<img :src="item.imageUrl" class="gallery-image" :class="{ selected: item.imageUrl === selectedImage }" />
									<div class="filename-style">
										<span class="file-name">{{ item.textureName }}</span>
									</div>
									<el-button
										class="update-buttonF"
										@click="editTexture(item.id)"
										:icon="Edit"
										type="text"
										v-if="privateFlag && hoveringIndex === rowIndex + '-' + imageIndex"
									></el-button>
									<el-button
										class="delete-buttonF"
										:icon="Delete"
										type="text"
										@click.stop="handleDeleteTexture(item)"
										v-if="privateFlag && hoveringIndex === rowIndex + '-' + imageIndex"
									>
									</el-button>
								</div>
							</div>
						</div>
						<el-empty description="暂无数据" class="desc-style" v-if="!isLoading && !imageRows.length" />
					</div>
					<el-pagination
						@current-change="handlePageChange"
						:current-page="page.current"
						:page-size="page.size"
						:total="totalItems"
						layout="total, prev, pager, next"
						class="pagina-tyle"
					/>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<div class="search-container">
					<el-input v-model="searchQuery" placeholder="请输入纹理名称搜索" clearable @clear="search" @keyup.enter="search" />
				</div>
				<el-button class="search-style" type="primary" @click="search">搜索</el-button>
				<el-button class="reset-style" @click="reset">重置</el-button>
				<el-button class="add-style" type="primary" @click="addTexture" v-if="privateFlag">新增</el-button>
				<el-button @click="handleCancel">取消</el-button>
				<el-button type="primary" @click="handleConfirm"> 确认 </el-button>
			</div>
		</template>

		<!-- 新增/编辑弹窗 -->
		<texture-form ref="textureDialogRef" @refresh="fetchData" :groups="treeGroups" />
		<group-form ref="groupDialogRef" @refresh="fetchGroups" :groups="textureGroups" />
	</el-dialog>
</template>

<script setup lang="ts" name="TextureLibrary">
import { ref, computed, watch, defineAsyncComponent } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete, Edit, Plus } from '@element-plus/icons-vue';
import { searchTexture, deleteTexture, getTextureGroups, deleteTextureGroup } from '/@/api/gai/texture';

const TextureForm = defineAsyncComponent(() => import('./textureDialog.vue'));
const GroupForm = defineAsyncComponent(() => import('./groupDialog.vue'));

// 定义props
interface Props {
	modelValue: boolean;
	userId?: number;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: false,
	userId: 1,
});

// 定义emits
const emit = defineEmits<{
	'update:modelValue': [value: boolean];
	confirm: [data: { url: string; fileName: string }];
}>();

// 响应式数据
const visible = ref(false);
const isLoading = ref(false);
const commonFlag = ref(true);
const privateFlag = ref(false);
const selectedImage = ref<string | null>(null);
const selectedTextureItem = ref<any>(null);
const hoveringIndex = ref<string | null>(null);
const selectedGroup = ref('全部');
const selectedGroupId = ref<string | null>(null);
const searchQuery = ref('');

const imagesWithFiles = ref<any[]>([]);
const totalItems = ref(0);

const page = ref({
	current: 1,
	size: 9,
});

const textureDialogRef = ref();
const groupDialogRef = ref();
const treeRef = ref();
const userIds = computed(() => props.userId);
const textureGroups = ref<any[]>([]);

// 计算树形结构的分组数据
const treeGroups = computed(() => {
	if (!textureGroups.value || textureGroups.value.length === 0) {
		return [];
	}

	const buildTree = (groups: any[], parentId: string | null = null): any[] => {
		const filtered = groups.filter((group) => {
			if (parentId === null) {
				return !group.parentId || group.parentId === '' || group.parentId === null;
			}
			return group.parentId === parentId;
		});

		return filtered.map((group) => ({
			...group,
			children: buildTree(groups, group.id),
		}));
	};

	const result = buildTree(textureGroups.value);
	return result;
});

// 计算属性
const imageRows = computed(() => {
	const rows: any[][] = [];
	for (let i = 0; i < imagesWithFiles.value.length; i += 3) {
		rows.push(imagesWithFiles.value.slice(i, i + 3));
	}
	return rows;
});

// 监听visible变化
watch(
	() => props.modelValue,
	(val) => {
		visible.value = val;
		if (val) {
			openDialog();
		}
	},
	{ immediate: true }
);

watch(visible, (val) => {
	emit('update:modelValue', val);
});

// 方法
const setFlag = async (type: string) => {
	selectedImage.value = null;
	selectedGroup.value = '全部';

	if (type === 'common') {
		commonFlag.value = true;
		privateFlag.value = false;
	} else if (type === 'private') {
		commonFlag.value = false;
		privateFlag.value = true;
	}

	// 强制清空现有分组数据
	textureGroups.value = [];

	// 重新获取分组数据
	await fetchGroups();

	// 获取纹理数据
	await fetchData();
};

const openDialog = async () => {
	commonFlag.value = true;
	privateFlag.value = false;
	selectedImage.value = null;
	selectedTextureItem.value = null;
	selectedGroup.value = '全部';
	await fetchGroups();
	await fetchData();
};

const fetchGroups = async () => {
	const params = { userId: privateFlag.value ? userIds.value : 1 };

	const res: any = await getTextureGroups(params);

	if (res.ok) {
		textureGroups.value = res.data;
	} else {
		console.error('fetchGroups - failed:', res);
	}
};

const addGroup = () => {
	groupDialogRef.value.openDialog({}, userIds.value);
};

const editGroup = (group: any) => {
	groupDialogRef.value.openDialog(group, userIds.value);
};

const deleteGroup = async (group: any) => {
	try {
		await ElMessageBox.confirm(`确定要删除分组 "${group.groupName}" 吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		const result: any = await deleteTextureGroup(group.id);
		if (result.ok) {
			ElMessage.success('删除成功');
			await fetchGroups();
		}
	} catch (error) {
		// User cancelled
	}
};

const fetchData = async () => {
	isLoading.value = true;
	try {
		const queryParams: any = {};
		if (commonFlag.value) {
			queryParams.userId = 1;
		} else {
			queryParams.userId = userIds.value;
		}

		if (selectedGroup.value !== '全部') {
			queryParams.group = selectedGroup.value;
		}

		if (searchQuery.value) {
			queryParams.textureName = searchQuery.value;
		}

		const result: any = await searchTexture(page.value, queryParams);
		if (result.ok && result.data) {
			totalItems.value = result.data.total;
			imagesWithFiles.value = result.data.records;
			if (imagesWithFiles.value.length > 0 && !selectedImage.value) {
				selectImage(imagesWithFiles.value[0].imageUrl);
			}
		}
	} catch (error) {
		ElMessage.error('获取数据失败');
	} finally {
		isLoading.value = false;
	}
};

const selectImage = (imageUrl: string) => {
	const selectedItem = imagesWithFiles.value.find((item) => item.imageUrl === imageUrl);
	if (selectedItem) {
		selectedImage.value = imageUrl;
		selectedTextureItem.value = selectedItem;
	}
};

const handlePageChange = async (newPage: number) => {
	page.value.current = newPage;
	selectedImage.value = null;
	await fetchData();
};

const handleNodeClick = (data: any) => {
	selectedGroupId.value = data.id;
	selectedGroup.value = data.groupName;
	page.value.current = 1;
	fetchData();
};

const selectTreeGroup = (groupName: string) => {
	selectedGroup.value = groupName;
	selectedGroupId.value = null;
	page.value.current = 1;
	fetchData();

	if (groupName === '全部' && treeRef.value) {
		treeRef.value.setCurrentKey(null);
	}
};

const search = async () => {
	page.value.current = 1;
	await fetchData();
};

const reset = async () => {
	selectedGroup.value = '全部';
	searchQuery.value = ''; // 清空搜索框
	page.value.current = 1;
	await fetchData();
};

const editTexture = (id: string) => {
	textureDialogRef.value.openDialog(id, userIds.value);
};

const handleDeleteTexture = async (item: any) => {
	try {
		await ElMessageBox.confirm(`确定要删除纹理 "${item.textureName}" 吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		const result: any = await deleteTexture([item.id]);
		if (result.ok) {
			ElMessage.success('删除成功');
			await fetchData();
		}
	} catch (error) {
		// User cancelled
	}
};

const addTexture = () => {
	textureDialogRef.value.openDialog('', userIds.value);
};

const handleCancel = () => {
	visible.value = false;
};

const handleConfirm = () => {
	if (!selectedImage.value) {
		ElMessage.warning('请选择一个纹理');
		return;
	}

	if (selectedTextureItem.value) {
		emit('confirm', {
			url: selectedTextureItem.value.imageUrl,
			fileName: selectedTextureItem.value.textureName,
		});
		visible.value = false;
	}
};

// 暴露方法给父组件
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
// Styles are copied from InsolesLibrary to maintain consistency
.custom-dialog .el-dialog__wrapper {
	position: fixed;
	top: 100px;
	right: 100px;
	bottom: auto;
	left: auto;
	height: 500px;
}
.dialog-content {
	height: 580px;
	margin-top: -10px;
}
.el-overlay {
	display: none; /* Hide the overlay */
}

.left-section {
	flex: 3;
	padding: 20px;
	margin-left: -20px;
	border-right: 1px solid #ddd;
}

.center-section {
	flex: 7;
	padding: 16px;
	width: 100%;
}

.dialog-footer {
	text-align: right;
}
.search-style {
	margin-right: 750px;
}
.reset-style {
	left: 290px;
	position: absolute;
}
.add-style {
	left: 370px;
	position: absolute;
}

.group-container {
	min-width: 150px;
	padding: 10px;
}

.group-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}

.group-header h4 {
	margin: 0;
}

/* Style for the "All" button */
.all-groups {
	padding: 6px 12px;
	cursor: pointer;
	border-radius: 4px;
	font-size: 14px;
	margin-bottom: 5px;
}
.all-groups.is-active {
	background-color: #ecf5ff;
	color: #409eff;
}

/* This is the main custom class for each node in the tree */
.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	width: 100%;
}

.node-label {
	flex-grow: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* Hide actions by default */
.custom-tree-node .actions {
	display: none;
	flex-shrink: 0;
}

/* Show actions on hover */
.el-tree-node__content:hover .actions {
	display: block;
}

/* Remove default padding from tree content and set height */
:deep(.el-tree-node__content) {
	padding: 0 8px !important;
	height: 32px;
}

/* Set a clean hover background color */
:deep(.el-tree-node__content:hover) {
	background-color: #f5f7fa;
}

/* Style for the currently selected node */
:deep(.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content) {
	background-color: #ecf5ff;
	color: #409eff;
	font-weight: bold;
}

/* Make sure the action buttons in the selected node are also blue */
:deep(.el-tree-node.is-current .actions .el-button) {
	color: #409eff;
}

.image-gallery {
	display: flex;
	flex-direction: column;
}

.gallery-grid {
	display: flex;
	flex-direction: column;
	gap: 10px;
}
.gallery-row {
	display: flex;
	gap: 50px;
}
.gallery-image {
	width: 200px;
	height: 150px;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
}
.gallery-image.selected {
	border: 2px solid #007bff;
}
.pagina-tyle {
	position: absolute;
	top: 200px;
	right: 0;
}
.top-degin {
	display: flex;
	justify-content: center;
	width: 1000px;
	position: absolute;
	top: 30px;
}
.el-button.active {
	background-color: #6463ff;
	color: white;
}
.center {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 300px;
}
.image-container {
	position: relative;
	display: inline-block;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
	width: 200px;
	text-align: center;
}

.delete-buttonF {
	position: absolute;
	top: -15px;
	right: 1px;
}
.update-buttonF {
	position: absolute;
	top: -15px;
	right: 17px;
}

.desc-style {
	display: flex;
	justify-content: center;
	align-content: center;
	margin-top: 200px;
	margin-left: 100px;
}
.filename-style {
	display: inline-block;
}
.file-name {
	width: 200px;
	display: flex;
	justify-content: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-top: 5px;
}

.search-container {
	margin-bottom: -32px;
	width: 200px;
	left: 150px;
}

.dialog-main-content {
	display: flex;
	justify-content: center;
}

.no-groups {
	padding: 10px;
	text-align: center;
	color: #999;
	font-size: 14px;
}
</style>
