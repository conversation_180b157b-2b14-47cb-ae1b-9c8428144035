<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" :title="dataForm.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible">
			<el-form :model="dataForm" :rules="dataRules" label-width="90px" ref="dataFormRef" v-loading="loading">
				<el-row :gutter="20">
					<el-col :span="12" class="mb20">
						<el-form-item label="文件名称" prop="fileName">
							<el-input v-model="dataForm.fileName" placeholder="请输入文件名称" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="类别" prop="category">
							<el-select v-model="dataForm.category" placeholder="请选择类别">
								<el-option v-for="item in DropDownData.category" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="肥瘦度" prop="weightIndex">
							<el-select v-model="dataForm.weightIndex" placeholder="请选择肥瘦度">
								<el-option v-for="item in DropDownData.weightIndex" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="足弓" prop="footarch">
							<el-select v-model="dataForm.footarch" placeholder="请选择足弓">
								<el-option v-for="item in DropDownData.footarch" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="年龄" prop="age">
							<el-input v-model="dataForm.age" placeholder="请输入年龄" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="脚长(mm)" prop="headShape">
							<el-input v-model="dataForm.footLength" placeholder="请输入脚长" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="脚宽" prop="shoeSize">
							<el-input v-model="dataForm.footWidth" placeholder="请输入脚宽" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="高脚背" prop="shoeShape">
							<el-select v-model="dataForm.heightInstep" placeholder="请选择">
								<el-option v-for="item in DropDownData.heightInstep" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="后跟直" prop="style">
							<el-select v-model="dataForm.heelStraight" placeholder="请选择">
								<el-option v-for="item in DropDownData.heelStraight" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="拇外翻" prop="style">
							<el-select v-model="dataForm.halluxValgus" placeholder="请选择">
								<el-option v-for="item in DropDownData.halluxValgus" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="脚型" prop="footShape">
							<el-input v-model="dataForm.footShape" placeholder="请输入脚型" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="跖围" prop="bottomGirth">
							<el-input v-model="dataForm.bottomGirth" placeholder="请输入跖围" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="文件" prop="file">
							<el-upload
								class="upload-demo"
								:action="uploadUrl"
								:show-file-list="false"
								:auto-upload="false"
								:on-change="(file) => handleSuccess(file)"
							>
								<el-button type="primary">上传文件</el-button>
							</el-upload>
							<div v-if="fileName" class="file-name">文件名称: {{ fileName }}</div>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="图片" prop="image">
							<el-upload
								ref="uploadRef"
								:auto-upload="false"
								class="avatar-uploader"
								:show-file-list="false"
								:on-change="(file) => onSelectFile(imageFile, file)"
								:action="imageFile.image"
							>
								<img v-if="imageFile.imgFile" :src="imageFile.imgFile" class="avatar" />
								<img v-else-if="imageFile.image" :src="imageFile.image" class="avatar" />
								<el-icon v-else class="avatar-uploader-icon">
									<Plus />
								</el-icon>
							</el-upload>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
					<el-button @click="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { updateByIdFootShape, getFootShapeById, saveFootShape, uploadObj, getDataType } from '/@/api/gai/foot';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { ElMessage } from 'element-plus';
import { userList } from '/@/api/admin/user';
const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const fileName = ref();
const uploadUrl = ref();
const fileUrl = ref();

const imageFile = ref({
	image: '',
	imgFile: '',
});

const dataForm = ref({
	id: '',
	userId: '',
	category: '',
	weightIndex: '',
	footarch: '',
	age: '',
	footLength: '',
	footWidth: '',
	heightInstep: '',
	heelStraight: '',
	halluxValgus: '',
	file: '',
	image: '',
	fileName: '',
	footShape: '',
	bottomGirth: '',
});

const dataRules = ref({
	category: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
	fileName: [
		{ required: true, message: '文件名称不能为空', trigger: 'blur' },
		{ max: 15, message: '文件名称不能超过15个字符', trigger: 'change' },
	],
});
const userIds = ref();
const typeData = ref();
const DropDownData = ref({
	category: [],
	weightIndex: [],
	footarch: [],
	age: [],
	footLength: [],
	footWidth: [],
	heightInstep: [],
	heelStraight: [],
	halluxValgus: [],
});
// 打开弹窗
const openDialog = async (id: string, userId?: number) => {
	visible.value = true; // 先显示对话框
	loading.value = true; // 显示加载状态

	// 重置表单数据
	dataForm.value = {
		id: '',
		userId: '',
		category: '',
		weightIndex: '',
		footarch: '',
		age: '',
		footLength: '',
		footWidth: '',
		heightInstep: '',
		heelStraight: '',
		halluxValgus: '',
		file: '',
		image: '',
		fileName: '',
		footShape: '',
		bottomGirth: '',
	};
	imageFile.value.image = '';
	fileName.value = '';
	// const type = '1';

	try {
		// console.log('ID value before API call:', id);
		const [userResult, typeResult] = await Promise.all([userList(), getDataType('1')]);

		// 如果传入了userId参数，使用传入的值；否则使用API返回的值
		userIds.value = userId || userResult.data.sysUser.userId;
		typeData.value = typeResult.data;

		typeData.value.forEach((item) => {
			if (item.children) {
				DropDownData.value[item.ename] = item.children;
			}
		});

		if (id) {
			const result = await getFootShapeById(id);
			imageFile.value.image = result.data.image;
			fileName.value = result.data.file.split('/').pop();
			fileUrl.value = result.data.file;

			dataForm.value.file = fileUrl.value;
			dataForm.value.image = imageFile.value.image;
			Object.assign(dataForm.value, result.data);
			console.log('result.data', result.data);
		}
	} catch (error) {
		console.error('Error loading data:', error);
	} finally {
		loading.value = false; // 隐藏加载状态
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		const { id } = dataForm.value;

		//检查必填文件和图片
		if (!fileUrl.value) {
			ElMessage.error('请上传必要的文件！');
			return;
		}
		if (!imageFile.value.image) {
			ElMessage.error('请上传必要的图片');
			return;
		}

		loading.value = true;
		dataForm.value.file = fileUrl.value; // 赋值文件
		dataForm.value.image = imageFile.value.image; // 赋值图片
		dataForm.value.userId = userIds.value;

		if (id) {
			await updateByIdFootShape(dataForm.value);
			useMessage().success(t('common.editSuccessText'));
		} else {
			await saveFootShape(dataForm.value);
			useMessage().success(t('common.addSuccessText'));
		}

		visible.value = false; // 关闭弹窗
		emit('refresh');
	} catch (error: any) {
		useMessage().error(error.msg);
	} finally {
		loading.value = false;
	}
};

const uploadRef = ref();

const onSelectFile = (imageFile, file) => {
	// 检查文件类型
	const fileType = file.raw.type;
	const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml'];

	if (!validImageTypes.includes(fileType)) {
		ElMessage({
			message: '请上传正确的图片格式！',
			type: 'warning',
		});
		return;
	}

	// 基于 FileReader 读取图片做预览
	const reader = new FileReader();
	reader.onload = (e) => {
		imageFile.image = e?.target?.result;

		const formData = new FormData();
		formData.append('mouldFile', file.raw);

		uploadObj(formData)
			.then((response) => {
				imageFile.image = response.data;
			})
			.catch((error) => {
				ElMessage({
					message: '上传失败，请重试！',
					type: 'info',
				});
				return;
			});
	};

	reader.readAsDataURL(file.raw);
};

const handleSuccess = async (file: any) => {
	// 检查文件类型
	const validFileExtensions = ['obj', 'stl', '3dm', 'stp', 'step', 'mtl'];
	const fileExtension = file.name.split('.').pop().toLowerCase();

	if (!validFileExtensions.includes(fileExtension)) {
		ElMessage({
			message: '请上传正确的图片格式！，例如：.obj, .stl, .3dm, .stp, .step, .mtl',
			type: 'warning',
		});
		return;
	}

	// 创建 FormData 并上传文件
	const formData = new FormData();
	formData.append('mouldFile', file.raw);

	try {
		const result = await uploadObj(formData);
		if (result.data) {
			fileUrl.value = result.data;
			fileName.value = file.name;
		}
	} catch (error) {
		ElMessage({
			message: '上传失败，请重试！',
			type: 'info',
		});
	}
};

// const getUserData = async (id: string) => {
// 	try {
// 		loading.value = true;
// 		const { data } = await getObj(id);
// 		Object.assign(dataForm, data);
// 		if (data.roleList) {
// 			dataForm.role = data.roleList.map((item) => item.roleId);
// 		}
// 		if (data.postList) {
// 			dataForm.post = data.postList.map((item) => item.postId);
// 		}
// 	} catch (err: any) {
// 		useMessage().error(err.msg);
// 	} finally {
// 		loading.value = false;
// 	}
// };

// // 初始化部门数据
// const getDeptData = () => {
// 	// 获取部门数据
// 	deptTree().then((res) => {
// 		deptData.value = res.data;
// 		// 默认选择第一个
// 		if (!dataForm.userId) {
// 			dataForm.deptId = res.data[0].id;
// 		}
// 	});
// };

// // 岗位数据
// const getPostData = () => {
// 	postList().then((res) => {
// 		postData.value = res.data;
// 		// 默认选择第一个
// 		if (!dataForm.userId) {
// 			dataForm.post = [res.data[0].postId];
// 		}
// 	});
// };
// // 角色数据
// const getRoleData = () => {
// 	roleList().then((res) => {
// 		roleData.value = res.data;
// 		// 默认选择第一个
// 		if (!dataForm.userId) {
// 			dataForm.role = [res.data[0].roleId];
// 		}
// 	});
// };

// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.avatar-uploader {
	:deep() {
		.avatar {
			width: 180px;
			height: 125px;
			display: block;
		}

		.el-upload {
			border: 1px dashed var(--el-border-color);
			border-radius: 6px;
			cursor: pointer;
			position: relative;
			overflow: hidden;
			transition: var(--el-transition-duration-fast);
		}

		.el-upload:hover {
			border-color: var(--el-color-primary);
		}

		.el-icon.avatar-uploader-icon {
			font-size: 28px;
			color: #8c939d;
			width: 180px;
			height: 125px;
			text-align: center;
		}
	}
}
.file-name {
	margin-top: 10px;
	color: #409eff;
}
.upload-demo .el-upload {
	display: inline-block;
}
</style>
