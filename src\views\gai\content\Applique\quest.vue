<template>
	<div>
		<div class="flex spaceB">
			<div class="toptext flex"><img src="/@/assets/home.png" class="imgS" @click="goHome" />/ &nbsp;&nbsp;任务列表</div>
			<el-button class="graycolor" @click="openDialog">+ &nbsp;&nbsp;新建任务</el-button>
			<el-dialog v-model="dialogVisible" title="新建任务" width="500px">
				<!-- 项目名称输入框 -->
				<el-form label-width="100px" :model="form" :rules="rules" ref="formCreateRef">
					<el-form-item label="任务名称" prop="taskName">
						<el-input v-model="form.taskName" placeholder="请输入任务名称，25个字以内"></el-input>
					</el-form-item>
				</el-form>
				<!-- 确认和取消按钮 -->
				<template v-slot:footer>
					<span class="dialog-footer">
						<el-button @click="dialogVisible = false">取消</el-button>
						<el-button type="primary" @click="createProject">确认</el-button>
					</span>
				</template>
			</el-dialog>
		</div>
		<page-container title="任务名称">
			<!-- 搜索栏 -->
			<template #button>
				<div class="align">
					<el-input v-model="input" style="width: 240px" placeholder="请输入任务名称" />
					<p class="align">日期范围</p>
					<div class="block">
						<el-date-picker
							v-model="dateRange"
							type="daterange"
							unlink-panels
							range-separator="-"
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							:shortcuts="shortcuts"
						/>
					</div>
					<div class="bottonM">
						<el-button @click="search" class="buttonStyle" :icon="Search">搜索</el-button>
						<el-button @click="reset" class="buttonStyle">
							<el-icon :size="35"> <Refresh /> </el-icon>&nbsp;&nbsp;重置
						</el-button>
					</div>
				</div>
			</template>
		</page-container>
		<!-- 显示方块内容 -->
		<div class="project-container" v-if="projects.length > 0">
			<el-row :gutter="20">
				<el-col :span="6" v-for="project in projects" :key="project.id">
					<el-card class="box-card" @mouseenter="toggleDeleteButton(true, project.id)" @mouseleave="toggleDeleteButton(false, project.id)">
						<img src="/@/assets/Preview.png" @click="goCompute(project)" />
						<div class="content-wrapper">
							<div class="project-info">
								<!-- <div class="delete-wrapper">
									<el-button
										type="danger"
										circle
										@click="showDeleteConfirm(project)"
										class="deleteStyle"
										:style="{ display: deleteButtonVisible[project.id] ? 'block' : 'none' }"
									>
										<el-icon :size="20" class="closeS">
											<Close />
										</el-icon>
									</el-button>
								</div> -->
								<div class="project-button">
									<span class="project-name ellipsis" @click="goCompute(project)">{{ project.taskName }}</span>
									<div class="header-buttons">
										<el-button type="text" @click="showEditDialog(project)" :icon="Edit"></el-button>
										<el-button type="text" @click="showDeleteConfirm(project)" :icon="Delete"></el-button>
									</div>
								</div>
								<div class="flex">
									<p class="createT">{{ project.updateBy }}</p>
									<p class="createA">{{ formatDate(project.createTime) }}</p>
								</div>
							</div>
						</div>
					</el-card>
					<div v-if="index !== projects.length - 1" class="spacer"></div>
				</el-col>
			</el-row>
			<el-pagination
				v-model:current-page="currentPage"
				v-model:page-size="pageSize"
				:page-sizes="[8, 16, 32, 64]"
				:small="small"
				:disabled="disabled"
				:background="background"
				layout="total, sizes, prev, pager, next, jumper"
				:total="totalItems"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
				class="page-region"
			/>
		</div>
		<template v-else>
			<div class="empty">
				<el-empty description="暂无数据"></el-empty>
			</div>
		</template>

		<el-dialog title="编辑项目" v-model="editDialogVisible" style="width: 500px">
			<el-form :model="form" :rules="rules" label-width="80px" ref="formEditRef">
				<el-form-item label="项目名称" prop="taskName">
					<el-input v-model="form.taskName"></el-input>
				</el-form-item>
			</el-form>
			<template v-slot:footer>
				<div class="dialog-footer">
					<el-button @click="editDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="editProject">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { Search, Delete, Edit } from '@element-plus/icons-vue';
import { getTaskObj, addTaskObj, putTaskObj, delTaskObj, searchTask } from '/@/api/gai/foot';
import { ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { userList } from '/@/api/admin/user';
import { update } from 'lodash';
const PageContainer = defineAsyncComponent(() => import('/@/views/gai/components/PageContainer.vue'));
const input = ref('');
const dateRange = ref([]);
const dialogVisible = ref(false);
const editDialogVisible = ref(false);
const projectId1 = ref();
const route = useRoute();
onMounted(() => {
	// window.location.reload();
	const projectId = route.query.projectId;
	projectId1.value = projectId;
	getProjectList();
});
interface Project {
	id: number;
	taskName: string;
	taskType: string;
	createTime: string | null;
	updateTime: string;
	delFlag: string;
	username: string;
	taskId: string;
	updateBy: string;
}
const projects = ref<Project[]>([]);
const disabled = ref(false);
const background = ref(false);
const small = ref(false);
const totalItems = ref(0);
const currentPage = ref(1);
const pageSize = ref(8);

const handleSizeChange = async (val: number) => {
	pageSize.value = val;
	if ((input.value && input.value.trim() !== '') || (dateRange.value && dateRange.value.length === 2)) {
		search(); // 如果有输入内容或日期范围，调用 search
	} else {
		getProjectList(); // 否则调用 getProjectList
	}
};
const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	if ((input.value && input.value.trim() !== '') || (dateRange.value && dateRange.value.length === 2)) {
		search(); // 如果有输入内容或日期范围，调用 search
	} else {
		getProjectList(); // 否则调用 getProjectList
	}
};

const getProjectList = async () => {
	try {
		const taskType = '5';
		const response = await getTaskObj(projectId1.value, taskType, currentPage.value, pageSize.value);
		const projectsData = response.data.records;
		totalItems.value = response.data.total;
		projects.value = projectsData;
	} catch (error) {
		console.error(error);
	}
};

const router = useRouter();
const goHome = () => {
	router.push('/Applique');
	// router.go(-1)
};

//去计算页面
const goCompute = (project) => {
	router.push({
		path: '/AppliqueCompute',
		query: {
			projectId: project.id.toString(),
			taskName: project.taskName.toString(),
			taskId: project.taskId.toString(),
		},
	});
};

//校验
const rules = ref({
	taskName: [
		{ required: true, message: '请输入项目名称', trigger: 'blur' },
		{ max: 25, message: '任务名称不能超过 25 个字', trigger: 'blur' },
	],
});

const openDialog = () => {
	resetForm();
	dialogVisible.value = true;
};
// const props = defineProps({
//   projects: Array, // 接收项目列表作为属性
//   editDialogVisible: Boolean // 接收编辑对话框可见性作为属性
// });
// const form = reactive({
//   taskName: '',
// });
const form = ref({
	id: 0,
	taskName: '',
	taskId: 0,
	projectType: '5',
	updateBy: '',
});
const formCreateRef = ref();
const formEditRef = ref();

// 创建项目
const createProject = async () => {
	const isValid = await formCreateRef.value.validate(); // 等待表单校验完成并获取校验结果
	const result = await userList();
	const username = result.data.sysUser.username;
	form.value.taskId = projectId1.value; // 设置默认任务数量
	form.value.projectType = '5'; // 设置默认项目类型
	form.value.updateBy = username;

	if (isValid) {
		// 检查校验结果
		const result = await addTaskObj(form.value); //新增接口
		if (result.ok) {
			ElMessage.success('新增成功');
		} else {
			ElMessage.error('新增失败');
		}
		getProjectList();
		dialogVisible.value = false;
	} else {
		console.log('Validation failed. Please check your input.');
	}
};

const showEditDialog = (project) => {
	form.value = { ...project }; // 将当前项目的信息填充到编辑表单中
	editDialogVisible.value = true;
};
//编辑项目
const editProject = async () => {
	await formEditRef.value.validate();
	form.value.taskId = form.value.id;
	const reult = await putTaskObj(form.value);
	console.log('----------------', form.value);
	if (reult.ok) {
		ElMessage.success('任务已更新');
	} else {
		ElMessage.error('更新失败');
	}
	getProjectList();
	editDialogVisible.value = false;
};
//删除项目
const deleteProject = async (project) => {
	const result = await delTaskObj([project.id]);
	if (result.ok) {
		ElMessage.success('删除成功');
	} else {
		ElMessage.error('删除失败');
	}
	getProjectList();
};

const showDeleteConfirm = (project) => {
	ElMessageBox.confirm('此操作将永久删除该项目, 是否继续?', '警告', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deleteProject(project);
		})
		.catch(() => {
			// 用户取消操作
		});
};
// 截断项目介绍，限制长度为20个字符
const truncateDescription = (description) => {
	const maxWidth = 400; // 最大宽度，单位为像素
	const maxChars = Math.floor(maxWidth / 10); // 假设每个字符的宽度为 10 个像素（可以根据实际情况调整）
	if (description.length > maxChars) {
		return description.slice(0, maxChars) + '...';
	} else {
		return description;
	}
};

const handleResize = () => {
	// 处理输入框大小变化事件
};

//搜索方法
const search = async () => {
	const formattedDateRange = (dateRange.value as Date[]).map((date) => date.toLocaleDateString('zh-CN'));
	// 检查input.value是否为空
	const taskName = input.value ? input.value : '';
	const startDate = formattedDateRange[0] ? formattedDateRange[0] + ' 00:00:00' : '';
	const endDate = formattedDateRange[1] ? formattedDateRange[1] + ' 23:59:59' : '';
	const userResult = await userList();
	const username = userResult.data.sysUser.username;
	const taskType = '5';
	const result = await searchTask(taskName, startDate, endDate, username, projectId1.value, taskType, currentPage.value, pageSize.value);
	const projectsData = result.data.records;
	totalItems.value = result.data.total;
	projects.value = projectsData;
};
const formatDate = (dateString) => {
	const date = new Date(dateString);
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
};
//重置方法
const reset = () => {
	input.value = ''; // 清空项目名称输入框
	dateRange.value = []; // 清空时间范围选择
	currentPage.value = 1;
	handleCurrentChange(1);
	getProjectList();
};

const resetForm = () => {
	Object.keys(form.value).forEach((key) => {
		form.value[key] = '';
	});
};

const deleteButtonVisible = ref({});

const toggleDeleteButton = (show, projectId) => {
	deleteButtonVisible.value[projectId] = show;
};
const shortcuts = [
	{
		text: '上个星期',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
			return [start, end];
		},
	},
	{
		text: '上个月',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
			return [start, end];
		},
	},
	{
		text: '上三个月',
		value: () => {
			const end = new Date();
			const start = new Date();
			start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
			return [start, end];
		},
	},
];
</script>

<style lang="scss" scoped>
.align {
	display: flex;
	align-items: center;
	padding: 0 30px;
}

.buttonStyle {
	background-color: #6463ff;
	color: azure;
}

.buttonStyle:hover {
	background-color: #a1a0ff;
}

.buttonStyle:active {
	background-color: #a1a0ff;
}

.bottonM {
	display: flex;
	align-items: center;
	margin-left: auto;
}

.bottonM .el-button {
	margin-left: 10px;
	/* 调整按钮之间的间距 */
	border: none !important;
	/* 移除按钮的边框 */
}

.toptext {
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 550;
	color: #333333;
	margin-left: 20px;
	// width: 100px;
}

.imgS {
	width: 18px;
	height: 18px;
	margin-top: -1.5px;
	cursor: pointer;
	margin-right: 4px;
}

.spaceB {
	justify-content: space-between;

	.graycolor {
		background-color: #333333;
		color: #fff;
	}
}

.dialog-footer {
	text-align: center;
}

.content-wrapper {
	display: flex;
	justify-content: space-between;
}

.header-buttons {
	// position: relative;
	// margin-right: 20px;
	// margin-left: 70px;
	padding: 0 10px;
	margin-top: -5px;
}

.project-button {
	display: flex;
}

.project-info {
	flex: 1;
	// padding: 10px;
}

.box-card {
	max-width: 350px;
}

.createT {
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: normal;
	color: #555555;
}

.createA {
	font-family: Alibaba PuHuiTi 3;
	font-size: 12px;
	font-weight: normal;
	line-height: 20px;
	color: #999999;
	margin-left: 30px;
}

.createB {
	font-family: Alibaba PuHuiTi 3;
	font-size: 13px;
	font-weight: normal;
	color: #555555;
	// margin-left: 30px;
	margin-top: 3px;
}

.task-number {
	// font-size: 1.5em;
	font-family: Alibaba PuHuiTi 3;
	font-size: 36px;
	font-weight: normal;
	line-height: 22px;
	color: #6463ff;
	margin-top: 15px; // margin-right: 12px;
	/* 放大字体 */
}

@media (max-width: 700px) {
	.content-wrapper {
		flex-direction: column;
	}
}

// .project-info p {
//   margin: 0;
//   /* 去除段落默认的上下间距 */
// }

// .task-count p {
//   margin-bottom: 10px;
// }

.project-description {
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.project-container {
	width: 100%;
	height: 700px;
	/* 容器高度固定为400px */
	overflow-y: auto;
	/* 垂直滚动条 */
	overflow-x: hidden;
	// margin-top: 5px;
	/* 隐藏水平滚动条 */
}

//缩放110
@media only screen and (min-width: 1720px) {
	.project-container {
		height: 60vh; /* 在小屏幕下设置较小的高度 */
	}
	// .deleteStyle {
	// 	top: -100px;
	// 	// left: 295px;
	// }
}
@media only screen and (max-width: 1660px) {
	.project-container {
		height: 58vh;
	}
	// .deleteStyle {
	// 	top: -290px;
	// 	// transform: translate(-50%, -50%);
	// }
}
@media only screen and (max-width: 1420px) {
	.project-container {
		height: 58vh;
	}
	.deleteStyle {
		top: 200px;
	}
}

.deleteStyle {
	position: absolute;
	margin-top: -190px;
	margin-left: 295px;
	z-index: 999;
}
.delete-wrapper {
	z-index: 9999;
}

.closeS {
	margin-top: -3.3px;
	margin-left: -2.5px;
}

.spacer {
	height: 10px;
	/* 设置间距高度 */
}

.project-name {
	font-family: Alibaba PuHuiTi 3;
	font-size: 18px;
	font-weight: 500;
	cursor: pointer;
}
.page-region {
	position: absolute;
	bottom: 10px;
	right: 10px;
}
.empty {
	margin-top: 200px;
}
.ellipsis {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
	width: 70%;
}
</style>
