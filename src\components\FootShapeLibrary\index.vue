<template>
	<el-dialog
		v-model="visible"
		title="脚型通用库"
		width="1200"
		:style="{ height: dialogHeight }"
		draggable
		:append-to-body="true"
		class="custom-dialog"
		:close-on-click-modal="false"
		overflow
	>
		<div class="dialog-content">
			<div class="top-degin">
				<el-button :class="{ active: commonFlag }" @click="setFlag('common')">通用库</el-button>
				<el-button :class="{ active: privateFlag }" @click="setFlag('private')">私有库</el-button>
			</div>
			<div class="dialog-main-content">
				<div class="left-section">
					<div class="container">
						<div class="label-column">
							<div v-for="item in typeData" :key="item.id" class="label-item">{{ item.nameType }}</div>
						</div>
						<div class="select-column">
							<div v-for="item in typeData" :key="item.id" class="select-item">
								<!-- 如果字段是需要手动输入的，则显示输入框 -->
								<template v-if="isInputField(item.ename)">
									<el-input v-model="selectData[item.ename]" :placeholder="'请输入' + item.nameType" clearable />
								</template>
								<!-- 否则显示下拉选择框 -->
								<template v-else>
									<el-select v-model="selectedOptions[item.id]" multiple placeholder="请选择" :clearable="true" collapse-tags>
										<el-option v-for="child in item.children" :key="child.id" :label="child.nameType" :value="child.id"> </el-option>
									</el-select>
								</template>
							</div>
						</div>
					</div>
				</div>
				<div class="center-section">
					<div class="image-gallery">
						<div class="gallery-grid">
							<div v-if="isLoading" v-loading="isLoading" class="center"></div>
							<div v-for="(row, rowIndex) in imageRows" :key="rowIndex" class="gallery-row" v-else>
								<div
									v-for="(item, imageIndex) in row"
									:key="imageIndex"
									class="image-container"
									@mouseover="hoveringIndex = rowIndex + '-' + imageIndex"
									@mouseleave="hoveringIndex = null"
									@click="selectImage(item.image)"
								>
									<img :src="item.image" class="gallery-image" :class="{ selected: item.image === selectedImage }" />
									<div class="filename-style">
										<span class="file-name">{{ item.fileName }}</span>
									</div>
									<el-button
										class="update-buttonF"
										@click="editFootShape(item.id)"
										:icon="Edit"
										type="text"
										v-if="privateFlag && hoveringIndex === rowIndex + '-' + imageIndex"
									></el-button>
									<el-button
										class="delete-buttonF"
										:icon="Delete"
										type="text"
										@click.stop="deleteImageShape(item)"
										v-if="privateFlag && hoveringIndex === rowIndex + '-' + imageIndex"
									>
									</el-button>
								</div>
							</div>
						</div>
						<el-empty description="暂无数据" class="desc-style" v-if="!imageRows.length" />
					</div>
					<el-pagination
						@current-change="handlePageChange"
						:current-page="page.current"
						:page-size="page.size"
						:total="totalItems"
						layout="total, prev, pager, next"
						class="pagina-tyle"
					/>
				</div>
				<div class="right-preview" v-if="selectedShoeItem">
					<div class="preview-header">脚型详情</div>
					<div class="preview-content">
						<el-descriptions :column="1" border>
							<el-descriptions-item v-if="selectedShoeItem.category" label="类别">{{ selectedShoeItem.category }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.weightIndex" label="肥瘦度">{{ selectedShoeItem.weightIndex }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.footarch" label="足弓">{{ selectedShoeItem.footarch }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.age" label="年龄">{{ selectedShoeItem.age }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.shoeSize" label="鞋码">{{ selectedShoeItem.shoeSize }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.footWidth" label="脚宽">{{ selectedShoeItem.footWidth }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.footLength" label="脚长(mm)">{{ selectedShoeItem.footLength }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.bottomGirth" label="跖围(mm)">{{ selectedShoeItem.bottomGirth }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.heightInstep" label="高脚背">{{ selectedShoeItem.heightInstep }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.heelStraight" label="后跟直度">{{ selectedShoeItem.heelStraight }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.halluxValgus" label="拇外翻">{{ selectedShoeItem.halluxValgus }}</el-descriptions-item>
							<el-descriptions-item v-if="selectedShoeItem.footShape" label="脚型">{{ selectedShoeItem.footShape }}</el-descriptions-item>
						</el-descriptions>
					</div>
				</div>
			</div>
		</div>
		<template #footer>
			<div class="dialog-footer">
				<el-button class="search-style" type="primary" @click="search">搜索</el-button>
				<el-button class="reset-style" @click="reset">重置</el-button>
				<el-button class="add-style" type="primary" @click="addFootShape" v-if="privateFlag == true">新增</el-button>
				<el-button @click="handleCancel">取消</el-button>
				<el-button type="primary" @click="handleConfirm"> 确认 </el-button>
			</div>
		</template>

		<!-- 新增/编辑弹窗 -->
		<shape-from ref="shapeDialogRef" @close="showShapeFrom = false" @refresh="fetchDatafoot" />
	</el-dialog>
</template>

<script setup lang="ts" name="FootShapeLibrary">
import { ref, computed, watch, defineAsyncComponent } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Edit, Delete } from '@element-plus/icons-vue';
import { searchFootShape, getDataType, deleteFootShape } from '/@/api/gai/foot';

const ShapeFrom = defineAsyncComponent(() => import('/@/views/gai/components/footShapeDialog.vue'));

// 定义props
interface Props {
	modelValue: boolean;
	userId?: number;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: false,
	userId: 1,
});

// 定义emits
const emit = defineEmits<{
	'update:modelValue': [value: boolean];
	confirm: [data: { file: string; fileName: string; item: any }];
}>();

// 响应式数据
const visible = ref(false);
const isLoading = ref(false);
const commonFlag = ref(true);
const privateFlag = ref(false);
const selectedImage = ref<string | null>(null);
const selectedShoeItem = ref<any>(null);
const selectedFootShapeFile = ref<string>('');
const hoveringIndex = ref<string | null>(null);
const showShapeFrom = ref(false);

const typeData = ref<any[]>([]);
const selectedOptions = ref<Record<string, any[]>>({});
const selectData = ref<Record<string, any>>({});
const imagesWithFiles = ref<any[]>([]);
const totalItems = ref(0);

const page = ref({
	current: 1,
	size: 9,
});

const shapeDialogRef = ref();
const userIds = computed(() => props.userId);

// 定义需要输入框的字段
const inputFields = ['age', 'footLength', 'footWidth', 'footShape', 'bottomGirth'];

// 计算属性
const imageRows = computed(() => {
	const rows: any[][] = [];
	const pagedItems = imagesWithFiles.value;
	for (let i = 0; i < pagedItems.length; i += 3) {
		rows.push(pagedItems.slice(i, i + 3));
	}
	return rows;
});

// 监听visible变化
watch(
	() => props.modelValue,
	(val) => {
		visible.value = val;
		if (val) {
			openDialog();
		}
	},
	{ immediate: true }
);

watch(visible, (val) => {
	emit('update:modelValue', val);
});

// 方法
const isInputField = (ename: string) => {
	return inputFields.includes(ename);
};

const setFlag = async (type: string) => {
	// 重置选中的图片
	selectedImage.value = null;

	if (type === 'common') {
		commonFlag.value = true;
		privateFlag.value = false;
		await fetchDatafoot({ userId: 1, isStored: true });
	} else if (type === 'private') {
		commonFlag.value = false;
		privateFlag.value = true;
		await fetchDatafoot({ userId: userIds.value });
	}
};

const openDialog = async () => {
	commonFlag.value = true;
	privateFlag.value = false;
	const type = '1';

	try {
		const result = await getDataType(type);
		typeData.value = result.data.filter((item: any) => !['183', '184', '185'].includes(item.id));
		// 清空旧的筛选条件
		selectedOptions.value = {};
		selectData.value = {};
		// 重置选中的图片
		selectedImage.value = null;
		selectedShoeItem.value = null;

		// 打开对话框时默认显示通用库，传递userId=1
		await fetchDatafoot({ userId: 1, isStored: true });
	} catch (error) {
		// Error opening dialog
	}
};

const fetchDatafoot = async (params: any = {}) => {
	isLoading.value = true;
	try {
		const queryParams = { ...params };
		// 所有调用都必须带上userId，通用库用1，私有库用当前用户ID
		if (commonFlag.value) {
			queryParams.userId = 1;
			queryParams.isStored = true; // 通用库需要带上isStored为true的参数
		} else {
			queryParams.userId = userIds.value;
		}

		const result = await searchFootShape(page.value, queryParams);
		if (result.ok && result.data) {
			totalItems.value = result.data.total;
			imagesWithFiles.value = result.data.records.map((record: any) => ({
				image: record.image,
				file: record.file,
				id: record.id,
				fileName: record.fileName,
				category: record.category,
				weightIndex: record.weightIndex,
				footarch: record.footarch,
				age: record.age,
				shoeSize: record.shoeSize,
				footWidth: record.footWidth,
				bottomGirth: record.bottomGirth,
				footLength: record.footLength,
				heightInstep: record.heightInstep,
				heelStraight: record.heelStraight,
				halluxValgus: record.halluxValgus,
				footShape: record.footShape,
				exceptionItem: record.exceptionItem,
			}));

			// 自动选择第一项
			selectFirstItem();
		}
	} catch (error) {
		// Error fetching data
	} finally {
		isLoading.value = false;
	}
};

const selectFirstItem = () => {
	if (imagesWithFiles.value.length > 0) {
		const firstItem = imagesWithFiles.value[0];
		selectImage(firstItem.image);
	}
};

const selectImage = (image: string) => {
	const selectedItem = imagesWithFiles.value.find((item) => item.image === image);
	if (selectedItem) {
		selectedImage.value = image;
		selectedFootShapeFile.value = selectedItem.file;
		selectedShoeItem.value = selectedItem;
	}
};

const handlePageChange = async (newPage: number) => {
	page.value.current = newPage;
	selectedImage.value = null;

	const searchParams = { ...selectData.value };

	// 处理选择的选项
	for (const [key, values] of Object.entries(selectedOptions.value)) {
		if (values && values.length > 0) {
			const item = typeData.value.find((type: any) => type.id === key);
			if (item && item.ename) {
				const selectedNames = values
					.map((valueId) => {
						const child = item.children.find((child: any) => child.id === valueId);
						return child ? child.nameType : null;
					})
					.filter((name) => name !== null);
				if (selectedNames.length > 0) {
					searchParams[item.ename] = selectedNames;
				}
			}
		}
	}

	if (commonFlag.value) {
		searchParams.userId = 1;
		searchParams.isStored = true; // 通用库需要带上isStored为true的参数
	} else {
		searchParams.userId = userIds.value;
	}

	await fetchDatafoot(searchParams);
};

const search = async () => {
	const searchParams: any = {};

	// 处理下拉选择的值
	Object.entries(selectedOptions.value).forEach(([key, values]) => {
		if (values && values.length > 0) {
			const item = typeData.value.find((type: any) => type.id === key);
			if (item && item.ename) {
				const selectedNames = values
					.map((valueId) => {
						const child = item.children.find((child: any) => child.id === valueId);
						return child ? child.nameType : null;
					})
					.filter((name) => name !== null);
				if (selectedNames.length > 0) {
					searchParams[item.ename] = selectedNames;
				}
			}
		}
	});

	// 处理输入框的值
	Object.entries(selectData.value).forEach(([key, value]) => {
		if (value !== '' && value !== null && value !== undefined) {
			if (inputFields.includes(key)) {
				searchParams[key] = [value];
			} else {
				searchParams[key] = value;
			}
		}
	});

	if (commonFlag.value) {
		searchParams.userId = 1;
		searchParams.isStored = true; // 通用库需要带上isStored为true的参数
	} else {
		searchParams.userId = userIds.value;
	}

	await fetchDatafoot(searchParams);
};

const reset = async () => {
	selectedOptions.value = {};
	selectData.value = {};
	selectedImage.value = null;

	// 重置时也需要根据当前模式传递userId
	const resetParams: any = {};
	if (commonFlag.value) {
		resetParams.userId = 1;
		resetParams.isStored = true; // 通用库需要带上isStored为true的参数
	} else {
		resetParams.userId = userIds.value;
	}

	await fetchDatafoot(resetParams);
};

const editFootShape = async (id: string) => {
	// 编辑时也传递当前用户ID
	shapeDialogRef.value.openDialog(id, userIds.value);
};

const deleteImageShape = async (items: any) => {
	try {
		await ElMessageBox.confirm('确定要删除这个脚型文件吗？', '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		const ids = [items.id];
		const result = await deleteFootShape(ids);
		if (result) {
			ElMessage.success('删除成功');
			// 删除后重新获取数据，需要根据当前模式传递userId
			const refreshParams: any = {};
			if (commonFlag.value) {
				refreshParams.userId = 1;
				refreshParams.isStored = true; // 通用库需要带上isStored为true的参数
			} else {
				refreshParams.userId = userIds.value;
			}
			await fetchDatafoot(refreshParams);
		}
	} catch (error) {
		if (error === 'cancel') {
			// 用户取消删除
		}
	}
};

const addFootShape = async () => {
	// 新增时传递当前用户ID
	shapeDialogRef.value.openDialog('', userIds.value);
};

const handleCancel = () => {
	visible.value = false;
};

const handleConfirm = () => {
	if (!selectedImage.value || !selectedFootShapeFile.value) {
		ElMessage.warning('请选择一个脚型文件');
		return;
	}

	const selectedItem = imagesWithFiles.value.find((item) => item.image === selectedImage.value);
	if (selectedItem) {
		emit('confirm', {
			file: selectedFootShapeFile.value,
			fileName: selectedItem.fileName,
			item: selectedItem,
		});
		visible.value = false;
	}
};

// 暴露方法给父组件
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
// 复制FootCompute.vue中的原有样式，确保布局一致
.custom-dialog .el-dialog__wrapper {
	position: fixed;
	top: 100px;
	right: 100px;
	bottom: auto;
	left: auto;
	height: 500px;
}
.dialog-content {
	display: flex;
	height: 580px;
	margin-top: -10px;
}
.el-overlay {
	display: none; /* Hide the overlay */
}

.left-section {
	flex: 3;
	padding: 5px;
	border-right: 1px solid #ddd; /* Optional for visual separation */
	/* background: #000; */
}

.right-section {
	flex: 7;
	padding: 5px;
	width: 100%;
	/* background: #cb5151; */
}

.dialog-footer {
	/* padding: 10px; */
	text-align: right;
	/* margin-top: -10px; */
}
.search-style {
	margin-right: 700px;
}
.reset-style {
	left: 150px;
	position: absolute;
}
.add-style {
	left: 350px;
	position: absolute;
}
.select-style {
	width: 200px;

	/* margin-top: 20px; */
}
.item {
	margin-bottom: 10px;
}

.label {
	display: block;
	margin-bottom: 5px;
}
.container {
	display: flex;
	align-items: center;
	gap: 20px;
	margin-top: 20px;
}

.label-column {
	flex: 3.5;
	margin-top: 10px;
}
.select-column {
	flex: 6.5;
}

.label-item,
.select-item {
	display: flex;
	align-items: center;
}
.label-item {
	display: flex;
	align-items: center;
	margin-bottom: 30px;
	/* padding: 2px 0; */
}
.el-select .el-select__tags {
	overflow: hidden;
	text-overflow: ellipsis;
}
.select-item {
	margin-bottom: 20px;
}
.right-section {
	padding: 16px;
}

.image-gallery {
	display: flex;
	flex-direction: column;
}

.image-row {
	display: flex;
	margin-bottom: 8px;
}

/* .gallery-image:last-child {
	margin-right: 0;
} */
.el-pagination {
	/* margin-top: 16px; */
	display: flex;
	justify-content: center;
}
.gallery-grid {
	display: flex;
	flex-direction: column; /* 垂直排列每行 */
	gap: 10px;
}
.gallery-row {
	display: flex; /* 横向排列图片 */
	gap: 10px; /* 图片之间的间距 */
}
.gallery-image {
	width: 200px;
	height: 130px;
	/* margin-right: 8px; */
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
	margin-top: -10px;
	/* box-sizing: border-box; */
}
.gallery-image.selected {
	border: 2px solid #007bff; /* 选中的边框颜色 */
}
.pagina-tyle {
	position: absolute;
	top: 530px;
	right: 0;
}
.top-degin {
	display: flex;
	justify-content: center;
	width: 1000px;
	position: absolute;
	top: 30px;
}
.el-button.active {
	background-color: #6463ff; /* 按钮的背景颜色 */
	color: white; /* 按钮文字颜色 */
}
.center {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 200px;
	margin-bottom: 200px;
}
.image-container {
	position: relative;
	display: inline-block;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
	width: 200px;
	height: 130px;
	margin: 10px 0; /* 10px 的上下间距 */
	/* padding: 10px; */
}

.gallery-image {
	width: 200px;
	height: 130px;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
}

.delete-buttonF {
	position: absolute;
	top: -15px; /* 调整按钮的位置 */
	right: 1px;
}
.update-buttonF {
	position: absolute;
	top: -15px; /* 调整按钮的位置 */
	right: 17px;
}

/* .delete-buttonF:hover {
	background: darkred;
} */
.desc-style {
	display: flex;
	justify-content: center;
	align-content: center;
	margin-top: 100px;
}
.filename-style {
	display: inline-block;
}
.file-name {
	width: 200px;
	display: flex;
	justify-content: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.demo-progress {
	position: absolute;
	top: 50%;
	left: 63%;
	font-size: 28px;
}

.dialog-main-content {
	display: flex;
	justify-content: center;
}
.right-preview {
	margin-left: 20px;
	width: 220px;
}
.center-section {
	width: 620px;
}
</style>
