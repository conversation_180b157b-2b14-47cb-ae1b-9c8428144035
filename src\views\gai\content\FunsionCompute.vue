<template>
	<div>
		<div class="headerC">
			<div class="toptext flex">
				<img src="/@/assets/home.png" class="imgS" @click="goHome" />
				{{ taskName }}
			</div>
			<!--头部右边的头像-->
			<div class="righttext">
				<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
					<span class="logolayout">
						<img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" class="avatarl" />
						{{ username }}
					</span>
					<template #dropdown>
						<el-dropdown-menu>
							<!-- <el-dropdown-item command="/FootLast">首页</el-dropdown-item> -->
							<el-dropdown-item command="personal">{{ $t('user.dropdown2') }}</el-dropdown-item>
							<el-dropdown-item divided command="logOut">{{ $t('user.dropdown5') }}</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<personal-drawer ref="personalDrawerRef"></personal-drawer>
			</div>
		</div>
		<div class="content">
			<div class="aside">
				<div class="Mdropdown">
					<button @click="toggleDropdown" class="model-button">输入模型</button>
					<div class="overflow">
						<div v-if="isOpen" class="Mdropdown-content">
							<div v-for="(button, index) in fileInputs" :key="index" class="file-input-group">
								<el-button class="no-purple" @mousedown.prevent="handleFileButtonClick(index, $event)">
									<label
										:for="'fileInput-' + index"
										class="file-label"
										:title="buttonLabels[index] ? '鞋楦' + button + ': ' + buttonLabels[index] : '鞋楦' + button"
										>鞋楦{{ button }}{{ buttonLabels[index] ? ': ' + buttonLabels[index] : '' }}</label
									>
									<input type="file" :id="'fileInput-' + index" @change="handleFileUpload(index)" style="display: none" />
								</el-button>
								<div class="text2">
									<el-text class="text1" @click="openFootLastDialog(index)">鞋楦通用库</el-text>
								</div>
							</div>
							<el-button class="delete-button" @click="handleDelete()" v-if="!isQiaoDuAdjustMode">删除</el-button>
							<el-button class="purple" @click="addFileInput" v-if="!isQiaoDuAdjustMode">新增</el-button>
						</div>
					</div>
				</div>
				<el-dialog
					v-model="FootLastDialog"
					title="鞋楦通用库"
					width="1200"
					:style="{ height: dialogHeight }"
					draggable
					:append-to-body="true"
					class="custom-dialog"
					:close-on-click-modal="true"
					overflow
				>
					<div class="dialog-content">
						<div class="top-degin">
							<el-button :class="{ active: commonFlag }" @click="setFlag('common')">通用库</el-button>
							<el-button :class="{ active: privateFlag }" @click="setFlag('private')">私有库</el-button>
						</div>
						<div class="dialog-main-content">
							<div class="left-section">
								<div class="container">
									<div class="label-column">
										<div v-for="item in typeData" :key="item.id" class="label-item">
											{{ item.nameType }}
										</div>
									</div>
									<div class="select-column">
										<div v-for="item in typeData" :key="item.id" class="select-item">
											<!-- 如果字段是需要手动输入的，则显示输入框 -->
											<template v-if="isInputField(item.ename)">
												<el-input v-model="selectData[item.ename]" :placeholder="'请输入' + item.nameType" clearable />
											</template>
											<!-- 否则显示下拉选择框 -->
											<template v-else>
												<el-select
													v-model="selectedOptions[item.id]"
													multiple
													placeholder="请选择"
													@change="updateSelectData"
													:clearable="true"
													collapse-tags
												>
													<el-option v-for="child in item.children" :key="child.id" :label="child.nameType" :value="child.id"> </el-option>
												</el-select>
											</template>
										</div>
									</div>
								</div>
							</div>
							<div class="center-section">
								<div class="image-gallery">
									<div class="gallery-grid">
										<div v-if="isLoading" v-loading="isLoading" class="center"></div>
										<div v-for="(row, rowIndex) in imageRows" :key="rowIndex" class="gallery-row" v-else>
											<div
												v-for="(item, imageIndex) in row"
												:key="imageIndex"
												class="image-container"
												@mouseover="hoveringIndex = rowIndex + '-' + imageIndex"
												@mouseleave="hoveringIndex = null"
												@click="selectImage(item.image)"
											>
												<img :src="item.image" class="gallery-image" :class="{ selected: item.image === selectedImage }" />
												<div class="filename-style">
													<span class="file-name">{{ item.fileName }}</span>
												</div>
												<el-button
													class="update-buttonF"
													@click="addfootLast(item.id)"
													:icon="Edit"
													type="text"
													v-if="privateFlag && hoveringIndex === rowIndex + '-' + imageIndex"
												></el-button>
												<el-button
													class="delete-buttonF"
													:icon="Delete"
													type="text"
													@click.stop="deleteImage(item)"
													v-if="privateFlag && hoveringIndex === rowIndex + '-' + imageIndex"
												>
												</el-button>
											</div>
										</div>
									</div>
									<el-empty description="暂无数据" class="desc-style" v-if="!imageRows.length" />
								</div>
								<el-pagination
									@current-change="handlePageChange"
									:current-page="page.current"
									:page-size="page.size"
									:total="totalItems"
									layout="total, prev, pager, next"
									class="pagina-tyle"
								/>
							</div>
							<div class="right-preview" v-if="selectedShoeItem">
								<div class="preview-header">鞋楦详情</div>
								<div class="preview-content">
									<el-descriptions :column="1" border>
										<el-descriptions-item v-if="selectedShoeItem.category" label="类别">{{ selectedShoeItem.category }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.shoeType" label="品类">{{ selectedShoeItem.shoeType }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.sportsShoesType" label="运动鞋">{{
											selectedShoeItem.sportsShoesType
										}}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.brand" label="品牌">{{ selectedShoeItem.brand }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.leatherShoes" label="皮鞋">{{ selectedShoeItem.leatherShoes }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.grountPattern" label="楦底样长">{{
											selectedShoeItem.grountPattern
										}}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.lastBottomGirth" label="楦跖围">{{
											selectedShoeItem.lastBottomGirth
										}}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.heelHeight" label="楦跟高">{{ selectedShoeItem.heelHeight }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.headShape" label="头型">{{ selectedShoeItem.headShape }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.shoeSize" label="鞋码">{{ selectedShoeItem.shoeSize }}</el-descriptions-item>
										<el-descriptions-item v-if="selectedShoeItem.style" label="风格">{{ selectedShoeItem.style }}</el-descriptions-item>
									</el-descriptions>
								</div>
							</div>
						</div>
					</div>
					<template #footer>
						<div class="dialog-footer">
							<el-button class="search-style" type="primary" @click="search">搜索</el-button>
							<el-button class="reset-style" @click="reset">重置</el-button>
							<el-button class="add-style" type="primary" @click="addfootLast" v-if="privateFlag == true">新增</el-button>
							<el-button @click="FootLastDialog = false">取消</el-button>
							<el-button type="primary" @click="sumbit"> 确认 </el-button>
						</div>
					</template>
				</el-dialog>
				<foot-from ref="userDialogRef" @refresh="fetchData" />

				<div class="Pdropdown">
					<button @click="PtoggleDropdown" class="model-button">参数配置</button>
					<div v-if="isOpenP" class="Pdropdown-content">
						<div class="side-style">
							<div class="design-style">
								设计方式：
								<el-select v-model="displayDefinitionName" placeholder="请选择" style="width: 150px">
									<el-option v-for="item in deFinitOptions" :key="item.value" :label="item.label" :value="item.value" />
								</el-select>
							</div>
							<div v-if="!isQiaoDuAdjustMode">
								<div v-for="(data, index) in fileInputs" :key="index" class="slider-container">
									<div class="slider-label">
										鞋楦{{ data }}权重：
										<el-input-number
											v-model="DataForm['data' + (index + 1)]"
											:min="0"
											:max="100"
											:step="1"
											@change="updateDataFormValue(index + 1, $event)"
											style="width: 120px; margin-left: 10px"
										/>
									</div>
									<el-slider
										v-model="DataForm['data' + (index + 1)]"
										:min="0"
										:max="100"
										:step="1"
										@change="updateDataFormSlider(index + 1, $event)"
										style="margin-top: 8px"
									/>
								</div>
							</div>
						</div>
						<div v-if="!isQiaoDuAdjustMode">
							对齐方式：
							<el-select v-model="Aligning" placeholder="请选择" style="width: 150px">
								<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" class="selectS" />
							</el-select>
						</div>

						<div class="flex2" v-if="!isQiaoDuAdjustMode">
							<p>MeshType：</p>
							<el-select v-model="MeshType" placeholder="请选择" style="width: 150px">
								<el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</div>
						<div v-if="isQiaoDuAdjustMode" class="slider-container">
							<div class="slider-label">
								跟高调节参数：
								<el-input-number
									v-model="BFTJC"
									:min="-10"
									:max="20"
									:step="0.1"
									:precision="1"
									@change="updateBFTJCValue"
									style="width: 120px; margin-left: 10px"
								/>
							</div>
							<el-slider v-model="BFTJC" :min="-10" :max="20" :step="0.1" @change="updateBFTJCSlider" style="margin-top: 8px" />
						</div>
						<div v-if="isQiaoDuAdjustMode" class="slider-container">
							<div class="slider-label">
								跟高调节量：
								<el-input-number
									v-model="HeightAdjust"
									:min="-50"
									:max="50"
									:step="0.1"
									:precision="1"
									@change="updateHeightAdjustValue"
									style="width: 120px; margin-left: 10px"
								/>
							</div>
							<el-slider v-model="HeightAdjust" :min="-50" :max="50" :step="0.1" @change="updateHeightAdjustSlider" style="margin-top: 8px" />
						</div>
						<div v-if="isQiaoDuAdjustMode" class="slider-container">
							<div class="slider-label">
								前翘调节量：
								<el-input-number
									v-model="ForwardCurl"
									:min="-10"
									:max="20"
									:step="0.1"
									:precision="1"
									@change="updateForwardCurlValue"
									style="width: 120px; margin-left: 10px"
								/>
							</div>
							<el-slider v-model="ForwardCurl" :min="-10" :max="20" :step="0.1" @change="updateForwardCurlSlider" style="margin-top: 8px" />
						</div>
						<div class="bgbutton">
							<el-tooltip content="计算完成后才可再次提交" placement="top" v-if="!isValid">
								<el-button class="computeC" @click="sumbitCompute" id="compute" :disabled="!isValid">提交计算</el-button>
							</el-tooltip>
							<el-button class="computeC" @click="sumbitCompute" id="compute" :disabled="!isValid" v-else>提交计算</el-button>
						</div>
					</div>
				</div>
				<!-- </div> -->
			</div>

			<div class="moudle-region">
				<div class="m-region">
					<img src="/src/assets/polygon.png" class="img-style" />
					<div class="row">
						<div class="mould-names">
							<div
								v-for="(mouldName, nameIndex) in fileInputs"
								:key="nameIndex"
								class="mould-name"
								:class="{ active: activeIndex === nameIndex }"
								@click="setActiveIndex(nameIndex)"
							>
								鞋楦{{ mouldName }}
							</div>
							<div class="square" ref="square"></div>

							<!-- 添加鞋楦解析区域 -->
							<div class="analysis-container">
								<div class="analysis-group">
									<div class="analysis-content">
										<!-- 添加跟高调节 -->
										<div class="slider-container">
											<div class="slider-label">
												<p>跟高调节：</p>
												<el-input-number
													v-model="ggtjcs"
													:min="-10"
													:max="20"
													:step="0.1"
													:precision="1"
													@change="updateGgtjcsValue"
													style="width: 120px; margin-left: 10px"
												/>
											</div>
											<el-slider v-model="ggtjcs" :min="-10" :max="20" :step="0.1" @change="updateGgtjcsSlider" style="margin-top: 8px" />
										</div>
										<div class="button-status-group">
											<el-button @click="analysis('lastDataAnalysis')" class="analysis-button" type="primary" :icon="Search"> 解析鞋楦 </el-button>
											<div v-if="lastAnalysisStatus.loading" class="status-indicator">
												<el-icon class="is-loading"><Loading /></el-icon>
												<span>解析中...</span>
											</div>
											<div v-else-if="lastAnalysisStatus.finished && activeIndex !== null && activeIndex >= 0" class="status-indicator">
												<el-icon :class="[lastAnalysisStatus.success ? 'success-icon' : 'error-icon']">
													<component :is="lastAnalysisStatus.success ? 'CircleCheckFilled' : 'CircleCloseFilled'" />
												</el-icon>
												<span :class="[lastAnalysisStatus.success ? 'success-text' : 'error-text']">
													{{ lastAnalysisStatus.success ? '解析成功' : '解析失败' }}
												</span>
											</div>
										</div>
										<div class="squareAna-info">
											<!-- 鞋楦基本数据表格 -->
											<div v-if="lastAnalysisStatus.success" class="analysis-result">
												<div class="section-title">鞋楦基本数据</div>
												<div class="result-table">
													<div class="table-row">
														<div class="table-cell label">楦底样长</div>
														<div class="table-cell">
															{{ formatValue(FootLastAnalysis.LastLong) ? Number(formatValue(FootLastAnalysis.LastLong)).toFixed(2) : '' }} mm
														</div>
													</div>
													<div class="table-row">
														<div class="table-cell label">楦宽</div>
														<div class="table-cell">{{ formatValue(FootLastAnalysis.LengthOfLast) }} mm</div>
													</div>
													<div class="table-row">
														<div class="table-cell label">前翘</div>
														<div class="table-cell">{{ formatValue(FootLastAnalysis.CockingForward) }} mm</div>
													</div>
													<div class="table-row">
														<div class="table-cell label">跟高</div>
														<div class="table-cell">{{ formatValue(FootLastAnalysis.SoleGith) }} mm</div>
													</div>
													<div class="table-row">
														<div class="table-cell label">趾围</div>
														<div class="table-cell">{{ formatValue(FootLastAnalysis.WidthOfLast) }} mm</div>
													</div>
													<div class="table-row">
														<div class="table-cell label">鞋头类型</div>
														<div class="table-cell">{{ formatValue(FootLastAnalysis.ShoeToeShape) }}</div>
													</div>
												</div>

												<!-- 鞋码对照表 -->
												<div class="section-title">鞋码对照表</div>
												<div class="size-table">
													<el-table
														:data="formatSizeTableData(FootLastAnalysis.PossibleShoeSize)"
														border
														stripe
														size="small"
														:header-cell-style="{
															background: '#f5f7fa',
															color: '#606266',
															height: '28px',
															padding: '2px',
															fontSize: '11px',
														}"
														:cell-style="{
															padding: '2px',
															fontSize: '11px',
														}"
														style="width: 100%"
													>
														<el-table-column prop="footLength" label="脚长" min-width="45" align="center" show-overflow-tooltip>
															<template #header>
																<el-tooltip content="脚长(mm)" placement="top">
																	<span>脚长</span>
																</el-tooltip>
															</template>
														</el-table-column>
														<el-table-column label="世界鞋号" align="center" min-width="70">
															<template #header>
																<el-tooltip content="世界鞋号/中国鞋号/日本鞋号" placement="top">
																	<span>世界</span>
																</el-tooltip>
															</template>
															<el-table-column prop="worldSize1" label="差1" min-width="35" align="left" show-overflow-tooltip>
																<template #header>
																	<el-tooltip content="号差1" placement="top">
																		<span>差1</span>
																	</el-tooltip>
																</template>
															</el-table-column>
															<el-table-column prop="worldSize2" label="差2" min-width="35" align="left" show-overflow-tooltip>
																<template #header>
																	<el-tooltip content="号差2" placement="top">
																		<span>差2</span>
																	</el-tooltip>
																</template>
															</el-table-column>
														</el-table-column>
														<el-table-column prop="euroSize" label="欧洲" min-width="40" align="left" show-overflow-tooltip>
															<template #header>
																<el-tooltip content="欧洲鞋号" placement="top">
																	<span>欧洲</span>
																</el-tooltip>
															</template>
														</el-table-column>
														<el-table-column prop="ukSize" label="英国" min-width="40" align="left" show-overflow-tooltip>
															<template #header>
																<el-tooltip content="英国鞋号" placement="top">
																	<span>英国</span>
																</el-tooltip>
															</template>
														</el-table-column>
														<el-table-column label="美国" align="center" min-width="80">
															<template #header>
																<el-tooltip content="美国鞋号(男/女)" placement="top">
																	<span>美国</span>
																</el-tooltip>
															</template>
															<el-table-column prop="usSizeMen" label="男" min-width="30" align="left" show-overflow-tooltip>
																<template #header>
																	<el-tooltip content="美国男鞋号" placement="top">
																		<span>男</span>
																	</el-tooltip>
																</template>
															</el-table-column>
															<el-table-column prop="usSizeWomen" label="女" min-width="30" align="left" show-overflow-tooltip>
																<template #header>
																	<el-tooltip content="美国女鞋号" placement="top">
																		<span>女</span>
																	</el-tooltip>
																</template>
															</el-table-column>
														</el-table-column>
													</el-table>
												</div>

												<div v-if="Object.keys(FootLastAnalysis.PossibleShoeSize).length === 0" class="empty-data">
													<el-empty description="暂无鞋码数据" :image-size="60"></el-empty>
												</div>
											</div>
											<div v-else-if="lastAnalysisStatus.finished && !lastAnalysisStatus.success" class="empty-data">
												<el-empty description="解析失败，请重试" :image-size="60"></el-empty>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- <div class="loader" ref="loader"></div> -->
			<div class="demo-progress">
				<el-progress type="circle" :percentage="percentage2" :color="colors" v-if="loader" :width="60" />
			</div>
			<!-- 模型显示区域-->
			<div class="left-regin">
				<div class="switchStyle">
					<button @click="switchToTopView()" class="switchTo"><img src="/@/assets/lookDownOn.png" class="button-icon" />俯视</button>
					<button @click="switchToBottomView()" class="switchTo">
						<img src="/@/assets/lookDownOn.png" class="button-icon" style="transform: rotate(180deg)" />仰视
					</button>
					<button @click="switchToFrontView()" class="switchTo"><img src="/@/assets/front.png" class="button-icon" />前视</button>
					<button @click="switchToLeftSideView()" class="switchTo">
						<img src="/@/assets/flank.png" class="button-icon" style="transform: rotate(180deg)" />左视
					</button>
					<button @click="switchToSideView()" class="switchTo"><img src="/@/assets/flank.png" class="button-icon" />右视</button>
					<button @click="switchToBackView()" class="switchTo"><img src="/@/assets/back.png" class="button-icon" />后视</button>
				</div>
				<div id="objectControls"></div>

				<div class="active-main"></div>
			</div>
		</div>
		<el-collapse v-if="resultBotton" class="result-values" accordion>
			<el-collapse-item class="text-resultbox">
				<template #title>
					<span class="text-result">结果</span>
				</template>
				<div class="textR" v-if="FootMeasurements.DeviationRange && !isQiaoDuAdjustMode">
					偏差范围：<span class="resultText">{{ FootMeasurements.DeviationRange.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.MaximumGirthDifference && !isQiaoDuAdjustMode">
					最大周长差：<span class="resultText">{{ FootMeasurements.MaximumGirthDifference.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.PlantarCir && isQiaoDuAdjustMode">
					跖围：<span class="resultText">{{ FootMeasurements.PlantarCir.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.BottomLastLength && isQiaoDuAdjustMode">
					楦底样长：<span class="resultText">{{ FootMeasurements.BottomLastLength.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.LastWidth && isQiaoDuAdjustMode">
					楦宽：<span class="resultText">{{ FootMeasurements.LastWidth.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.FllowHigh && isQiaoDuAdjustMode">
					跟高：<span class="resultText">{{ FootMeasurements.FllowHigh.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.ForwardCurl && isQiaoDuAdjustMode">
					前翘：<span class="resultText">{{ FootMeasurements.ForwardCurl.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.NewFllowHigh && isQiaoDuAdjustMode">
					新跟高：<span class="resultText">{{ FootMeasurements.NewFllowHigh.slice(1, -1) }}</span>
				</div>
				<div class="textR" v-if="FootMeasurements.NewForwardCurl && isQiaoDuAdjustMode">
					新前翘：<span class="resultText">{{ FootMeasurements.NewForwardCurl.slice(1, -1) }}</span>
				</div>
				<div style="display: flex; margin-top: 10px" v-if="uploadedFile.Ndoc != '' && !isQiaoDuAdjustMode">
					<div class="result-text">正常对齐</div>
					<button id="downloadButton" class="load-button" @click="Normaldownload('stl')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="Normaldownload('3dm')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>3dm
					</button>
				</div>
				<div style="display: flex; margin-top: 10px" v-if="uploadedFile.Ndoc != '' && isQiaoDuAdjustMode">
					<div class="result-text">正位鞋楦</div>
					<button id="downloadButton" class="load-button" @click="Normaldownload('stl')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="Normaldownload('3dm')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>3dm
					</button>
				</div>
				<div style="display: flex" v-if="uploadedFile.Fdoc != '' && Aligning != '0' && !isQiaoDuAdjustMode">
					<div class="result-text">后跟点对齐</div>
					<button id="downloadButton" class="load-button" @click="Followdownload('stl')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="Followdownload('3dm')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>3dm
					</button>
				</div>
				<div style="display: flex" v-if="uploadedFile.Fundoc != '' && !isQiaoDuAdjustMode">
					<div class="result-text">鞋楦</div>
					<button id="downloadButton" class="load-button" @click="Funsiondownload('stl')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="Funsiondownload('3dm')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>3dm
					</button>
				</div>
				<div style="display: flex" v-if="uploadedFile.Fdoc != '' && isQiaoDuAdjustMode">
					<div class="result-text">新鞋楦</div>
					<button id="downloadButton" class="load-button" @click="Followdownload('stl')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="Followdownload('3dm')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>3dm
					</button>
				</div>
				<div style="display: flex" v-if="uploadedFile.CPadoc != '' && Aligning != '0' && !isQiaoDuAdjustMode">
					<div class="result-text">偏离云图</div>
					<button id="downloadButton" class="load-button" @click="CloudPatterndownload('stl')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>stl
					</button>
					<button id="downloadButton" class="load-button" @click="CloudPatterndownload('3dm')">
						<el-icon style="color: #c2abe5"><Download /> </el-icon>3dm
					</button>
				</div>
				<div style="display: flex; margin-left: -5px">
					<button id="downloadButton" class="load-button-all" @click="Alldownload('stl')">
						<el-icon> <Download /> </el-icon>下载全部stl
					</button>
					<button id="downloadButton" class="load-button-all" @click="Alldownload('3dm')">
						<el-icon> <Download /> </el-icon>下载全部3dm
					</button>
				</div>
			</el-collapse-item>
		</el-collapse>
	</div>
</template>

<script setup lang="ts">
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Rhino3dmLoader } from 'three/examples/jsm/loaders/3DMLoader';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader';
import { EXRLoader } from 'three/addons/loaders/EXRLoader.js';
import rhino3dm from 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/rhino3dm.module.js';
import { RhinoCompute } from 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/compute.rhino3d.module.js';
import { ref, onMounted, onBeforeUnmount, watch, computed, nextTick } from 'vue';
import JSZip from 'jszip';
import { STLExporter } from 'three/examples/jsm/exporters/STLExporter.js';
import { Search, Delete, Edit } from '@element-plus/icons-vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
	uploadGHFile,
	uploadCompute,
	getGHFileById,
	uploadStl,
	uploadAddress,
	searchFootLast,
	getDataType,
	deleteFootLast,
	getDeleteById,
	uploadSettings,
	downloadAnalysisFile,
	downloadDefinitionFile,
} from '/@/api/gai/foot';
import mittBus from '/@/utils/mitt';
import { logout } from '/@/api/login';
import { Session } from '/@/utils/storage';
import { useI18n } from 'vue-i18n';
import { userList } from '/@/api/admin/user';
import { Loading } from '@element-plus/icons-vue';

const { t } = useI18n();
const personalDrawerRef = ref();
const FootFrom = defineAsyncComponent(() => import('/@/views/gai/components/footLastDialog.vue'));
const PersonalDrawer = defineAsyncComponent(() => import('/@/views/admin/system/user/personal.vue'));
const taskId = ref();
const taskName = ref();
const taskIdR = ref();
const route = useRoute();
const avatar = ref();
const username = ref();
const Button = ref(true);
const userDialogRef = ref();
const settingsData = ref({
	id: route.query.projectId,
	settings: '',
});
const saveSettings = ref<{
	checkbox_0: boolean;
	checkbox_1: boolean;
	checkbox_2: boolean;
	checkbox_3: boolean;
	checkbox_4: boolean;
	checkbox_5: boolean;
	color_0: string;
	color_1: string;
	color_2: string;
	color_3: string;
	color_4: string;
	color_5: string;
	opacity_0: string;
	opacity_1: string;
	opacity_2: string;
	opacity_3: string;
	opacity_4: string;
	opacity_5: string;
}>({
	checkbox_0: false,
	checkbox_1: false,
	checkbox_2: false,
	checkbox_3: false,
	checkbox_4: true,
	checkbox_5: false,
	color_0: '#6CD56E',
	color_1: '#6CD56E',
	color_2: '#57FFD5',
	color_3: '#57FFD5',
	color_4: '#CC9DE2',
	color_5: '#ffffff',
	opacity_0: '0.5',
	opacity_1: '1',
	opacity_2: '0.5',
	opacity_3: '0.5',
	opacity_4: '1',
	opacity_5: '1',
});
const saveSettings3 = ref<{
	checkbox_0: boolean;
	checkbox_1: boolean;
	checkbox_2: boolean;
	color_0: string;
	color_1: string;
	color_2: string;
	opacity_0: string;
	opacity_1: string;
	opacity_2: string;
}>({
	checkbox_0: false,
	checkbox_1: false,
	checkbox_2: true,
	color_0: '#6CD56E',
	color_1: '#6CD56E',
	color_2: '#CC9DE2',
	opacity_0: '0.5',
	opacity_1: '1',
	opacity_2: '1',
});
const saveSettings2 = ref<{
	checkbox_0: boolean;
	checkbox_1: boolean;
	color_0: string;
	color_1: string;
	opacity_0: string;
	opacity_1: string;
}>({
	checkbox_0: true,
	checkbox_1: true,
	color_0: '#6CD56E',
	color_1: '#CC9DE2',
	opacity_0: '0.5',
	opacity_1: '1',
});
const defaultSettings = ref({
	checkbox_0: false,
	checkbox_1: false,
	checkbox_2: false,
	checkbox_3: false,
	checkbox_4: true,
	checkbox_5: false,
	color_0: '#6CD56E',
	color_1: '#6CD56E',
	color_2: '#57FFD5',
	color_3: '#57FFD5',
	color_4: '#CC9DE2',
	color_5: '#ffffff',
	opacity_0: '0.5',
	opacity_1: '1',
	opacity_2: '0.5',
	opacity_3: '0.5',
	opacity_4: '1',
	opacity_5: '1',
});
const defaultSettings3 = ref({
	checkbox_0: false,
	checkbox_1: false,
	checkbox_2: true,
	color_0: '#6CD56E',
	color_1: '#6CD56E',
	color_2: '#CC9DE2',
	opacity_0: '0.5',
	opacity_1: '1',
	opacity_2: '1',
});
const defaultSettings2 = ref({
	checkbox_0: true,
	checkbox_1: true,
	color_0: '#6CD56E',
	color_1: '#CC9DE2',
	opacity_0: '0.5',
	opacity_1: '1',
});
const uploadedFile = ref({
	doc: '',
	Ndoc: '',
	Fdoc: '',
	Fundoc: '',
	CPadoc: '',
});
const Rhino = ref({
	id: '',
	rhinoFile: '',
});
const selectedType = ref(0);
const isValid = ref(true);
// definitionName将在deFinitOptions定义后设置
const deFinitOptions = [
	{
		value: 'xuanronghhe',
		label: '楦融合',
	},
	{
		value: 'qiaodutiaozheng',
		label: '翘度调整',
	},
];
const definitionName = ref(deFinitOptions[0].value);

const stlFile = ref<{ data: string; index: number }[]>([]);
const address = ref('D:/Minio/gai/');
const FootLastDialog = ref(false);
const typeData = ref();
const totalItems = ref(0);
const selectedOptions = ref<Record<string, string[]>>({});
interface SelectData {
	[key: string]: string[] | number | number[] | boolean;
}
const userIds = ref();
const selectData = ref<SelectData>({});
const selectedImage = ref<string | null>(null);
const selectedFootLastFile = ref<string | null>(null);
const selectedShoeItem = ref<any>(null); // 用于存储选中项的详细信息
const imagesWithFiles = ref();
const currentFileInputIndex = ref();
const percentage2 = ref(0);

const colors = [
	{ color: '#f56c6c', percentage: 20 },
	{ color: '#e6a23c', percentage: 40 },
	{ color: '#6f7ad3', percentage: 60 },
	{ color: '#1989fa', percentage: 80 },
	{ color: '#5cb87a', percentage: 100 },
];
// 定义分页配置
const page = ref({
	current: 1, // 当前页
	size: 9, // 每页条数，搜索前为9条
});
const commonFlag = ref(true);
const privateFlag = ref(false);
const hoveringIndex = ref();

// 定义需要手动输入的字段 (使用 ename)
const footLastInputFields = ['brand', 'leatherShoes', 'grountPattern', 'lastBottomGirth', 'heelHeight'];

// 判断字段是否为输入字段 (复用 FootCompute.vue 的逻辑)
const isInputField = (ename: string) => {
	return footLastInputFields.includes(ename);
};

const setFlag = async (type: string) => {
	// 重置选中的图片
	selectedImage.value = null;
	selectedShoeItem.value = null;

	if (type === 'common') {
		commonFlag.value = true;
		privateFlag.value = false;
		await fetchData();
	} else if (type === 'private') {
		commonFlag.value = false;
		privateFlag.value = true;
		await fetchData();
	}
};
const deleteImage = async (items: any) => {
	try {
		await ElMessageBox.confirm('确定要删除这个鞋楦吗？', '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		// 用户确认删除
		const ids = [items.id];
		// 过滤掉需要删除的图片
		imagesWithFiles.value = imagesWithFiles.value.filter((item: any) => item.image !== items.imageSrc);

		const result = await deleteFootLast(ids);
		if (result) {
			ElMessage.success('删除成功');
			await fetchData();
		}
	} catch (error) {
		// 用户取消删除
		if (error === 'cancel') {
			// 用户取消删除
		}
	}
};
const openFootLastDialog = async (index: number) => {
	// 确保index有效，如果无效则使用0
	if (index === undefined || index === null) {
		index = 0;
	}

	// 确保index在fileInputs的范围内
	if (fileInputs.value && fileInputs.value.length > 0) {
		if (index >= fileInputs.value.length) {
			index = 0;
		}
	}

	currentFileInputIndex.value = index;
	const type = '0';
	FootLastDialog.value = true;
	const result = await getDataType(type);
	typeData.value = result.data;
	Object.keys(selectData.value).forEach((field) => {
		selectedOptions.value[field] = [];
	});
	// 重置选中的图片
	selectedImage.value = null;
	selectedShoeItem.value = null;

	page.value.current = 1;

	await fetchData();
};
const isLoading = ref(false);

const fetchData = async (params: SelectData = {}) => {
	isLoading.value = true;
	try {
		// 合并分页参数和查询条件，只传一个对象
		const queryParams: any = {
			current: page.value.current,
			size: 9,
			...params,
		};
		if (commonFlag.value) {
			queryParams.userId = 1;
			queryParams.isStored = true; // 通用库需要带上isStored为true的参数
		} else {
			queryParams.userId = userIds.value;
		}
		// 传递 page.value 和 queryParams 两个参数，符合接口定义
		const result = await searchFootLast(page.value, queryParams);
		if (result.ok && result.data) {
			totalItems.value = result.data.total;
			imagesWithFiles.value = result.data.records.map((record: any) => ({
				image: record.image,
				file: record.file,
				id: record.id,
				fileName: record.fileName,
				brand: record.brand,
				category: record.category,
				headShape: record.headShape,
				heelHeight: record.heelHeight,
				shoeSize: record.shoeSize,
				shoeType: record.shoeType,
				grountPattern: record.grountPattern,
				lastBottomGirth: record.lastBottomGirth,
				leatherShoes: record.leatherShoes,
				sportsShoesType: record.sportsShoesType,
				style: record.style,
			}));
			selectFirstItem();
		}
	} catch (error) {
		// Error fetching data
	} finally {
		isLoading.value = false;
	}
};
const addfootLast = async (id: string) => {
	userDialogRef.value.openDialog(id);
};

// 处理页码变化
const handlePageChange = async (newPage: number) => {
	page.value.current = newPage;

	// 重置选中的图片
	selectedImage.value = null;

	// 根据当前打开的对话框类型调用相应的函数
	if (FootLastDialog.value) {
		// 鞋楦通用库对话框
		const searchParams = { ...selectData.value };

		// 处理选择的选项
		for (const [key, values] of Object.entries(selectedOptions.value)) {
			if (values && values.length > 0) {
				const item = typeData.value.find((type: any) => type.id === key);
				if (item && item.ename) {
					const selectedNames = values
						.map((valueId) => {
							const child = item.children.find((child: any) => child.id === valueId);
							return child ? child.nameType : null;
						})
						.filter((name) => name !== null);
					if (selectedNames.length > 0) {
						searchParams[item.ename] = selectedNames;
					}
				}
			}
		}

		// 设置用户ID
		if (commonFlag.value) {
			searchParams.userId = 1;
			searchParams.isStored = true; // 通用库需要带上isStored为true的参数
		} else {
			searchParams.userId = userIds.value;
		}

		await fetchData(searchParams);
	}
};
const imageRows = computed(() => {
	const rows: { image: string; file: string; fileName: string; id: string }[][] = [];

	if (!Array.isArray(imagesWithFiles.value)) {
		return rows; // 返回一个空的行数组
	}

	const pagedItems = imagesWithFiles.value;
	for (let i = 0; i < pagedItems.length; i += 3) {
		rows.push(pagedItems.slice(i, i + 3));
	}
	return rows;
});
const search = async () => {
	const searchParams: SelectData = {};

	// 处理下拉选择的值 (过滤空值，并统一包装成数组)
	Object.entries(selectedOptions.value).forEach(([key, values]) => {
		if (values && values.length > 0) {
			const item = typeData.value.find((type: any) => type.id === key);
			if (item && item.ename) {
				const selectedNames = values
					.map((valueId) => {
						const child = item.children.find((child: any) => child.id === valueId);
						return child ? child.nameType : null;
					})
					.filter((name) => name !== null);
				if (selectedNames.length > 0) {
					searchParams[item.ename] = selectedNames; // 下拉选择的值已经是数组
				}
			}
		}
	});

	// 处理输入框的值 (过滤空值，并统一包装成数组)
	Object.entries(selectData.value).forEach(([key, value]) => {
		if (typeof value === 'string' && value !== '' && value !== null && value !== undefined) {
			searchParams[key] = [value];
		}
	});

	if (commonFlag.value) {
		searchParams.userId = 1;
		searchParams.isStored = true; // 通用库需要带上isStored为true的参数
	} else {
		searchParams.userId = userIds.value;
	}

	await fetchData(searchParams); // 传递参数给 fetchData
};

const reset = async () => {
	// 清空下拉选择和输入框的值
	selectedOptions.value = {};
	selectData.value = {};
	// 重置选中的图片
	selectedImage.value = null;
	selectedShoeItem.value = null;

	// 重置当前页为第一页
	page.value.current = 1;

	// 重新获取数据
	await fetchData();
};

const sumbit = async () => {
	let index = currentFileInputIndex.value;

	// 检查index是否存在，如果不存在则提供默认值0
	if (index === undefined || index === null) {
		index = 0;
		currentFileInputIndex.value = 0;
	}

	const existingIndex = stlFile.value.findIndex((value) => value.index === index);
	if (existingIndex !== -1) {
		stlFile.value.splice(existingIndex, 1); // 删除当前按钮索引相同的值
	}
	// 将新上传的 result.data 插入到当前按钮索引对应的位置
	stlFile.value.splice(index, 0, { data: selectedFootLastFile.value || '', index: index });

	// 更新按钮标签为选中的文件名
	if (imagesWithFiles.value) {
		const selectedFile = imagesWithFiles.value.find((item: any) => item.file === selectedFootLastFile.value);
		if (selectedFile) {
			buttonLabels.value[index] = selectedFile.fileName;
		}
	}

	setActiveIndex(index);

	// 根据不同模式更新对应的footLastFile数据
	if (isQiaoDuAdjustMode.value) {
		// 翘度调整模式只需要一个LastFile
		const selectedFile = selectedFootLastFile.value;
		if (selectedFile) {
			const parts = selectedFile.split('/');
			const filename = parts[parts.length - 1];
			const footprint = address.value + filename;
			footLastFile.value.LastFile = footprint;
		}
	} else {
		// 楦融合模式需要多个FootLast
		const { FootLast1Value, FootLast2Value, FootLast3Value, FootLast4Value, FootLast5Value, FootLast6Value } = calculateFootprints(
			stlFile.value,
			address
		);

		footLastFile.value.FootLast1 = FootLast1Value;
		footLastFile.value.FootLast2 = FootLast2Value;
		footLastFile.value.FootLast3 = FootLast3Value;
		footLastFile.value.FootLast4 = FootLast4Value;
		footLastFile.value.FootLast5 = FootLast5Value;
		footLastFile.value.FootLast6 = FootLast6Value;
	}

	setActiveIndex(index);

	// 调用函数以更新 stlFile 配置
	assignStlFileToStlConfig();
	FootLastDialog.value = false;

	// console.log('stlconfig.value:', JSON.stringify(stlconfig.value));
	// console.log('footLastFile.value:', footLastFile.value);
};

function calculateFootprints(stlFiles: { data: string; index: number }[], address: any) {
	const footprints = [];
	for (let i = 0; i < stlFiles.length; i++) {
		// 确保stlFiles[i]存在并且有data属性
		const fileData = stlFiles[i]?.data;
		if (fileData) {
			const parts = fileData.split('/');
			const filename = parts[parts.length - 1];
			const footprint = address.value + filename;
			footprints.push(footprint);
		}
	}
	return {
		FootLast1Value: footprints.length > 0 ? footprints[0] : '',
		FootLast2Value: footprints.length > 1 ? footprints[1] : '',
		FootLast3Value: footprints.length > 2 ? footprints[2] : '',
		FootLast4Value: footprints.length > 3 ? footprints[3] : '',
		FootLast5Value: footprints.length > 4 ? footprints[4] : '',
		FootLast6Value: footprints.length > 5 ? footprints[5] : '',
	};
}
const options = [
	{
		value: '0',
		label: '正常',
	},
	{
		value: '1',
		label: '后跟点',
	},
];
const options1 = [
	{
		value: '0',
		label: 'Quad',
	},
	{
		value: '1',
		label: 'Triangle',
	},
];

const Aligning = ref(options[0].value);
const MeshType = ref(options1[0].value);
const BFTJC = ref<number>(0);
const BFTJC1 = ref<string>('0.0');
const HeightAdjust = ref<number>(-20);
const HeightAdjust1 = ref<string>('-20.0');
const ForwardCurl = ref<number>(-5);
const ForwardCurl1 = ref<string>('-5.0');
const DataForm = ref<Record<string, number>>({
	data1: 70,
	data2: 50,
	data3: 0,
	data4: 0,
	data5: 0,
	data6: 0,
});

const ButtonValue = ref();
const AligningValue = ref();
const MeshTypeValue = ref();
const BFTJCValue = ref();
const HeightAdjustValue = ref();
const ForwardCurlValue = ref();
const resultBotton = ref(false);
onMounted(async () => {
	showSpinner(false);
	// await loadModelFromURL()
	init();
	const projectId = route.query.projectId;
	const projectName = route.query.taskName;
	const projectIdR = route.query.taskId;
	//任务id
	taskId.value = projectId;
	taskName.value = projectName;
	//任务关联的项目id
	taskIdR.value = projectIdR;
	const result = await userList();
	avatar.value = result.data.sysUser.avatar;
	username.value = result.data.sysUser.username;
	userIds.value = result.data.sysUser.userId;
	// await rhinoCom()
	await getParamConfig();
});
// const shouldRefresh = true;
const rhinoAddress = ref();
const getParamConfig = async () => {
	const result = await getGHFileById(taskId.value);
	const paramConfig = JSON.parse(result.data.paramConfig);
	let computeResult;
	if (result.data.computeResult) {
		computeResult = JSON.parse(result.data.computeResult);
	}

	const moudleConfig = JSON.parse(result.data.stlMouldfile);
	let rhinoFile;
	let rhinoFileString;
	if (typeof result.data.rhinoFile === 'string' && result.data.rhinoFile) {
		try {
			rhinoFile = JSON.parse(result.data.rhinoFile);
			// console.log('rhinoFile----=====', rhinoFile);

			if (rhinoFile && typeof rhinoFile === 'string' && rhinoFile.length > 1) {
				try {
					rhinoFileString = JSON.parse(rhinoFile);
				} catch (error) {
					// 解析rhinoFile时出错
					rhinoFileString = null;
				}
			}
		} catch (error) {
			// 解析rhinoFile初始数据时出错
			rhinoFile = null;
		}
	}
	if (result.data.settings) {
		// 优先判断翘度调整模式（根据definitionName）
		if (paramConfig.definitionName === 'qiaodutiaozheng') {
			// 翘度调整模式下的设置 - 只保留有效的字段
			const loadedSettings = JSON.parse(result.data.settings);
			// 过滤出翘度调整模式下有效的字段（只保留 index 0 和 1）
			const validKeys = ['checkbox_0', 'checkbox_1', 'color_0', 'color_1', 'opacity_0', 'opacity_1'];
			const filteredSettings: any = {};
			validKeys.forEach((key) => {
				if (loadedSettings.hasOwnProperty(key)) {
					filteredSettings[key] = loadedSettings[key];
				}
			});
			saveSettings2.value = { ...saveSettings2.value, ...filteredSettings };
		} else if (paramConfig.aligning && paramConfig.aligning == '0') {
			saveSettings3.value = JSON.parse(result.data.settings);
		} else {
			saveSettings.value = JSON.parse(result.data.settings);
		}
	}

	if (paramConfig && moudleConfig) {
		init();
		Button.value = paramConfig.button;
		Aligning.value = paramConfig.aligning;
		MeshType.value = paramConfig.meshType;
		BFTJC.value = paramConfig.BFTJC;
		HeightAdjust.value = paramConfig.HeightAdjust;
		ForwardCurl.value = paramConfig.ForwardCurl;
		definitionName.value = paramConfig.definitionName;
		// 解析DataForm并确保所有值都是数字类型
		const parsedDataForm = JSON.parse(paramConfig.DataForm);
		DataForm.value = {
			data1: Number(parsedDataForm.data1) || 0,
			data2: Number(parsedDataForm.data2) || 0,
			data3: Number(parsedDataForm.data3) || 0,
			data4: Number(parsedDataForm.data4) || 0,
			data5: Number(parsedDataForm.data5) || 0,
			data6: Number(parsedDataForm.data6) || 0,
		};

		// 恢复保存的文件名
		if (paramConfig.uploadedFileNames) {
			buttonLabels.value = paramConfig.uploadedFileNames;
		}

		ButtonValue.value = Button.value;
		AligningValue.value = Aligning.value;
		MeshTypeValue.value = MeshType.value;
		BFTJCValue.value = BFTJC.value;
		HeightAdjustValue.value = HeightAdjust.value;
		ForwardCurlValue.value = ForwardCurl.value;
		if (computeResult) {
			FootMeasurements.value.DeviationRange = computeResult.DeviationRange;
			FootMeasurements.value.MaximumGirthDifference = computeResult.MaximumGirthDifference;
			FootMeasurements.value.PlantarCir = computeResult.PlantarCir;
			FootMeasurements.value.LastWidth = computeResult.LastWidth;
			FootMeasurements.value.FllowHigh = computeResult.FllowHigh;
			FootMeasurements.value.NewFllowHigh = computeResult.NewFllowHigh;
			FootMeasurements.value.NewForwardCurl = computeResult.NewForwardCurl;
			FootMeasurements.value.ForwardCurl = computeResult.ForwardCurl;
			FootMeasurements.value.BottomLastLength = computeResult.BottomLastLength;
		}

		// 在加载模型文件前清空stlFile数组
		stlFile.value = [];

		// 根据数据库中保存的definitionName判断模式，保持与设置加载逻辑一致
		const isQiaoDuMode = paramConfig.definitionName === 'qiaodutiaozheng';

		// 根据模式加载不同的模型文件
		if (isQiaoDuMode) {
			// 翘度调整模式下只加载非空的文件
			if (moudleConfig.mouldfile1 && moudleConfig.mouldfile1 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile1, index: 0 });
			}
			if (moudleConfig.mouldfile2 && moudleConfig.mouldfile2 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile2, index: 1 });
			}
			if (moudleConfig.mouldfile3 && moudleConfig.mouldfile3 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile3, index: 2 });
			}
		} else {
			// 楦融合模式加载所有文件
			if (moudleConfig.mouldfile1 && moudleConfig.mouldfile1 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile1, index: 0 });
			}
			if (moudleConfig.mouldfile2 && moudleConfig.mouldfile2 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile2, index: 1 });
			}
			if (moudleConfig.mouldfile3 && moudleConfig.mouldfile3 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile3, index: 2 });
			}
			if (moudleConfig.mouldfile4 && moudleConfig.mouldfile4 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile4, index: 3 });
			}
			if (moudleConfig.mouldfile5 && moudleConfig.mouldfile5 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile5, index: 4 });
			}
			if (moudleConfig.mouldfile6 && moudleConfig.mouldfile6 !== '') {
				stlFile.value.push({ data: moudleConfig.mouldfile6, index: 5 });
			}
		}

		assignStlFileToStlConfig();
		const { FootLast1Value, FootLast2Value, FootLast3Value, FootLast4Value, FootLast5Value, FootLast6Value } = calculateFootprints(
			stlFile.value,
			address
		);
		footLastFile.value.FootLast1 = FootLast1Value;
		footLastFile.value.FootLast2 = FootLast2Value;
		footLastFile.value.FootLast3 = FootLast3Value;
		footLastFile.value.FootLast4 = FootLast4Value;
		footLastFile.value.FootLast5 = FootLast5Value;
		footLastFile.value.FootLast6 = FootLast6Value;

		if (stlFile.value[0] && stlFile.value[0].data !== '') {
			setActiveIndex(0);
		}

		let count = -1;
		for (let i = 1; i <= 6; i++) {
			const footLastKey = `FootLast${i}` as keyof typeof footLastFile.value;
			if (footLastFile.value[footLastKey] !== '' && footLastFile.value[footLastKey] !== undefined && footLastFile.value[footLastKey] !== null) {
				count++;
			}
		}

		// Push numberInChinese values into fileInputs.value based on count
		for (let i = 0; i < count; i++) {
			const nextNumber = fileInputs.value.length + 1;
			if (nextNumber <= numberInChinese.length) {
				fileInputs.value.push(numberInChinese[nextNumber - 1]);
			}
		}

		if (rhinoFileString && typeof rhinoFileString === 'object' && rhinoFileString.doc) {
			const fileUrl = rhinoFileString.doc;

			const fileExists = await isValidFile(fileUrl);

			if (fileExists) {
				rhinoAddress.value = fileUrl;
				uploadedFile.value = rhinoFileString;
				resultBotton.value = true;
				initRhino();
			} else {
				// 文件不存在或无法访问
				// 如果需要，显示错误信息给用户
				// ElMessage.error({
				// 	message: '指定的文件不存在或无法访问',
				// 	duration: 3000, // 设置提示持续时间为 2 秒
				// });
			}
		} else {
			// rhinoFileString is undefined or doc is not available
		}
	} else {
		// 没有结果
		return;
	}
};
const checkFileExistence = async (url: string) => {
	try {
		const response = await fetch(url, { method: 'HEAD' });

		if (response.ok) {
			// 文件存在
			return true;
		} else if (response.status === 404) {
			// 文件不存在
			return false;
		} else {
			// 其他错误，例如网络问题或服务端错误
			// HTTP 错误: ${response.status}
			return false;
		}
	} catch (error) {
		// 检查文件时发生错误
		return false;
	}
};
const isValidFile = async (url: string) => {
	const isExist = await checkFileExistence(url);
	return isExist;
};

// const compute = ref(false)
const router = useRouter();
const goHome = () => {
	// window.location.reload();
	router.replace({ path: '/FunsionQuest', query: { projectId: taskIdR.value.toString() } });
	//router.go(-1);
};

const onHandleCommandClick = (path: string) => {
	if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('user.logOutTitle'),
			message: t('user.logOutMessage'),
			showCancelButton: true,
			confirmButtonText: t('user.logOutConfirm'),
			cancelButtonText: t('user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('user.logOutExit');
					setTimeout(() => {
						done();
						setTimeout(() => {
							instance.confirmButtonLoading = false;
						}, 300);
					}, 700);
				} else {
					done();
				}
			},
		})
			.then(async () => {
				// 关闭全部的标签页
				mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 3, ...router }));
				// 调用后台接口
				await logout();
				// 清除缓存/token等
				Session.clear();
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				// window.location.reload();
				router.push('/login');
			})
			.catch(() => {});
	} else if (path === 'personal') {
		// 打开个人页面
		personalDrawerRef.value.open();
	} else {
		router.push(path);
	}
};

const isOpen = ref(true);
const toggleDropdown = () => {
	isOpen.value = !isOpen.value;
};
const isOpenP = ref(true);
const PtoggleDropdown = () => {
	isOpenP.value = !isOpenP.value;
};

const isOpenResult = ref(false);
// const PtoggleDropdownResult = () => {
// 	isOpenResult.value = !isOpenResult.value;
// };
const paramConfig = computed(() => ({
	button: Button.value,
	aligning: Aligning.value,
	meshType: MeshType.value,
	BFTJC: BFTJC.value,
	HeightAdjust: HeightAdjust.value,
	ForwardCurl: ForwardCurl.value,
	// data1: Data1.value,
	// data2: Data2.value,
	DataForm: JSON.stringify(DataForm.value),
	uploadedFileNames: buttonLabels.value, // 添加文件名到参数配置
	definitionName: getActualDefinitionName(), // 使用实际的文件类型而不是界面显示的值
}));
const stlconfig = ref<{
	[key: string]: string;
}>({
	mouldfile1: '',
	mouldfile2: '',
	mouldfile3: '',
	mouldfile4: '',
	mouldfile5: '',
	mouldfile6: '',
});

const form = computed(() => ({
	id: taskId.value,
	paramConfig: JSON.stringify(paramConfig.value),
	stlMouldfile: JSON.stringify(stlconfig.value),
}));

const assignStlFileToStlConfig = () => {
	for (let i = 0; i < stlFile.value.length; i++) {
		const key = `mouldfile${i + 1}`;
		// 添加检查确保stlFile.value[i]存在且有data属性
		if (stlFile.value[i] && stlFile.value[i].data) {
			stlconfig.value[key] = stlFile.value[i].data;
		} else {
			// 如果数据不完整，设置为空字符串
			stlconfig.value[key] = '';
		}
	}

	for (let i = stlFile.value.length + 1; i <= 6; i++) {
		const key = `mouldfile${i}`;
		stlconfig.value[key] = '';
	}
};

const stlAddress = ref();
// 存储每个鞋楦对应的地址
const stlAddressMap = ref<{ [key: number]: string }>({});
//上传文件
const activeIndex = ref<number | null>(0);
const numberInChinese = ['一', '二', '三', '四', '五', '六'];
const fileInputs = ref<string[]>(['一']);
const buttonLabels = ref<{ [key: number]: string }>({}); // 新增用于存储按钮标签的对象
// 根据当前模式和鞋楦数量获取实际的文件类型（用于API调用）
const getActualDefinitionName = () => {
	if (isQiaoDuAdjustMode.value) {
		return 'qiaodutiaozheng';
	} else {
		// 楦融合模式，根据文件数量返回对应的类型
		if (fileInputs.value.length >= 2 && fileInputs.value.length <= 6) {
			switch (fileInputs.value.length) {
				case 2:
					return 'xuanronghhe2';
				case 3:
					return 'xuanronghhe3';
				case 4:
					return 'xuanronghhe4';
				case 5:
					return 'xuanronghhe5';
				case 6:
					return 'xuanronghhe6';
				default:
					return 'xuanronghhe';
			}
		} else {
			return 'xuanronghhe';
		}
	}
};

const addFileInput = () => {
	const nextNumber = fileInputs.value.length + 1;
	if (nextNumber <= numberInChinese.length) {
		fileInputs.value.push(numberInChinese[nextNumber - 1]);
	}
};
//删除
// const dataKeys = Object.keys(DataForm.value);
// 修改删除鞋楦的处理
const handleDelete = () => {
	// 获取最后一个索引
	const lastIndex = fileInputs.value.length - 1;

	// 保存当前文件数组的副本
	const newStlFile = [...stlFile.value];
	// 保存当前解析状态的副本
	// const newLastAnalysisStatus = { ...lastAnalysisStatus.value };
	// // 保存当前解析结果的副本
	// const newFootLastAnalysisList = { ...FootLastAnalysisList.value };

	// 删除最后一个鞋楦
	fileInputs.value.splice(lastIndex, 1);

	// 重新构建文件数组，保持索引对应
	const updatedStlFile = [];
	const updatedStatus = {};
	const updatedAnalysis = {};

	// 遍历剩余的文件，重新排序
	fileInputs.value.forEach((_, newIndex) => {
		if (newStlFile[newIndex]) {
			updatedStlFile[newIndex] = newStlFile[newIndex];
		}
		// if (newLastAnalysisStatus[newIndex]) {
		// 	updatedStatus[newIndex] = newLastAnalysisStatus[newIndex];
		// }
		// if (newFootLastAnalysisList[newIndex]) {
		// 	updatedAnalysis[newIndex] = newFootLastAnalysisList[newIndex];
		// }
	});

	// 更新响应式数据
	stlFile.value = updatedStlFile;
	// lastAnalysisStatus.value = updatedStatus;
	// FootLastAnalysisList.value = updatedAnalysis;

	// 更新当前选中的索引
	if (activeIndex.value >= fileInputs.value.length) {
		activeIndex.value = Math.max(0, fileInputs.value.length - 1);
	}

	// 更新场景显示
	if (fileInputs.value.length === 0) {
		// 如果没有鞋楦了，清空场景
		if (sceneStl) {
			while (sceneStl.children.length > 0) {
				sceneStl.remove(sceneStl.children[0]);
			}
		}
	} else {
		// 显示当前选中鞋楦的模型
		const currentFile = stlFile.value[activeIndex.value];
		if (currentFile && currentFile.data) {
			stlAddress.value = currentFile.data;
			const fileMode = stlAddress.value.split('.').pop();
			initStl(fileMode);
		}
	}
};

const footLastFile = ref({
	FootLast1: '',
	FootLast2: '',
	FootLast3: '',
	FootLast4: '',
	FootLast5: '',
	FootLast6: '',
	LastFile: '', // 添加LastFile字段用于翘度调整模式
});
//上传文件地址
const handleFileUpload = async (index: number) => {
	const input = document.getElementById('fileInput-' + index) as HTMLInputElement;
	if (input.files && input.files.length > 0) {
		const mouldFile = input.files[0];
		// console.log('上传的文件:', mouldFile);

		if (!checkAllowedFileTypes(mouldFile)) {
			ElMessage.error('只允许上传后缀名为3dm、stl、obj或stp的文件');
			return;
		}

		const formData = new FormData();
		formData.append('mouldFile', mouldFile);
		const result = await uploadStl(formData);

		if (result.ok) {
			// console.log('result结果---', result);

			// 更新按钮标签为文件名
			buttonLabels.value[index] = mouldFile.name;

			// 检查 stlFile.value 中是否已经存在与当前按钮索引相同的值，如果存在，则删除
			const existingIndex = stlFile.value.findIndex((value) => value.index === index);
			if (existingIndex !== -1) {
				stlFile.value.splice(existingIndex, 1); // 删除当前按钮索引相同的值
			}
			// 将新上传的 result.data 插入到当前按钮索引对应的位置
			stlFile.value.splice(index, 0, { data: result.data, index: index });

			// 清除旧模型的引用（如果存在）
			if (stlModelsMap.value[index]) {
				const oldMesh = stlModelsMap.value[index];
				if (oldMesh.geometry) oldMesh.geometry.dispose();
				if (oldMesh.material) {
					if (Array.isArray(oldMesh.material)) {
						oldMesh.material.forEach((mat) => mat.dispose());
					} else {
						oldMesh.material.dispose();
					}
				}
				delete stlModelsMap.value[index];
			}

			// 保存地址到映射中
			stlAddressMap.value[index] = result.data;

			// 清除此鞋楦的缓存模型（如果存在）
			if (stlModelsMap.value[index]) {
				delete stlModelsMap.value[index];
			}

			// 切换到当前鞋楦
			setActiveIndex(index);
			// 更新 stlconfig 对象的属性
			assignStlFileToStlConfig();
			ElMessage.success({
				message: '上传成功',
				duration: 1500,
			});

			// console.log('JSON.stringify(stlconfig.value)', JSON.stringify(stlconfig.value));
			// console.log('stlFile.value----', stlFile.value);
			const { FootLast1Value, FootLast2Value, FootLast3Value, FootLast4Value, FootLast5Value, FootLast6Value } = calculateFootprints(
				stlFile.value,
				address
			);
			footLastFile.value.FootLast1 = FootLast1Value;
			footLastFile.value.FootLast2 = FootLast2Value;
			footLastFile.value.FootLast3 = FootLast3Value;
			footLastFile.value.FootLast4 = FootLast4Value;
			footLastFile.value.FootLast5 = FootLast5Value;
			footLastFile.value.FootLast6 = FootLast6Value;
		} else {
			ElMessage.error('上传失败');
		}
	} else {
		// 没有选择文件
	}
};

// 辅助函数：检查文件类型是否符合要求
const checkAllowedFileTypes = (file: File): boolean => {
	const allowedTypes = ['3dm', 'stl', 'obj', 'stp'];
	const fileType = getFileExtension(file.name).toLowerCase();
	return allowedTypes.includes(fileType);
};

// 辅助函数：获取文件后缀名
const getFileExtension = (filename: string): string => {
	return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
};

const handleFileButtonClick = (index: number, event: MouseEvent) => {
	event.preventDefault();
	const input = document.getElementById('fileInput-' + index) as HTMLInputElement;
	input.click();
};
// const showUploadContent = (index: number) => {
//   activeIndex.value = index;
//   // console.log("activeIndex.value", activeIndex.value);
// };
// let previousIndex = -1; // 存储上一次点击的索引
const setActiveIndex = async (index: number) => {
	// 保存旧的索引值用于清理
	const oldIndex = activeIndex.value;

	// 先停止渲染循环
	const stopRenderLoop = () => {
		// 正确的停止动画方法是取消动画帧并重置标志
		const win = window as any;
		if (win.stlAnimationId) {
			cancelAnimationFrame(win.stlAnimationId);
		}
		isStlAnimating.value = false;
		setTimeout(() => {
			isStlAnimating.value = true;
			animateStl();
		}, 100);
	};

	stopRenderLoop();

	// 清理旧的场景内容 - 清理场景可以避免不同鞋楦的模型相互干扰
	if (sceneStl) {
		while (sceneStl.children.length > 0) {
			const child = sceneStl.children[0];
			sceneStl.remove(child);
		}
	}

	// 更新当前活动索引
	activeIndex.value = index;

	if (stlFile.value.length > 0 && activeIndex.value >= 0 && activeIndex.value < stlFile.value.length) {
		// 更新模型地址，优先使用缓存中的地址
		if (!stlAddressMap.value[index] && stlFile.value[index]?.data) {
			stlAddressMap.value[index] = stlFile.value[index].data;
		}

		// 确保地址存在
		if (stlAddressMap.value[index]) {
			stlAddress.value = stlAddressMap.value[index];
		} else if (stlFile.value[index]?.data) {
			stlAddress.value = stlFile.value[index].data;
		} else {
			console.error('找不到该鞋楦的地址');
			ElMessage.error('找不到该鞋楦的文件地址，请重新上传');
			return;
		}

		// 从地址获取文件类型
		const fileMode = stlAddress.value.split('.').pop();

		// 初始化当前鞋楦的跟高值（如果未初始化）
		if (typeof ggtjcsList.value[index] === 'undefined') {
			ggtjcsList.value[index] = 0;
			ggtjcs1List.value[index] = '0.0';
		}

		// 初始化当前鞋楦的分析状态（如果未初始化）
		if (!lastAnalysisStatusList.value[index]) {
			lastAnalysisStatusList.value[index] = {
				loading: false,
				finished: false,
				success: false,
			};
		}

		// 初始化模型 - 完全重新加载模型，不使用已有缓存，避免引用问题
		initStl(fileMode);

		// 如果有解析成功的结果，且当前显示的是解析结果，则显示解析模型
		if (lastAnalysisStatusList.value[index]?.success) {
			try {
				initAnay();
			} catch (error) {
				console.error('显示解析模型失败:', error);
			}
		}

		// console.log('activeIndex.value----', activeIndex.value);
		// console.log('stlAddress.value:', stlAddress.value);
	} else {
		console.log('stlFile 数组为空或 activeIndex 超出范围');
		ElMessage.info('未上传文件');
	}
};
let scene, camera, renderer, controls;
//提交计算
const sumbitCompute = async () => {
	// 确保在翘度调整模式下更新LastFile字段
	if (isQiaoDuAdjustMode.value && stlFile.value.length > 0 && stlFile.value[0]) {
		// 检查stlFile[0]是否有data属性，如果是简单字符串则进行转换
		if (typeof stlFile.value[0] === 'string') {
			stlFile.value[0] = { data: stlFile.value[0], index: 0 };
		} else if (stlFile.value[0] && !stlFile.value[0].hasOwnProperty('index')) {
			stlFile.value[0] = {
				data: stlFile.value[0].data,
				index: 0,
			};
		}

		const fileData = stlFile.value[0].data;
		const parts = fileData.split('/');
		const filename = parts[parts.length - 1];
		const footprint = address.value + filename;
		footLastFile.value.LastFile = footprint;
		// console.log('更新LastFile为:', footLastFile.value.LastFile);
	}

	const currentBackground = scene?.background;
	const currentCamera = camera?.position.clone();
	const currentControls = controls?.target.clone();

	// 2. 停止动画但不立即清理场景
	stopAnimation();

	// 3. 上传文件并等待结果
	const result = await uploadGHFile(form.value);

	// 4. 在清理场景之前，创建新场景
	const newScene = new THREE.Scene();
	if (currentBackground) {
		newScene.background = currentBackground;
	}

	// 5. 现在清理旧场景
	if (scene) {
		scene.remove(...scene.children);
		scene.remove(loadedObject.value);
		scene.traverse((object) => {
			if (object.isMesh) {
				object.geometry?.dispose();
				object.material?.dispose();
			}
		});
		scene.clear();
	}

	// 6. 切换到新场景
	scene = newScene;

	// 7. 恢复相机和控制器状态
	if (currentCamera && currentControls) {
		camera.position.copy(currentCamera);
		controls.target.copy(currentControls);
	}

	// 8. 初始化新内容
	init();

	// 9. 重新开始动画
	isAnimating.value = true;
	animate();

	if (scene && scene.children.length > 0) {
		scene.remove(...scene.children);
		scene.remove(loadedObject.value);
	}
	if (Aligning.value != '0' && !isQiaoDuAdjustMode.value) {
		if (JSON.stringify(saveSettings.value) !== JSON.stringify(defaultSettings.value)) {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(saveSettings.value);
			await uploadSettings(settingsData.value);
		} else {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(defaultSettings.value);
			await uploadSettings(settingsData.value);
		}
	} else if (isQiaoDuAdjustMode.value) {
		if (JSON.stringify(saveSettings.value) !== JSON.stringify(defaultSettings2.value)) {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(saveSettings2.value);
			// console.log('saveSettings2.value', saveSettings2.value);
			const result = await uploadSettings(settingsData.value);
		} else {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(defaultSettings2.value);
			await uploadSettings(settingsData.value);
		}
	} else {
		if (JSON.stringify(saveSettings.value) !== JSON.stringify(defaultSettings3.value)) {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(saveSettings3.value);
			await uploadSettings(settingsData.value);
		} else {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(defaultSettings3.value);
			await uploadSettings(settingsData.value);
		}
	}
	let rhinoFileString;
	const result2 = await getGHFileById(taskId.value);
	if (result2.data.rhinoFile && typeof result2.data.rhinoFile === 'string') {
		const rhinoFile = JSON.parse(result2.data.rhinoFile);
		if (rhinoFile.length > 1) {
			rhinoFileString = JSON.parse(rhinoFile);
		}
		if (rhinoFileString) {
			const result3 = await getDeleteById(taskId.value);
			console.log('======result3===========', result3.data);
		}
	}
	console.log('提交计算');
	if (stlFile.value[0] !== '') {
		// 翘度调整模式只需要一个鞋楦文件，不需要检查AligningValue和MeshTypeValue
		if (isQiaoDuAdjustMode.value) {
			ButtonValue.value = Button.value;
			BFTJCValue.value = BFTJC.value;
			HeightAdjustValue.value = HeightAdjust.value;
			ForwardCurlValue.value = ForwardCurl.value;

			// console.log('使用的definitionName.value.value', definitionName.value);
			timing();
			isValid.value = false;
			await loadModelFromURL();
			compute();
			console.log('提交的结束计算');

			// 计算完成后，确保界面显示正确的设计方式
			// 翘度调整模式计算完成后保持显示为翘度调整
			// 不需要修改definitionName，因为已经是正确的

			// 重置索引和相关状态，确保下次上传文件不会出错
			if (fileInputs.value && fileInputs.value.length > 0) {
				currentFileInputIndex.value = 0;
				setActiveIndex(0);
			}
		}
		// 楦融合模式需要两个及以上鞋楦文件，并且需要检查其他参数
		else if (stlFile.value[1] !== '') {
			if (Aligning.value && MeshType.value && DataForm.value) {
				ButtonValue.value = Button.value;
				AligningValue.value = Aligning.value;
				MeshTypeValue.value = MeshType.value;
				timing();
				isValid.value = false;
				await loadModelFromURL();
				compute();
				console.log('提交的结束计算');

				// 计算完成后，确保界面显示正确的设计方式
				// 楦融合模式计算完成后保持显示为楦融合
				// 根据当前文件数量设置正确的楦融合类型
				const fileCount = fileInputs.value.length;
				if (fileCount >= 2 && fileCount <= 6) {
					definitionName.value = `xuanronghhe${fileCount}`;
				} else {
					definitionName.value = 'xuanronghhe';
				}

				// 重置索引和相关状态，确保下次上传文件不会出错
				if (fileInputs.value && fileInputs.value.length > 0) {
					currentFileInputIndex.value = 0;
					setActiveIndex(0);
				}
			} else {
				// 有未填写的字段，提示用户
				ElMessage.error('楦融合模式需要填写所有必填字段');
			}
		} else {
			ElMessage.error('楦融合模式需要上传至少两个鞋楦文件');
		}
	} else {
		ElMessage.error('请上传至少一个鞋楦文件');
	}

	// 在计算结束时保存当前的设计方式到参数配置
	if (Aligning.value != '0') {
		if (JSON.stringify(saveSettings.value) !== JSON.stringify(defaultSettings.value)) {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(saveSettings.value);
			await uploadSettings(settingsData.value);
		} else {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(defaultSettings.value);
			await uploadSettings(settingsData.value);
		}
	} else {
		if (JSON.stringify(saveSettings.value) !== JSON.stringify(defaultSettings3.value)) {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(saveSettings3.value);
			await uploadSettings(settingsData.value);
		} else {
			settingsData.value.id = taskId.value;
			settingsData.value.settings = JSON.stringify(defaultSettings3.value);
			await uploadSettings(settingsData.value);
		}
	}

	// 保存当前选择的设计方式到参数配置
	paramConfig.value.definitionName = definitionName.value;

	// 构建STL模型文件数据
	let stlMouldfileData = {};

	// 翘度调整模式下只保存有效的文件
	if (isQiaoDuAdjustMode.value) {
		stlMouldfileData = {
			mouldfile1: stlFile.value[0] && stlFile.value[0].data ? stlFile.value[0].data : '',
			mouldfile2: '',
			mouldfile3: '',
			mouldfile4: '',
			mouldfile5: '',
			mouldfile6: '',
		};
	} else {
		// 楦融合模式保存所有文件
		stlMouldfileData = {
			mouldfile1: stlFile.value[0] && stlFile.value[0].data ? stlFile.value[0].data : '',
			mouldfile2: stlFile.value[1] && stlFile.value[1].data ? stlFile.value[1].data : '',
			mouldfile3: stlFile.value[2] && stlFile.value[2].data ? stlFile.value[2].data : '',
			mouldfile4: stlFile.value[3] && stlFile.value[3].data ? stlFile.value[3].data : '',
			mouldfile5: stlFile.value[4] && stlFile.value[4].data ? stlFile.value[4].data : '',
			mouldfile6: stlFile.value[5] && stlFile.value[5].data ? stlFile.value[5].data : '',
		};
	}
};

/**
 * Shows or hides the loading spinner
 */
const loader = ref();

function showSpinner(enable) {
	if (enable) {
		loader.value = true;
	} else {
		loader.value = false;
	}
}

const intervalId = ref();
const timing = () => {
	if (intervalId.value) {
		clearInterval(intervalId.value); // 清除上一个定时器
	}
	resetProgress();
	const intervalDuration = 500;
	const maxProgress = 100;
	const progressDuration = 90 * 1000;
	const progressIncrement = 95 / (progressDuration / intervalDuration);
	const maxProgressDuringInterval = 95;
	const errorTimeout = 4 * 60 * 1000;

	let progress = 0;
	let hasReached90Seconds = false;
	const startTime = Date.now();

	intervalId.value = setInterval(async () => {
		const elapsedTime = Date.now() - startTime;

		// 更新进度条
		if (elapsedTime < progressDuration) {
			progress += progressIncrement;
			if (progress > maxProgressDuringInterval) {
				progress = maxProgressDuringInterval;
			}

			if (Math.floor(progress) !== Math.floor(percentage2.value)) {
				percentage2.value = Math.floor(progress);
			}
		} else {
			hasReached90Seconds = true;
			if (Math.floor(percentage2.value) !== maxProgressDuringInterval) {
				percentage2.value = Math.floor(maxProgressDuringInterval);
			}
		}

		if (elapsedTime > errorTimeout) {
			clearInterval(intervalId.value);
			showSpinner(false);
			ElMessage.error('计算超时，请重试！');
			isValid.value = true;
			return;
		}

		try {
			const result = await getGHFileById(taskId.value);
			const paramConfig = JSON.parse(result.data.paramConfig);
			// const computeResult = JSON.parse(result.data.computeResult);
			const stlMoudleConfig = JSON.parse(result.data.stlMouldfile);
			let rhinoFile;
			let rhinoFileString;
			if (typeof result.data.rhinoFile == 'string' && result.data.rhinoFile) {
				rhinoFile = JSON.parse(result.data.rhinoFile);
				if (rhinoFile.length > 1) {
					rhinoFileString = JSON.parse(rhinoFile);
				}
			}
			if (paramConfig && stlMoudleConfig && rhinoFileString) {
				clearInterval(intervalId.value);
				percentage2.value = maxProgress;
				setTimeout(() => {
					showSpinner(false);
				}, 500);
			}
		} catch (error) {
			console.error('Error fetching data:', error);
		}

		if (hasReached90Seconds && Math.floor(progress) === maxProgressDuringInterval) {
			percentage2.value = Math.floor(maxProgressDuringInterval);
		}
	}, intervalDuration);
};
const resetProgress = () => {
	percentage2.value = 0; // 重置进度条
	isValid.value = true; // 清除之前的有效状态
	showSpinner(true); // 显示加载
};

const modelData = ref([]);
// let Ndoc, Fdoc, Fundoc
//compute方法区
//下载文件
function Alldownload(format = '3dm') {
	const zip = new JSZip(); // 创建一个新的压缩包

	// 创建一个数组来存储所有的 Promise
	const promises = [];

	// 添加所有文件到压缩包中，并将每个文件的 Promise 存储在 promises 数组中
	if (Aligning.value == '0' && !isQiaoDuAdjustMode.value) {
		promises.push(addFileToZip(zip, '正常对齐.' + format, uploadedFile.value.Ndoc || Ndoc, format));
		promises.push(addFileToZip(zip, '鞋楦.' + format, uploadedFile.value.Fundoc || Fundoc, format));
	} else if (Aligning.value == '1' && !isQiaoDuAdjustMode.value) {
		promises.push(addFileToZip(zip, '正常对齐.' + format, uploadedFile.value.Ndoc || Ndoc, format));
		promises.push(addFileToZip(zip, '后跟点对齐.' + format, uploadedFile.value.Fdoc || Fdoc, format));
		promises.push(addFileToZip(zip, '鞋楦.' + format, uploadedFile.value.Fundoc || Fundoc, format));
		promises.push(addFileToZip(zip, '偏离云图.' + format, uploadedFile.value.CPadoc || CPadoc, format));
	} else {
		promises.push(addFileToZip(zip, '正位鞋楦.' + format, uploadedFile.value.Ndoc || Ndoc, format));
		promises.push(addFileToZip(zip, '新鞋楦.' + format, uploadedFile.value.Fdoc || Fdoc, format));
	}

	// 等待所有文件都被添加到压缩包中
	Promise.all(promises)
		.then(() => {
			// 所有文件添加完毕后生成并下载 zip 文件
			zip
				.generateAsync({ type: 'blob' })
				.then(function (content) {
					const link = document.createElement('a');
					link.href = window.URL.createObjectURL(content);
					link.download = '楦融合生成文件' + format + '.zip';
					link.click();
				})
				.catch((error) => {
					console.error('Error generating zip file:', error);
				});
		})
		.catch((error) => {
			console.error('Error adding files to zip:', error);
		});
}

function addFileToZip(zip: JSZip, fileName: string, fileData: any, format = '3dm') {
	return new Promise((resolve, reject) => {
		if (fileData) {
			let buffer;
			if (format === 'stl') {
				// 获取 stl 格式数据
				if (typeof fileData === 'string') {
					// 如果 fileData 是 URL，使用 fetch 获取文件内容
					fetch(fileData)
						.then((response) => response.blob())
						.then((blob) => {
							convert3dmToStl(fileData, fileName)
								.then((stlBuffer) => {
									// 将转换后的 STL 数据添加到 zip 中
									let blob = new Blob([stlBuffer], { type: 'application/octet-stream' });
									zip.file(fileName.replace('.3dm', '.stl'), blob);
									resolve(); // 文件成功添加到 zip
								})
								.catch((error) => {
									console.error(`Error converting ${fileName} to STL:`, error);
									reject(error); // 转换错误时 reject
								});
						})
						.catch((error) => {
							console.error(`Error adding ${fileName} to zip:`, error);
							reject(error); // 出现错误时 reject
						});
				} else {
					// 如果 fileData 是字节数据
					buffer = fileData.toByteArray(); // 假设 toByteArray() 已定义
					zip.file(fileName, buffer);
					resolve(); // 文件成功添加到 zip
				}
			} else {
				if (typeof fileData === 'string') {
					// 如果 fileData 是 URL，使用 fetch 获取文件内容
					fetch(fileData)
						.then((response) => response.blob())
						.then((blob) => {
							zip.file(fileName, blob);
							resolve(); // 文件成功添加到 zip
						})
						.catch((error) => {
							console.error(`Error adding ${fileName} to zip:`, error);
							reject(error); // 出现错误时 reject
						});
				} else {
					// 如果 fileData 是字节数据
					buffer = fileData.toByteArray(); // 假设 toByteArray() 已定义
					zip.file(fileName, buffer);
					resolve(); // 文件成功添加到 zip
				}
			}
		} else {
			resolve(); // 如果没有文件数据也解决 Promise
		}
	});
}

async function Normaldownload(format = '3dm') {
	if (!isQiaoDuAdjustMode.value) {
		if (uploadedFile.value.Ndoc) {
			if (format === 'stl') {
				let blob = await convert3dmToStl(uploadedFile.value.Ndoc, '正常对齐.' + format);
				saveByteArray('正常对齐.' + format, blob);
			} else {
				downloadFile(uploadedFile.value.Ndoc, '正常对齐.' + format, format);
			}
		} else {
			let buffer = Ndoc.toByteArray(); // Assuming Ndoc.toByteArray() is defined elsewhere
			saveByteArray('正常对齐.3dm', buffer);
		}
	} else {
		if (uploadedFile.value.Ndoc) {
			if (format === 'stl') {
				let blob = await convert3dmToStl(uploadedFile.value.Ndoc, '正位鞋楦.' + format);
				saveByteArray('正位鞋楦.' + format, blob);
			} else {
				downloadFile(uploadedFile.value.Ndoc, '正位鞋楦.' + format, format);
			}
		} else {
			let buffer = Ndoc.toByteArray(); // Assuming Ndoc.toByteArray() is defined elsewhere
			saveByteArray('正位鞋楦.3dm', buffer);
		}
	}
}

async function Followdownload(format = '3dm') {
	if (!isQiaoDuAdjustMode.value) {
		if (uploadedFile.value.Fdoc) {
			if (format === 'stl') {
				let blob = await convert3dmToStl(uploadedFile.value.Fdoc, '后跟点对齐.' + format);
				saveByteArray('后跟点对齐.' + format, blob);
			} else {
				downloadFile(uploadedFile.value.Fdoc, '后跟点对齐.' + format, format);
			}
		} else {
			let buffer = Fdoc.toByteArray(); // Assuming Fdoc.toByteArray() is defined elsewhere
			saveByteArray('后跟点对齐.3dm', buffer);
		}
	} else {
		if (uploadedFile.value.Fdoc) {
			if (format === 'stl') {
				let blob = await convert3dmToStl(uploadedFile.value.Fdoc, '新鞋楦.' + format);
				saveByteArray('新鞋楦.' + format, blob);
			} else {
				downloadFile(uploadedFile.value.Fdoc, '新鞋楦.' + format, format);
			}
		} else {
			let buffer = Fdoc.toByteArray(); // Assuming Fdoc.toByteArray() is defined elsewhere
			saveByteArray('新鞋楦.3dm', buffer);
		}
	}
}

async function Funsiondownload(format = '3dm') {
	if (uploadedFile.value.Fundoc) {
		if (format === 'stl') {
			let blob = await convert3dmToStl(uploadedFile.value.Fundoc, '鞋楦.' + format);
			saveByteArray('鞋楦.' + format, blob);
		} else {
			downloadFile(uploadedFile.value.Fundoc, '鞋楦.' + format, format);
		}
	} else {
		let buffer = Fundoc.toByteArray(); // Assuming Fundoc.toByteArray() is defined elsewhere
		saveByteArray('鞋楦.3dm', buffer);
	}
}
async function CloudPatterndownload(format = '3dm') {
	if (uploadedFile.value.CPadoc) {
		if (format === 'stl') {
			let blob = await convert3dmToStl(uploadedFile.value.CPadoc, '偏离云图.' + format);
			saveByteArray('偏离云图.' + format, blob);
		} else {
			downloadFile(uploadedFile.value.CPadoc, '偏离云图.' + format, format);
		}
	} else {
		let buffer = CPadoc.toByteArray(); // Assuming CPadoc.toByteArray() is defined elsewhere
		saveByteArray('偏离云图.3dm', buffer);
	}
}
async function convert3dmToStl(inputFilePath: string, filename: string) {
	const response = await fetch(inputFilePath);
	const arrayBuffer = await response.arrayBuffer();
	rhino = await rhino3dm();
	const file3dm = rhino.File3dm.fromByteArray(new Uint8Array(arrayBuffer));
	const file = file3dm;
	const meshes = [];

	// 收集所有网格
	const objects = file.objects();
	for (let i = 0; i < objects.count; i++) {
		const obj = objects.get(i);
		const geometry = obj.geometry();

		if (geometry instanceof rhino.Mesh) {
			meshes.push(geometry);
		} else if (geometry instanceof rhino.Surface) {
			const mesh = geometry.toMesh();
			meshes.push(mesh);
		} else if (geometry instanceof rhino.Curve) {
			const mesh = geometry.toNurbsSurface()?.toMesh();
			if (mesh) meshes.push(mesh);
		}
	}

	// 创建新的场景
	const scene = new THREE.Scene();

	// 处理每个网格
	meshes.forEach((mesh) => {
		try {
			// 创建 Three.js 几何体
			const geometry = new THREE.BufferGeometry();
			const vertices = [];
			const indices = [];

			// 获取顶点
			const vertexCount = mesh.vertices().count;
			for (let i = 0; i < vertexCount; i++) {
				const vertex = mesh.vertices().get(i);
				vertices.push(vertex[0], vertex[1], vertex[2]);
			}

			// 获取面
			const faceCount = mesh.faces().count;
			for (let i = 0; i < faceCount; i++) {
				const face = mesh.faces().get(i);
				if (face.length === 3) {
					// 三角形面
					indices.push(face[0], face[1], face[2]);
				} else if (face.length === 4) {
					// 四边形面，分解为两个三角形
					indices.push(face[0], face[1], face[2]);
					indices.push(face[0], face[2], face[3]);
				}
			}

			// 设置几何体属性
			geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
			geometry.setIndex(indices);
			geometry.computeVertexNormals();

			// 创建材质和网格
			const material = new THREE.MeshStandardMaterial({
				color: 0xcccccc,
				metalness: 0.5,
				roughness: 0.5,
			});
			const threeMesh = new THREE.Mesh(geometry, material);
			scene.add(threeMesh);
		} catch (error) {
			console.error('Error processing mesh:', error);
		}
	});

	// 确保场景中有对象
	if (scene.children.length === 0) {
		throw new Error('No valid meshes found to export');
	}

	// 导出为 STL
	const exporter = new STLExporter();
	const stlString = exporter.parse(scene, { binary: true });

	// 返回二进制 blob
	return new Blob([stlString], { type: 'application/octet-stream' });
}
function downloadFile(url: string, fileName: string) {
	fetch(url)
		.then((response) => response.blob())
		.then((blob) => {
			let link = document.createElement('a');
			link.href = window.URL.createObjectURL(blob);
			link.download = fileName;
			link.click();
		})
		.catch((error) => {
			console.error('Error downloading file:', error);
		});
}
function saveByteArray(fileName: string, byte: Uint8Array) {
	let blob = new Blob([byte], { type: 'application/octect-stream' });
	let link = document.createElement('a');
	link.href = window.URL.createObjectURL(blob);
	link.download = fileName;
	link.click();
}

let rhino: any = null;
const definition = ref();
let doc, Ndoc, Fdoc, Fundoc, CPadoc;
const loadedObjects = ref([]);
async function loadModelFromURL() {
	try {
		const m = await rhino3dm();
		console.log('Loaded rhino3dm.');
		rhino = m;

		init();

		RhinoCompute.url = 'http://************:80/';
		RhinoCompute.apiKey = 'c8cdc5f3d46143b664d72d039b5832fc';
		// RhinoCompute.url = 'http://localhost:6500/';
		// RhinoCompute.apiKey = '';

		await loadDefinitionResource();

		// init();
	} catch (error) {
		console.error('加载模型时出错:', error);
		ElMessage.error('加载模型时出错，请检查网络连接和 URL 设置.');
	}
}

// 声明包含多个属性的响应式对象
const FootMeasurements = ref({
	DeviationRange: '',
	MaximumGirthDifference: '',
	PlantarCir: '',
	LastWidth: '',
	FllowHigh: '',
	NewFllowHigh: '',
	NewForwardCurl: '',
	ForwardCurl: '',
	BottomLastLength: '',
});

const FootMeasure = computed(() => ({
	DeviationRange: FootMeasurements.value.DeviationRange || '',
	MaximumGirthDifference: FootMeasurements.value.MaximumGirthDifference || '',
	PlantarCir: FootMeasurements.value.PlantarCir || '',
	BottomLastLength: FootMeasurements.value.BottomLastLength || '',
	LastWidth: FootMeasurements.value.LastWidth || '',
	FllowHigh: FootMeasurements.value.FllowHigh || '',
	NewFllowHigh: FootMeasurements.value.NewFllowHigh || '',
	NewForwardCurl: FootMeasurements.value.NewForwardCurl || '',
	ForwardCurl: FootMeasurements.value.ForwardCurl || '',
}));

const FootMeasureForm = computed(() => ({
	id: taskId.value,
	computeResult: JSON.stringify(FootMeasure.value),
}));
//计算方法
async function compute() {
	// 在计算开始前清空FootMeasurements，确保不会有旧数据残留
	FootMeasurements.value = {
		DeviationRange: '',
		MaximumGirthDifference: '',
		PlantarCir: '',
		LastWidth: '',
		FllowHigh: '',
		NewFllowHigh: '',
		NewForwardCurl: '',
		ForwardCurl: '',
		BottomLastLength: '',
	};

	let Button = ButtonValue.value;

	// 创建基本的参数树
	let param1 = new RhinoCompute.Grasshopper.DataTree('Button');
	param1.append([0], [Button]);

	let trees = [];
	trees.push(param1);

	// 根据不同的设计模式添加不同的参数
	if (isQiaoDuAdjustMode.value) {
		// 翘度调整模式的参数
		let BFTJC = BFTJCValue.value;
		let HeightAdjust = HeightAdjustValue.value;
		let ForwardCurl = ForwardCurlValue.value;

		// 添加翘度调整相关参数
		let param2 = new RhinoCompute.Grasshopper.DataTree('BFTJC');
		param2.append([1], [BFTJC]);
		trees.push(param2);

		let param3 = new RhinoCompute.Grasshopper.DataTree('HeightAdjust');
		param3.append([2], [HeightAdjust]);
		trees.push(param3);

		let param4 = new RhinoCompute.Grasshopper.DataTree('ForwardCurl');
		param4.append([3], [ForwardCurl]);
		trees.push(param4);

		// 添加鞋楦文件参数 - 翘度调整只需要一个鞋楦文件
		if (footLastFile.value['LastFile']) {
			let param = new RhinoCompute.Grasshopper.DataTree('LastFile');
			param.append([4], [footLastFile.value['LastFile']]);
			trees.push(param);
		}
	} else {
		// 楦融合模式的参数
		let Aligning = AligningValue.value;
		let MeshType = MeshTypeValue.value;

		let param2 = new RhinoCompute.Grasshopper.DataTree('Aligning');
		param2.append([3], [Aligning]);
		trees.push(param2);

		let param3 = new RhinoCompute.Grasshopper.DataTree('MeshType');
		param3.append([4], [MeshType]);
		trees.push(param3);

		// 添加多个鞋楦文件参数 - 楦融合模式
		for (let j = 1; j <= 6; j++) {
			if (footLastFile.value[`FootLast${j}`]) {
				let param = new RhinoCompute.Grasshopper.DataTree(`FootLast${j}`);
				param.append([1], [footLastFile.value[`FootLast${j}`]]);
				trees.push(param);
			}
		}

		// 添加数据文件参数
		for (let i = 1; i <= 6; i++) {
			if (DataForm.value[`data${i}`] !== undefined && DataForm.value[`data${i}`] !== null) {
				let param = new RhinoCompute.Grasshopper.DataTree(`Data${i}`);
				param.append([2], [DataForm.value[`data${i}`]]);
				trees.push(param);
			}
		}
	}

	// console.log('我是trees------：', trees);

	// Call RhinoCompute
	const res = await RhinoCompute.Grasshopper.evaluateDefinition(definition.value, trees);

	if (Object.keys(res.values[0].InnerTree).length > 0) {
		// console.log('我是收集前的数据', res);
		collectResults(res);
	} else {
		if (intervalId.value) {
			clearInterval(intervalId.value); // 清除定时器
		}
		showSpinner(false);
		isValid.value = true;
		ElMessage.error('加载失败');
	}
}

const loadedObject = ref();
/**
 * Parse response
 */
function collectResults(responseJson) {
	// clear doc
	if (doc !== undefined) doc.delete();

	const values = responseJson.values;
	// console.log('输出值-----', responseJson);

	doc = new rhino.File3dm();
	Ndoc = new rhino.File3dm();
	Fdoc = new rhino.File3dm();
	Fundoc = new rhino.File3dm();
	CPadoc = new rhino.File3dm();
	// for each output (RH_OUT:*)...
	for (let i = 0; i < values.length; i++) {
		for (const path in values[i].InnerTree) {
			const branch = values[i].InnerTree[path];

			if (branch.length > 2) {
				for (let j = branch.length - 1; j > 1; j--) {
					branch.splice(j, 1);
				}
			}
			if (!isQiaoDuAdjustMode.value) {
				// 楦融合模式：清空翘度调整模式的数据
				FootMeasurements.value.PlantarCir = '';
				FootMeasurements.value.BottomLastLength = '';
				FootMeasurements.value.LastWidth = '';
				FootMeasurements.value.FllowHigh = '';
				FootMeasurements.value.NewFllowHigh = '';
				FootMeasurements.value.NewForwardCurl = '';
				FootMeasurements.value.ForwardCurl = '';

				// 楦融合模式的数据处理
				if (i === 4 && values[4] && values[4].InnerTree && values[4].InnerTree['{0}'] && branch[0]) {
					FootMeasurements.value.DeviationRange = branch[0].data;
				} else if (i === 5 && values[5] && values[5].InnerTree && values[5].InnerTree['{0}'] && values[5].InnerTree['{0}'][0]) {
					FootMeasurements.value.MaximumGirthDifference = values[5].InnerTree['{0}'][0].data;
				} else if (values[4].InnerTree['{0}'] == undefined || values[5].InnerTree['{0}'].length == 0) {
					FootMeasurements.value.DeviationRange = '';
					FootMeasurements.value.MaximumGirthDifference = '';
				}
			} else {
				// 翘度调整模式：清空楦融合模式的数据
				FootMeasurements.value.DeviationRange = '';
				FootMeasurements.value.MaximumGirthDifference = '';
				// 翘度调整模式：使用更安全的数据访问方式
				try {
					if (i === 3 && values[3] && values[3].InnerTree) {
						// 查找包含目标模式的路径，如果没找到则使用第一个可用路径
						const pathKeys = Object.keys(values[3].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[3].InnerTree[targetPath] &&
							values[3].InnerTree[targetPath][0] &&
							values[3].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.PlantarCir = values[3].InnerTree[targetPath][0].data;
						}
					}
					if (i === 4 && values[4] && values[4].InnerTree) {
						const pathKeys = Object.keys(values[4].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[4].InnerTree[targetPath] &&
							values[4].InnerTree[targetPath][0] &&
							values[4].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.BottomLastLength = values[4].InnerTree[targetPath][0].data;
						}
					}
					if (i === 5 && values[5] && values[5].InnerTree) {
						const pathKeys = Object.keys(values[5].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[5].InnerTree[targetPath] &&
							values[5].InnerTree[targetPath][0] &&
							values[5].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.LastWidth = values[5].InnerTree[targetPath][0].data;
						}
					}
					if (i === 6 && values[6] && values[6].InnerTree) {
						const pathKeys = Object.keys(values[6].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[6].InnerTree[targetPath] &&
							values[6].InnerTree[targetPath][0] &&
							values[6].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.FllowHigh = values[6].InnerTree[targetPath][0].data;
						}
					}
					if (i === 7 && values[7] && values[7].InnerTree) {
						const pathKeys = Object.keys(values[7].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[7].InnerTree[targetPath] &&
							values[7].InnerTree[targetPath][0] &&
							values[7].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.NewFllowHigh = values[7].InnerTree[targetPath][0].data;
						}
					}
					if (i === 8 && values[8] && values[8].InnerTree) {
						const pathKeys = Object.keys(values[8].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[8].InnerTree[targetPath] &&
							values[8].InnerTree[targetPath][0] &&
							values[8].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.NewForwardCurl = values[8].InnerTree[targetPath][0].data;
						}
					}
					if (i === 9 && values[9] && values[9].InnerTree) {
						const pathKeys = Object.keys(values[9].InnerTree);
						const targetPath = pathKeys.find((key) => key.includes('4;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0')) || pathKeys[0];
						if (
							targetPath &&
							values[9].InnerTree[targetPath] &&
							values[9].InnerTree[targetPath][0] &&
							values[9].InnerTree[targetPath][0].data !== undefined
						) {
							FootMeasurements.value.ForwardCurl = values[9].InnerTree[targetPath][0].data;
						}
					}
				} catch (error) {
					console.error('翘度调整模式数据解析错误:', error);
					// 继续执行，不中断流程
				}
			}

			for (let j = 0; j < branch.length; j++) {
				const rhinoObject = decodeItem(branch[j]);
				// console.log('rhinoObject', rhinoObject);
				if (rhinoObject !== null) {
					doc.objects().add(rhinoObject, null);
					modelData.value.push(doc.toByteArray());
					doc.objects().delete(rhinoObject);
					if (!isQiaoDuAdjustMode.value) {
						if (i === 0) {
							Ndoc.objects().add(rhinoObject, null);
						}
						if (i === 1) {
							Fdoc.objects().add(rhinoObject, null);
						}

						if (i === 2) {
							Fundoc.objects().add(rhinoObject, null);
						}
						if (i === 3) {
							CPadoc.objects().add(rhinoObject, null);
						}
					} else {
						if (i === 0) {
							Ndoc.objects().add(rhinoObject, null);
						}
						if (i === 10) {
							Fdoc.objects().add(rhinoObject, null);
						}
					}
				}
			}
		}
	}
	// console.log('输出结果----------', FootMeasurements.value);
	computeResult();
	docUpload();

	if (doc.objects().count < 1) {
		console.error('No rhino objects to load!');
		showSpinner(false);
		isValid.value = true;
		return;
	}
	//   if (scene && loadedObjects.value.length > 0) {
	//   scene.remove(...scene.children);
	//   scene.remove(loadedObject.value);
	//   console.log("执行了吗");
	// }

	// load rhino doc into three.js scene
	const buffer = new Uint8Array(doc.toByteArray()).buffer;
	// set up loader for converting the results to threejs
	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');
	const API = {
		color: 0xffffff, // sRGB
		exposure: 1.0,
	};

	const manager = new THREE.LoadingManager(renderer);
	const loaderEXR = new EXRLoader(manager);
	const matcap = loaderEXR.load('https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/040full.exr');

	// load rhino doc into three.js scene
	loader.parse(
		buffer,
		function (object) {
			let modelCount = 0; // 用于计数模型数量
			const totalModels = object.children.length; // 获取模型总数

			// 判断总模型数是否为 5，如果是 5 才做最后一个模型跳过材质设置
			const skipLastModel = totalModels === 6;

			object.traverse((child) => {
				if (child.isMesh) {
					modelCount++; // 当前模型计数递增

					if (skipLastModel && modelCount === totalModels) {
						// 如果模型总数是 5 且当前是最后一个模型，跳过设置材质
						console.log('Skipping material for the last model');
					} else {
						// 设置材质
						const skinMaterial = new THREE.MeshMatcapMaterial({
							color: new THREE.Color().setHex(API.color).convertSRGBToLinear(),
							matcap: matcap,
							side: THREE.DoubleSide,
						});

						child.material = skinMaterial;
					}
				}
			}, false);

			loadedObjects.value.forEach((obj) => scene.remove(obj));
			loadedObjects.value = [];

			object.children.forEach((child, index) => {
				// console.log(`Processing child ${index}:`, child);
				if (child.isMesh) {
					loadedObjects.value.push(child);
					// scene.add(child);
				}
			});

			loadedObject.value = object;

			// zoom to extents
			zoomCameraToSelection(camera, controls, object.children);

			// 生成控制对象显示的复选框和材质控制
			generateObjectControls();

			// add object graph from rhino model to three.js scene
			scene.add(object);

			showSpinner(false);
			isValid.value = true;
			resultBotton.value = true;
		},
		function (error) {
			console.error('An error occurred while loading the model:', error);
		}
	);
}

const computeResult = async () => {
	// if (FootMeasurements.value.DeviationRange && FootMeasurements.value.MaximumGirthDifference) {
	const result = await uploadCompute(FootMeasureForm.value);
	if (result.data) {
		console.log('结果上传成功');
	} else {
		console.log('结果上传失败');
	}
	// }
};

const uploadedFileUrls = ref([]);

const docUpload = async () => {
	try {
		// 定义需要上传的文件数组和对应的文件名
		let filesToUpload = [];

		if (isQiaoDuAdjustMode.value) {
			// 翘度调整模式下只上传两个文件
			filesToUpload = [
				{ doc: doc.toByteArray(), fileName: 'doc.3dm' },
				{ doc: Ndoc.toByteArray(), fileName: 'Ndoc.3dm' }, // 正位鞋楦
				{ doc: Fdoc.toByteArray(), fileName: 'Fdoc.3dm' }, // 新鞋楦
			];
		} else {
			// 楦融合模式下上传所有五个文件
			filesToUpload = [
				{ doc: doc.toByteArray(), fileName: 'doc.3dm' },
				{ doc: Ndoc.toByteArray(), fileName: 'Ndoc.3dm' },
				{ doc: Fdoc.toByteArray(), fileName: 'Fdoc.3dm' },
				{ doc: Fundoc.toByteArray(), fileName: 'Fundoc.3dm' },
				{ doc: CPadoc.toByteArray(), fileName: 'CPadoc.3dm' },
			];
		}

		// 循环处理每个文件
		for (let fileData of filesToUpload) {
			let blob = new Blob([fileData.doc], { type: 'application/octet-stream' });
			const formData = new FormData();
			// formData.append('id', taskId.value);
			formData.append('mouldFile', blob, fileData.fileName);
			// 上传文件并获取结果
			const result = await uploadStl(formData);

			// 如果上传成功，将文件地址添加到全局变量数组
			if (result.data) {
				uploadedFileUrls.value.push(result.data);
				// console.log(`${fileData.fileName} 上传成功`);
			} else {
				console.log(`${fileData.fileName} 上传失败`);
			}
		}
		// 在此处可以进行上传完成后的逻辑处理，例如更新界面或其他操作
		console.log('所有文件上传完成');
		// console.log('上传成功的文件地址数组:', uploadedFileUrls.value);

		// 确保索引对应正确
		uploadedFile.value.doc = uploadedFileUrls.value[0];
		uploadedFile.value.Ndoc = uploadedFileUrls.value[1];
		uploadedFile.value.Fdoc = uploadedFileUrls.value[2];

		if (!isQiaoDuAdjustMode.value) {
			// 只在楦融合模式下设置这些属性
			uploadedFile.value.Fundoc = uploadedFileUrls.value[3];
			uploadedFile.value.CPadoc = uploadedFileUrls.value[4];
		} else {
			// 在翘度调整模式下，清空不需要的属性
			uploadedFile.value.Fundoc = '';
			uploadedFile.value.CPadoc = '';
		}
		uploadedFileUrls.value = [];
		// console.log('uploadedFile.value', uploadedFile.value);
		Rhino.value.id = taskId.value;
		Rhino.value.rhinoFile = JSON.stringify(uploadedFile.value);
		// console.log('Rhino.value', Rhino.value);

		const resultR = await uploadAddress(Rhino.value);
		if (resultR) {
			console.log('文件上传成功！！！');
		} else {
			console.log('文件上传失败！！！');
		}
	} catch (error) {
		console.error('上传文件发生错误:', error);
	}
};

function generateObjectControls() {
	const objectControls = document.getElementById('objectControls');
	objectControls.innerHTML = ''; // 清空之前的内容
	objectControls.style.display = 'flex'; // 设置为flex布局
	objectControls.style.flexDirection = 'column'; // 垂直列显示
	objectControls.style.alignItems = 'center'; // 上下居中
	objectControls.style.justifyContent = 'center'; // 左右居中
	objectControls.style.background = 'rgba(255, 255, 255, 0.3)'; // 设置背景色为半透明白色
	objectControls.style.position = 'absolute';
	objectControls.style.left = '570px';
	// objectControls.style.top = '100px';
	objectControls.style.top = '80px';
	objectControls.style.width = '220px';
	objectControls.style.height = '190px';
	objectControls.style.borderRadius = '10px';

	let labels: string[];

	// 每个分组内的子标签名称
	if (!isQiaoDuAdjustMode.value) {
		if (
			loadedObjects.value[0] &&
			loadedObjects.value[1] &&
			loadedObjects.value[2] &&
			loadedObjects.value[3] &&
			loadedObjects.value[4] &&
			loadedObjects.value[5]
		) {
			labels = ['正常对齐', '', '后跟点对齐', '', '鞋楦', '偏离云图'];
		} else {
			labels = ['正常对齐', '', '鞋楦', '', '', ''];
		}
	} else {
		labels = ['正位鞋楦', '新鞋楦', '', '', '', ''];
	}

	loadedObjects.value.forEach((obj, index) => {
		if (labels[index] === '' && !isQiaoDuAdjustMode.value) {
			return; // 跳过第2个和第4个标签
		}

		const controlContainer = document.createElement('div');
		controlContainer.style.display = 'flex';
		controlContainer.style.position = 'relative';
		controlContainer.style.marginBottom = '8px';

		const label = document.createElement('div');
		label.style.color = '#7472F1';
		label.style.marginRight = '100px';
		label.style.marginBottom = '-50px';
		label.style.bottom = '-15px';
		label.appendChild(document.createTextNode(labels[index]));
		controlContainer.appendChild(label);
		objectControls.appendChild(controlContainer);

		let defaultCheckboxState, defaultColor, defaultOpacity, savedCheckboxState, savedColor, savedOpacity;

		if (!isQiaoDuAdjustMode.value) {
			if (
				loadedObjects.value[0] &&
				loadedObjects.value[1] &&
				loadedObjects.value[2] &&
				loadedObjects.value[3] &&
				loadedObjects.value[4] &&
				loadedObjects.value[5]
			) {
				// 获取默认设置
				defaultCheckboxState = defaultSettings.value[`checkbox_${index}`]; // 获取默认的复选框状态
				defaultColor = defaultSettings.value[`color_${index}`]; // 获取默认的颜色
				defaultOpacity = defaultSettings.value[`opacity_${index}`]; // 获取默认的透明度

				// 获取保存的设置值
				savedCheckboxState = saveSettings.value[`checkbox_${index}`];
				savedColor = saveSettings.value[`color_${index}`];
				savedOpacity = saveSettings.value[`opacity_${index}`];
			} else {
				defaultCheckboxState = defaultSettings3.value[`checkbox_${index}`]; // 获取默认的复选框状态
				defaultColor = defaultSettings3.value[`color_${index}`]; // 获取默认的颜色
				defaultOpacity = defaultSettings3.value[`opacity_${index}`]; // 获取默认的透明度

				// 获取保存的设置值
				savedCheckboxState = saveSettings3.value[`checkbox_${index}`];
				savedColor = saveSettings3.value[`color_${index}`];
				savedOpacity = saveSettings3.value[`opacity_${index}`];
			}
		} else {
			defaultCheckboxState = defaultSettings2.value[`checkbox_${index}`]; // 获取默认的复选框状态
			defaultColor = defaultSettings2.value[`color_${index}`]; // 获取默认的颜色
			defaultOpacity = defaultSettings2.value[`opacity_${index}`]; // 获取默认的透明度

			// 获取保存的设置值
			savedCheckboxState = saveSettings2.value[`checkbox_${index}`];
			savedColor = saveSettings2.value[`color_${index}`];
			savedOpacity = saveSettings2.value[`opacity_${index}`];
		}

		// 最终使用的复选框状态、颜色、透明度（优先使用保存的设置，如果保存的设置不存在则使用默认设置）
		const finalCheckboxState = savedCheckboxState !== undefined && savedCheckboxState !== null ? savedCheckboxState : defaultCheckboxState;
		const finalColor = savedColor !== undefined && savedColor !== null ? savedColor : defaultColor;
		const finalOpacity = savedOpacity !== undefined && savedOpacity !== null ? savedOpacity : defaultOpacity;

		// 创建复选框并添加单击事件处理程序
		const checkbox = document.createElement('input');
		checkbox.type = 'checkbox';
		checkbox.checked = finalCheckboxState; // 从localStorage获取复选框状态
		checkbox.id = 'checkbox_' + index; // 为每个复选框设置唯一的ID

		// 创建用于显示自定义复选框状态的图像
		const checkboxImage = document.createElement('img');
		checkboxImage.src = checkbox.checked
			? 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/open_eyes.png'
			: 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/close_eyes.png';
		checkboxImage.style.cursor = 'pointer'; // 鼠标悬停时显示手型
		checkboxImage.style.marginLeft = '180px';
		checkboxImage.style.marginBottom = '50px';
		checkboxImage.style.width = '16px';
		checkboxImage.style.height = '10px';

		// 添加点击事件处理程序
		checkboxImage.onclick = () => {
			checkbox.checked = !checkbox.checked; // 切换复选框的状态
			checkboxImage.src = checkbox.checked
				? 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/open_eyes.png'
				: 'https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/close_eyes.png'; // 根据新状态更新图像
			toggleObjectVisibility(index, checkbox.checked);
			saveSetting('checkbox_' + index, checkbox.checked); // 保存复选框状态
		};

		objectControls.appendChild(checkboxImage);
		toggleObjectVisibility(index, checkbox.checked);

		// 添加颜色输入控件
		const colorInputContainer = document.createElement('div');
		colorInputContainer.style.display = 'flex';
		colorInputContainer.style.alignItems = 'center';
		colorInputContainer.style.marginLeft = '90px';
		colorInputContainer.style.marginTop = '-70px';
		colorInputContainer.style.justifyContent = 'center'; // 水平居中

		const colorInput = document.createElement('input');
		colorInput.type = 'color';
		colorInput.value = finalColor;
		colorInput.oninput = (e) => {
			const target = e.target as HTMLInputElement;
			setObjectColor(index, target.value);
			saveSetting('color_' + index, target.value); // 保存颜色设置
		};

		colorInputContainer.appendChild(colorInput);
		objectControls.appendChild(colorInputContainer);
		setObjectColor(index, finalColor); // 应用颜色到模型

		// 添加透明度输入控件
		const opacityInput = document.createElement('input');
		opacityInput.type = 'range';
		opacityInput.min = 0;
		opacityInput.max = 1;
		opacityInput.step = 0.1;
		opacityInput.value = finalOpacity;
		opacityInput.style.background = '#7472F1'; // 修改进度条颜色
		opacityInput.style.border = 'none'; // 去除进度条边框
		opacityInput.style.height = '5px'; // 设置进度条高度
		opacityInput.style.borderRadius = '2px'; // 设置进度条圆角
		opacityInput.style.marginTop = '5px';
		opacityInput.style.marginBottom = '10px';
		opacityInput.oninput = (e) => {
			const target = e.target as HTMLInputElement;
			setObjectOpacity(index, parseFloat(target.value));
			saveSetting('opacity_' + index, target.value); // 保存透明度设置
		};
		objectControls.appendChild(opacityInput);
		setObjectOpacity(index, finalOpacity); // 应用透明度到模型
	});
}

function toggleObjectVisibility(index, isVisible) {
	if (!loadedObjects.value[index]) return; // 确保对象存在

	if (index === 0 && loadedObjects.value[0] && loadedObjects.value[1] && !isQiaoDuAdjustMode.value) {
		loadedObjects.value[0].visible = isVisible;
		loadedObjects.value[1].visible = isVisible;
	} else if (index === 2 && loadedObjects.value[2] && loadedObjects.value[3] && !isQiaoDuAdjustMode.value) {
		loadedObjects.value[2].visible = isVisible;
		loadedObjects.value[3].visible = isVisible;
	} else if (loadedObjects.value[index]) {
		loadedObjects.value[index].visible = isVisible;
	}
}
function setObjectColor(index, color) {
	if (!loadedObjects.value[index]) return; // 确保对象存在

	if (index === 0 && loadedObjects.value[0] && loadedObjects.value[1] && !isQiaoDuAdjustMode.value) {
		loadedObjects.value[0].material.color.set(color);
		loadedObjects.value[1].material.color.set(color);
	} else if (index === 2 && loadedObjects.value[2] && loadedObjects.value[3] && !isQiaoDuAdjustMode.value) {
		loadedObjects.value[2].material.color.set(color);
		loadedObjects.value[3].material.color.set(color);
	} else if (loadedObjects.value[index]) {
		loadedObjects.value[index].material.color.set(color);
	}
}
function setObjectOpacity(index, opacity) {
	if (!loadedObjects.value[index]) return; // 确保对象存在

	if (index === 0 && loadedObjects.value[0] && loadedObjects.value[1] && !isQiaoDuAdjustMode.value) {
		loadedObjects.value[0].material.opacity = opacity;
		loadedObjects.value[1].material.opacity = opacity;
		loadedObjects.value[0].material.transparent = opacity < 1;
		loadedObjects.value[1].material.transparent = opacity < 1;
		loadedObjects.value[0].material.needsUpdate = true;
		loadedObjects.value[1].material.needsUpdate = true;
	} else if (index === 2 && loadedObjects.value[2] && loadedObjects.value[3] && !isQiaoDuAdjustMode.value) {
		loadedObjects.value[2].material.opacity = opacity;
		loadedObjects.value[3].material.opacity = opacity;
		loadedObjects.value[2].material.transparent = opacity < 1;
		loadedObjects.value[3].material.transparent = opacity < 1;
		loadedObjects.value[2].material.needsUpdate = true;
		loadedObjects.value[3].material.needsUpdate = true;
	} else if (loadedObjects.value[index]) {
		loadedObjects.value[index].material.opacity = opacity;
		loadedObjects.value[index].material.transparent = opacity < 1;
		loadedObjects.value[index].material.needsUpdate = true;
	}
}
// 获取存储的设置
function getStoredSetting(key: keyof typeof saveSettings.value, defaultValue: any): any {
	const value = saveSettings.value[key];
	return value !== undefined ? value : defaultValue;
}

async function saveSetting(key: keyof typeof saveSettings.value, value: any): Promise<void> {
	settingsData.value.id = taskId.value;
	if (isQiaoDuAdjustMode.value) {
		// 翘度调整模式 - 只保存有效的字段
		const validKeys = ['checkbox_0', 'checkbox_1', 'color_0', 'color_1', 'opacity_0', 'opacity_1'];
		if (validKeys.includes(key as string)) {
			saveSettings2.value[key] = value;
		}
		settingsData.value.settings = JSON.stringify(saveSettings2.value);
	} else if (Aligning.value == '0') {
		saveSettings3.value[key] = value;
		settingsData.value.settings = JSON.stringify(saveSettings3.value);
	} else if (Aligning.value == '1') {
		saveSettings.value[key] = value;
		settingsData.value.settings = JSON.stringify(saveSettings.value);
	} else {
		// 默认情况下使用 saveSettings
		saveSettings.value[key] = value;
		settingsData.value.settings = JSON.stringify(saveSettings.value);
	}

	// console.log('保存设置', saveSettings.value);
	try {
		// 假设 uploadSettings 返回一个 Promise
		const result = await uploadSettings(settingsData.value);
		if (result.data) {
		}
	} catch (error) {
		console.error('保存设置失败', error);
	}
}
/**
 * Attempt to decode data tree item to rhino geometry
 */
function decodeItem(item) {
	const data = JSON.parse(item.data);
	if (item.type === 'System.String') {
		// hack for draco meshes
		try {
			return rhino.DracoCompression.decompressBase64String(data);
		} catch {} // ignore errors (maybe the string was just a string...)
	} else if (typeof data === 'object') {
		return rhino.CommonObject.decode(data);
	}
	return null;
}
const backgroundTexture = ref(null);

function createBackground(width: number, height: number) {
	if (backgroundTexture.value) {
		return backgroundTexture.value;
	}

	const canvas = document.createElement('canvas');
	canvas.width = width;
	canvas.height = height;
	const context = canvas.getContext('2d');
	const gradient = context.createLinearGradient(0, 0, 0, canvas.height);
	gradient.addColorStop(0, '#cdbaed');
	gradient.addColorStop(1, '#e3e9ee');
	context.fillStyle = gradient;
	context.fillRect(0, 0, canvas.width, canvas.height);

	backgroundTexture.value = new THREE.CanvasTexture(canvas);
	return backgroundTexture.value;
}

//加载3dm
function init() {
	console.log('初始化场景');
	if (scene && scene.children.length > 0) {
		// 保存当前背景
		const currentBackground = scene.background;

		// 清理场景
		scene.traverse((object) => {
			if (object.isMesh) {
				object.geometry?.dispose();
				object.material?.dispose();
			}
		});
		scene.clear();

		// 恢复背景
		scene.background = currentBackground;
	} else {
		const width = innerWidth;
		const height = innerHeight;
		// 首次初始化才创建新背景
		scene = new THREE.Scene();
		scene.background = createBackground(width, height);
	}

	// Rhino models are z-up, so set this as the default
	THREE.Object3D.DEFAULT_UP = new THREE.Vector3(0, 0, 1);

	// create a scene and a camera

	if (!scene) {
		scene = new THREE.Scene();
	}

	const width = innerWidth;
	const height = innerHeight;
	// 渐变背景
	const canvas = document.createElement('canvas');
	canvas.width = width;
	canvas.height = height;
	const context = canvas.getContext('2d');
	const gradient = context.createLinearGradient(0, 0, 0, canvas.height);
	gradient.addColorStop(0, '#cdbaed');
	gradient.addColorStop(1, '#e3e9ee');
	context.fillStyle = gradient;
	context.fillRect(0, 0, canvas.width, canvas.height);
	const texture = new THREE.CanvasTexture(canvas);
	scene.background = texture;

	if (!camera) {
		camera = new THREE.PerspectiveCamera(70, width / height, 1, 1000);
		camera.position.set(0, -200, 30);
		// camera.lookAt(0, 20, 0);
	}

	// create the renderer and add it to the html
	if (!renderer) {
		renderer = new THREE.WebGLRenderer({ antialias: true });
		renderer.setPixelRatio(window.devicePixelRatio);
		renderer.setSize(width, height);
		const container = document.querySelector('.active-main');
		container.appendChild(renderer.domElement);
	}

	// add some controls to orbit the camera
	if (!controls) {
		controls = new OrbitControls(camera, renderer.domElement);
		controls.enablePan = false;
	}

	// controls.target.set( 0, 2, 0 );
	// controls.maxDistance = 80;
	// controls.minDistance = 20;

	// add a directional light
	const directionalLight = new THREE.DirectionalLight(0xffffff);
	directionalLight.intensity = 1;
	scene.add(directionalLight);

	const ambientLight = new THREE.AmbientLight();
	scene.add(ambientLight);

	// handle changes in the window size
	window.addEventListener('resize', onWindowResize, false);

	animate();
}

function onWindowResize() {
	camera.aspect = window.innerWidth / window.innerHeight;
	camera.updateProjectionMatrix();
	renderer.setSize(window.innerWidth, window.innerHeight);
	animate();
}

// 添加状态控制
const isAnimating = ref(true);

function animate() {
	if (!isAnimating.value) return;

	if (renderer && scene && camera) {
		requestAnimationFrame(animate);
		controls?.update();
		renderer.render(scene, camera);
	}
}

function stopAnimation() {
	isAnimating.value = false;

	// 同时确保所有相关动画也停止
	isStlAnimating.value = false;
	isAnayAnimating.value = false;

	// 取消任何正在进行的动画帧
	if (window.stlAnimationId) {
		cancelAnimationFrame(window.stlAnimationId);
	}

	if (window.anayAnimationId) {
		cancelAnimationFrame(window.anayAnimationId);
	}
}

//调整位置
//俯视效果
function switchToTopView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(100, -300, 3500);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

//仰视效果
function switchToBottomView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(100, -300, -3500);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

//正面效果
function switchToFrontView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			// camera.position.set(1000, 0, 0);
			camera.position.set(200, -1000, -50);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

//左侧面效果
function switchToLeftSideView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			// camera.position.set(100, 1000, 0);
			camera.position.set(-100, 0, 50);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}
//右侧面效果
function switchToSideView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(2000, 0, 200);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}
//背面效果
function switchToBackView() {
	loadedObject.value.traverse((child) => {
		if (child.isMesh) {
			child.position.set(0, 0, 0);
			camera.position.set(100, 1000, 0);
			camera.lookAt(0, 0, 0);
		}
	});
	zoomCameraToSelection(camera, controls, loadedObject.value.children);
}

/**
 * Helper function that behaves like rhino's "zoom to selection", but for three.js!
 */
function zoomCameraToSelection(camera, controls, selection, fitOffset = 1.2) {
	const box = new THREE.Box3();

	for (const object of selection) {
		if (object.isLight) continue;
		box.expandByObject(object);
	}

	const size = box.getSize(new THREE.Vector3());
	const center = box.getCenter(new THREE.Vector3());

	const maxSize = Math.max(size.x, size.y, size.z);
	const fitHeightDistance = maxSize / (2 * Math.atan((Math.PI * camera.fov) / 360));
	const fitWidthDistance = fitHeightDistance / camera.aspect;
	const distance = fitOffset * Math.max(fitHeightDistance, fitWidthDistance);

	const minDistance = Math.max(50, distance * 0.6); // 最小缩放距离（保证一定的可视距离）修改0.6可改变缩放的最大值，值越大缩放的最大值越小
	const maxDistance = Math.max(200, distance * 2); // 最大缩放距离（保证不会太远）
	controls.minDistance = minDistance;
	controls.maxDistance = maxDistance;

	const direction = controls.target.clone().sub(camera.position).normalize().multiplyScalar(distance);
	// controls.minDistance = 300;
	controls.target.copy(center);

	camera.near = 0.1;
	camera.far = 1000;
	camera.updateProjectionMatrix();
	camera.position.copy(controls.target).sub(direction);

	controls.update();
}
//加载stl文件
let sceneStl, cameraStl, rendererStl, controlsStl;
// 存储每个鞋楦对应的模型和场景
const stlScenesMap = ref<{ [key: number]: any }>({});
const stlModelsMap = ref<{ [key: number]: any }>({});

async function initStl(fileMode) {
	// 当前选中的鞋楦索引
	const currentIndex = activeIndex.value !== null ? activeIndex.value : 0;

	// 清理旧的渲染器（如果存在）
	if (rendererStl) {
		const container = document.querySelector('.square');
		if (container && container.contains(rendererStl.domElement)) {
			container.removeChild(rendererStl.domElement);
		}
	}

	// 创建一个全新的场景，避免使用已有场景可能存在的引用问题
	sceneStl = new THREE.Scene();

	// 创建背景
	const canvas = document.createElement('canvas');
	const canvasWidth = 250; // Canvas 宽度
	const canvasHeight = 250; // Canvas 高度
	canvas.width = canvasWidth;
	canvas.height = canvasHeight;
	const context = canvas.getContext('2d');

	// 定义背景颜色
	const color1 = '#d0d0d0'; // 灰色
	const color2 = '#ffffff'; // 白色

	// 绘制灰白相间的菱形网格
	for (let x = 0; x < canvasWidth; x += 20) {
		for (let y = 0; y < canvasHeight; y += 20) {
			const color = (x + y) % 40 === 0 ? color2 : color1; // 交替颜色
			context.fillStyle = color;
			context.fillRect(x, y, 20, 20);
		}
	}
	// 创建纹理
	const texture = new THREE.CanvasTexture(canvas);
	sceneStl.background = texture;

	// 每次都创建新的相机，避免不同鞋楦之间的视角混淆
	cameraStl = new THREE.PerspectiveCamera(60, 250 / 250, 0.1, 1000);
	cameraStl.position.set(15, -20, 5);

	// 每次都创建新的渲染器
	rendererStl = new THREE.WebGLRenderer({ antialias: true });
	rendererStl.setPixelRatio(window.devicePixelRatio);
	rendererStl.setSize(250, 250);
	const container = document.querySelector('.square');
	if (container) {
		container.innerHTML = ''; // 清空容器
		container.appendChild(rendererStl.domElement);
	}

	// 创建新的控件
	if (controlsStl) {
		controlsStl.dispose(); // 清理旧控件
	}
	controlsStl = new OrbitControls(cameraStl, rendererStl.domElement);
	controlsStl.minDistance = 30;

	// 添加光源
	const directionalLight = new THREE.DirectionalLight(0xffffff);
	directionalLight.intensity = 2;
	directionalLight.target.position.set(0, 0, 0);
	sceneStl.add(directionalLight);

	const ambientLight = new THREE.AmbientLight();
	sceneStl.add(ambientLight);

	// 检查模型地址是否为空
	if (!stlAddress.value) {
		console.error('模型地址为空，无法加载模型');
		ElMessage.error('模型地址为空，请重新上传文件');
		return;
	}

	let loader = null;
	if (fileMode === 'stl') {
		loader = new STLLoader();
	} else if (fileMode === 'obj') {
		loader = new OBJLoader();
	} else if (fileMode === 'mtl') {
		loader = new MTLLoader();
	} else if (fileMode === 'stp') {
		loader = new STLLoader();
	} else if (fileMode === '3dm') {
		loader = new Rhino3dmLoader();
		loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');
	}

	if (loader === null) {
		ElMessage.error('没有找到匹配的加载器');
	} else {
		loader.load(
			stlAddress.value,
			function (object) {
				const material = new THREE.MeshNormalMaterial({ wireframe: true });
				const materialstlMesh = new THREE.MeshPhysicalMaterial({
					side: THREE.DoubleSide,
					roughness: 1, //表面粗糙度
					clearcoat: 1.0,
					clearcoatRoughness: 0.1,
					polygonOffset: true,
					polygonOffsetFactor: 1, // 调整描边模型的深度
					polygonOffsetUnits: 1,
				});
				let mesh = null;
				if (fileMode === 'stl' || fileMode === 'stp') {
					mesh = new THREE.Mesh(object, materialstlMesh);
				} else if (fileMode === 'obj' || fileMode === 'mtl') {
					const geometry = object.children[0].geometry;
					mesh = new THREE.Mesh(geometry, materialstlMesh);
				} else if (fileMode === '3dm') {
					object.traverse((child) => {
						if (child.isMesh) {
							child.material = materialstlMesh;
						}
					}, false);
					mesh = object;
				}

				if (mesh) {
					mesh.scale.set(0.1, 0.1, 0.1);
					const boundingBox = new THREE.Box3().setFromObject(mesh);
					// 计算模型尺寸
					const size = new THREE.Vector3();
					boundingBox.getSize(size);
					const modelCenter = new THREE.Vector3();
					boundingBox.getCenter(modelCenter);
					//设置旋转中心是模型的中心
					controlsStl.target.copy(modelCenter);

					// 添加到场景
					sceneStl.add(mesh);

					// 保存场景状态
					stlScenesMap.value[currentIndex] = {
						scene: sceneStl,
						camera: cameraStl,
						controls: controlsStl,
					};
				} else {
					console.error('Failed to load model: Invalid file type');
				}
			},
			undefined,
			function (error) {
				console.error('Failed to load STL model:', error);
			}
		);
	}

	// 监听窗口大小调整
	window.addEventListener('resize', onWindowResizeStl, false);

	animateStl();
}
function onWindowResizeStl() {
	if (!cameraStl || !rendererStl) return;

	cameraStl.aspect = 250 / 250;
	cameraStl.updateProjectionMatrix();
	rendererStl.setSize(250, 250);

	// 安全地执行渲染
	try {
		if (sceneStl && cameraStl) {
			rendererStl.render(sceneStl, cameraStl);
		}
	} catch (error) {
		console.error('调整大小时渲染失败:', error);
	}
}

// 控制动画循环的标志
const isStlAnimating = ref(true);

function animateStl() {
	// 如果不再需要动画，就不再请求新的帧
	if (!isStlAnimating.value) return;

	// 安全地请求下一帧
	const animationId = requestAnimationFrame(animateStl);

	// 存储动画ID以便能够取消
	window.stlAnimationId = animationId;

	// 确保所有对象都存在
	if (controlsStl && rendererStl && sceneStl && cameraStl) {
		controlsStl.update();
		try {
			rendererStl.render(sceneStl, cameraStl);
		} catch (error) {
			console.error('渲染STL出错:', error);
			// 出错时停止动画循环
			isStlAnimating.value = false;
			cancelAnimationFrame(window.stlAnimationId);
		}
	}
}

//加载3dm文件
async function initRhino() {
	console.log('开始渲染3dm');
	timing();
	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');

	const API = {
		color: 0xffffff, // sRGB
		exposure: 1.0,
	};

	const manager = new THREE.LoadingManager(renderer);
	const loaderEXR = new EXRLoader(manager);
	const matcap = loaderEXR.load('https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/gh/040full.exr');
	// load rhino doc into three.js scene
	loader.load(
		rhinoAddress.value,
		function (object) {
			let modelCount = 0; // 用于计数模型数量
			const totalModels = object.children.length; // 获取模型总数

			// 判断总模型数是否为 5，如果是 5 才做最后一个模型跳过材质设置
			const skipLastModel = totalModels === 6;

			object.traverse((child) => {
				if (child.isMesh) {
					modelCount++; // 当前模型计数递增

					if (skipLastModel && modelCount === totalModels) {
						// 如果模型总数是 5 且当前是最后一个模型，跳过设置材质
						console.log('Skipping material for the last model');
					} else {
						// 设置材质
						const skinMaterial = new THREE.MeshMatcapMaterial({
							color: new THREE.Color().setHex(API.color).convertSRGBToLinear(),
							matcap: matcap,
							side: THREE.DoubleSide,
						});

						child.material = skinMaterial;
					}
				}
			}, false);

			loadedObjects.value.forEach((obj) => scene.remove(obj));
			loadedObjects.value = [];

			object.children.forEach((child, index) => {
				// console.log(`Processing child ${index}:`, child);
				if (child.isMesh) {
					loadedObjects.value.push(child);
					// scene.add(child);
				}
			});

			loadedObject.value = object;

			// zoom to extents
			zoomCameraToSelection(camera, controls, object.children);

			// 生成控制对象显示的复选框和材质控制
			generateObjectControls();

			// add object graph from rhino model to three.js scene
			scene.add(object);
			showSpinner(false);
		},
		function (error) {
			console.error('An error occurred while loading the model:', error);
		}
	);
}
onBeforeUnmount(() => {
	// 停止所有动画
	stopAnimation();

	// 清理Three.js资源
	disposeThreeJsResources();

	// 移除事件监听器
	window.removeEventListener('resize', onWindowResize);
	window.removeEventListener('resize', onWindowResizeStl);

	// 清理rhino3dm资源
	[doc, Ndoc, Fdoc, Fundoc, CPadoc].forEach((doc) => {
		if (doc) doc.delete();
	});

	// 清理STL相关资源
	if (sceneStl) {
		sceneStl.clear();
	}

	if (rendererStl) {
		rendererStl.dispose();
		rendererStl.forceContextLoss();
		rendererStl.domElement?.remove();
	}

	// 释放所有模型的内存
	Object.values(stlModelsMap.value).forEach((model) => {
		if (model.geometry) model.geometry.dispose();
		if (Array.isArray(model.material)) {
			model.material.forEach((mat) => mat.dispose());
		} else if (model.material) {
			model.material.dispose();
		}
	});

	// 清空映射对象
	stlModelsMap.value = {};
	stlScenesMap.value = {};

	// 移除事件监听器
	window.removeEventListener('resize', onWindowResizeStl);
});

function disposeThreeJsResources() {
	// 首先停止所有动画
	stopAnimation();

	// 清理渲染器
	if (renderer) {
		renderer.dispose();
		renderer.forceContextLoss();
		renderer.domElement?.remove();
		renderer = null;
	}

	// 清理场景中的对象
	if (scene) {
		scene.traverse((object) => {
			if (object.isMesh) {
				object.geometry?.dispose();
				if (Array.isArray(object.material)) {
					object.material.forEach((material) => material.dispose());
				} else {
					object.material?.dispose();
				}
			}
		});
		scene.clear();
		scene = null;
	}

	// 清理控制器
	if (controls) {
		controls.dispose();
		controls = null;
	}

	// 清理相机
	camera = null;

	// 清理加载器和其他资源
	if (loadedObjects.value) {
		loadedObjects.value = [];
	}
}

// 添加selectImage方法用于处理图片选择
const selectImage = (image) => {
	selectedImage.value = image;
	const selectedFile = imagesWithFiles.value.find((item: any) => item.image === image);
	if (selectedFile) {
		selectedFootLastFile.value = selectedFile.file;
		selectedShoeItem.value = selectedFile; // 保存选中项的所有信息
	}
};

// 自动选择第一个项目的辅助函数
const selectFirstItem = () => {
	if (imagesWithFiles.value && imagesWithFiles.value.length > 0) {
		const firstItem = imagesWithFiles.value[0];
		if (firstItem && firstItem.image) {
			selectImage(firstItem.image);
		}
	}
};

// 为每个鞋楦创建独立的解析状态
const lastAnalysisStatusList = ref<{ [key: number]: any }>({});

// 全局状态（兼容旧代码）
const lastAnalysisStatus = computed(() => {
	if (activeIndex.value !== null && lastAnalysisStatusList.value[activeIndex.value]) {
		return lastAnalysisStatusList.value[activeIndex.value];
	}
	return {
		loading: false,
		finished: false,
		success: false,
	};
});

// 为每个鞋楦创建解析结果
const FootLastAnalysisList = ref<{ [key: number]: any }>({});

// 声明包含多个属性的响应式对象（兼容旧代码）
const FootLastAnalysis = computed(() => {
	if (activeIndex.value !== null && FootLastAnalysisList.value[activeIndex.value]) {
		return FootLastAnalysisList.value[activeIndex.value];
	}
	return {
		CockingForward: '',
		LastLong: '',
		LengthOfLast: '',
		WidthOfLast: '',
		SoleGith: '',
		ShoeToeShape: '',
		PossibleShoeSize: {},
	};
});

// 添加跟高调节（为每个鞋楦独立存储）
const ggtjcsList = ref<{ [key: number]: number }>({});
const ggtjcs1List = ref<{ [key: number]: string }>({});

// 当前鞋楦的跟高调节值（兼容旧代码）
const ggtjcs = computed({
	get: () => {
		const idx = activeIndex.value !== null ? activeIndex.value : 0;
		return typeof ggtjcsList.value[idx] !== 'undefined' ? ggtjcsList.value[idx] : 0;
	},
	set: (val: number) => {
		const idx = activeIndex.value !== null ? activeIndex.value : 0;
		ggtjcsList.value[idx] = val;
	},
});

const ggtjcs1 = computed({
	get: () => {
		const idx = activeIndex.value !== null ? activeIndex.value : 0;
		return ggtjcs1List.value[idx] || '0.0';
	},
	set: (val: string) => {
		const idx = activeIndex.value !== null ? activeIndex.value : 0;
		ggtjcs1List.value[idx] = val;
	},
});

// 格式化值（去除引号）
const formatValue = (value: string) => {
	if (!value) return '';
	return value.replace(/['"]/g, '');
};

// 格式化鞋码表数据
const formatSizeTableData = (sizeData: any) => {
	if (!sizeData) return [];

	const tableData = [];

	// 遍历所有路径获取数据
	for (const path in sizeData) {
		const values = sizeData[path];
		const row: any = {
			footLength: '', // 脚长
			worldSize1: '', // 世界鞋号(号差1)
			worldSize2: '', // 世界鞋号(号差2)
			euroSize: '', // 欧洲鞋号
			ukSize: '', // 英国鞋号
			usSizeMen: '', // 美国男鞋号
			usSizeWomen: '', // 美国女鞋号
		};

		// 从值中提取数字
		for (const value of values) {
			const formattedValue = formatValue(value);
			if (formattedValue.includes('脚长')) {
				row.footLength = formattedValue.split('：')[1];
			} else if (formattedValue.includes('世界鞋号/中国鞋号/日本鞋号号差1')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.worldSize1 = match[1];
			} else if (formattedValue.includes('世界鞋号/中国鞋号/日本鞋号号差2')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.worldSize2 = match[1];
			} else if (formattedValue.includes('欧洲鞋号')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.euroSize = match[1];
			} else if (formattedValue.includes('英国鞋号')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.ukSize = match[1];
			} else if (formattedValue.includes('美国鞋号男')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.usSizeMen = match[1];
			} else if (formattedValue.includes('美国鞋号女')) {
				const match = formattedValue.match(/：(\d+\.?\d*)/);
				if (match) row.usSizeWomen = match[1];
			}
		}

		// 只有当行中有数据时才添加到表格中
		if (row.footLength || row.worldSize1 || row.worldSize2 || row.euroSize || row.ukSize || row.usSizeMen || row.usSizeWomen) {
			tableData.push(row);
		}
	}

	return tableData;
};

// 添加更新选择数据的方法
const updateSelectData = () => {
	// 清空 selectData
	Object.keys(selectData.value).forEach((key) => {
		selectData.value[key] = [];
	});

	// 根据 selectedOptions 更新 selectData
	Object.entries(selectedOptions.value).forEach(([key, values]) => {
		if (Array.isArray(values)) {
			const selectedNames = values
				.map((value) => {
					// 查找 typeData 中与 selectedOptions 值对应的 nameType
					const item = typeData.value.find((type) => type.id === key);
					if (item) {
						const child = item.children.find((child) => child.id === value);
						return child ? child.nameType : null;
					}
					return null;
				})
				.filter((name) => name !== null);
			// 更新 selectData 对应字段
			const typeName = typeData.value.find((type) => type.id === key)?.ename;
			if (typeName) {
				selectData.value[typeName] = selectedNames;
			}
		}
	});

	selectData.value.userId = 1;
};

// 添加鞋楦解析的 3D 相关变量
let anaDoc;
let sceneAnay, cameraAnay, rendererAnay, controlsAnay;
const loadedObjectsAnaMap = ref<{ [key: number]: any[] }>({});
const loadedObjectAnaMap = ref<{ [key: number]: any }>({});

// 兼容旧代码
const loadedObjectsAna = computed(() => {
	if (activeIndex.value !== null && activeIndex.value !== undefined && Array.isArray(loadedObjectsAnaMap.value[activeIndex.value])) {
		return loadedObjectsAnaMap.value[activeIndex.value];
	}
	return [];
});

const loadedObjectAna = computed({
	get: () => {
		if (activeIndex.value !== null) {
			return loadedObjectAnaMap.value[activeIndex.value];
		}
		return undefined;
	},
	set: (val: any) => {
		if (activeIndex.value !== null) {
			loadedObjectAnaMap.value[activeIndex.value] = val;
		}
	},
});

// 判断是否为翘度调整模式
const isQiaoDuAdjustMode = computed(() => {
	return definitionName.value === 'qiaodutiaozheng';
});

// 计算显示的设计方式名称 - 使用可写的计算属性
const displayDefinitionName = computed({
	get: () => {
		if (definitionName.value === 'qiaodutiaozheng') {
			return 'qiaodutiaozheng';
		} else if (definitionName.value.startsWith('xuanronghhe')) {
			return 'xuanronghhe';
		} else {
			return definitionName.value;
		}
	},
	set: (newValue) => {
		// 当用户手动切换设计方式时，更新definitionName
		if (newValue === 'qiaodutiaozheng') {
			definitionName.value = 'qiaodutiaozheng';
			// 切换到翘度调整模式时，清理多余文件
			cleanupExtraFiles();
		} else if (newValue === 'xuanronghhe') {
			// 根据当前文件数量设置正确的楦融合类型
			const fileCount = fileInputs.value.length;
			if (fileCount >= 2 && fileCount <= 6) {
				definitionName.value = `xuanronghhe${fileCount}`;
			} else {
				definitionName.value = 'xuanronghhe';
			}
		}

		// 使用nextTick确保DOM更新完成
		nextTick(() => {
			// 重置防循环标志
			isUpdatingDefinitionName.value = false;
		});
	},
});

const anaDocMap = ref<{ [key: number]: Uint8Array }>({});

// 添加鞋楦解析方法
const analysis = async (item: string) => {
	// 确保是解析当前选中的鞋楦
	if (activeIndex.value === null || activeIndex.value < 0 || !stlFile.value[activeIndex.value] || !stlFile.value[activeIndex.value].data) {
		ElMessage.error('请先选择要解析的鞋楦');
		return;
	}

	try {
		// 为当前鞋楦创建/重置状态
		const currentIndex = activeIndex.value;

		// 设置加载状态
		lastAnalysisStatusList.value[currentIndex] = {
			loading: true,
			finished: false,
			success: false,
		};

		// 每次解析前确保我们有一个干净的anaDoc
		await loadAnalysisURL();

		// 解析当前鞋楦
		await computeAnalysis(currentIndex);

		// 确保当前鞋楦的解析结果对象存在
		if (!FootLastAnalysisList.value[currentIndex]) {
			FootLastAnalysisList.value[currentIndex] = {
				CockingForward: '',
				LastLong: '',
				LengthOfLast: '',
				WidthOfLast: '',
				SoleGith: '',
				ShoeToeShape: '',
				PossibleShoeSize: {},
			};
		}

		// 检查当前鞋楦的解析结果
		const currentResult = FootLastAnalysisList.value[currentIndex];
		const hasResult =
			currentResult.CockingForward ||
			currentResult.LastLong ||
			currentResult.LengthOfLast ||
			currentResult.WidthOfLast ||
			currentResult.SoleGith ||
			currentResult.ShoeToeShape ||
			(currentResult.PossibleShoeSize && Object.keys(currentResult.PossibleShoeSize).length > 0);

		// 更新当前鞋楦的状态
		lastAnalysisStatusList.value[currentIndex].success = hasResult;
		lastAnalysisStatusList.value[currentIndex].loading = false;
		lastAnalysisStatusList.value[currentIndex].finished = true;

		// 如果解析成功，为当前鞋楦创建独立的解析文档副本
		if (hasResult && anaDoc) {
			try {
				// 先清理旧的文档（如果存在）
				if (anaDocMap.value[currentIndex] && typeof anaDocMap.value[currentIndex].delete === 'function') {
					anaDocMap.value[currentIndex].delete();
				}

				// 创建文档副本以独立存储当前鞋楦的解析结果
				const docBytes = anaDoc.toByteArray();
				anaDocMap.value[currentIndex] = docBytes;

				// 在下一个周期更新UI显示新的解析结果
				nextTick(() => {
					initAnay();
				});
			} catch (error) {
				console.error('创建解析文档副本失败:', error);
			}
		}
	} catch (error) {
		console.error('解析失败:', error);
		if (activeIndex.value !== null) {
			lastAnalysisStatusList.value[activeIndex.value] = {
				loading: false,
				finished: true,
				success: false,
			};
		}
		ElMessage.error('解析失败: ' + (error.message || '未知错误'));
	}
};

// 加载鞋楦解析的 URL
const analysisDefinition = ref();

async function loadAnalysisURL() {
	try {
		const m = await rhino3dm();
		console.log('Loaded rhino3dm for analysis.');
		rhino = m;

		RhinoCompute.url = 'http://************:80/';
		RhinoCompute.apiKey = 'c8cdc5f3d46143b664d72d039b5832fc';

		// 使用后端代理API获取分析文件，而不是直接fetch外部URL
		const response = await downloadAnalysisFile('last');

		// 处理响应，判断是直接的ArrayBuffer还是包含data的响应对象
		let arrayBuffer;
		if (response instanceof ArrayBuffer) {
			// 直接是ArrayBuffer
			arrayBuffer = response;
		} else if (response.data instanceof ArrayBuffer) {
			// 是响应对象，data是ArrayBuffer
			arrayBuffer = response.data;
		} else {
			throw new Error('响应数据格式不正确，期望ArrayBuffer类型');
		}

		analysisDefinition.value = new Uint8Array(arrayBuffer);

		// 确保anaDoc已创建
		if (!anaDoc) {
			anaDoc = new rhino.File3dm();
		} else {
			// 清除旧的内容
			anaDoc.delete();
			anaDoc = new rhino.File3dm();
		}
	} catch (error) {
		console.error('加载鞋楦解析模型时出错:', error);
		ElMessage.error('加载鞋楦解析模型时出错，请检查网络连接和 URL 设置.');
	}
}

// 计算鞋楦解析
async function computeAnalysis(currentIndex: number) {
	// 通过 calculateFootprints 方法获取正确的地址
	let footLast = stlFile.value[currentIndex].data;
	let footLastPath = address.value + footLast.split('/').pop();

	// 获取当前鞋楦的跟高值
	let GGTJCS = ggtjcsList.value[currentIndex] || 0;

	let param1 = new RhinoCompute.Grasshopper.DataTree('FootLastFile');
	param1.append([0], [footLastPath]);
	let param2 = new RhinoCompute.Grasshopper.DataTree('GGTJCS');
	param2.append([1], [GGTJCS]);

	let trees = [];
	trees.push(param1);
	trees.push(param2);

	// 调用 RhinoCompute
	const res = await RhinoCompute.Grasshopper.evaluateDefinition(analysisDefinition.value, trees);
	// console.log('解析结果：', res);

	if (!res) {
		throw new Error('Failed to compute analysis');
	}

	// 收集当前鞋楦的解析结果
	collectResultsFootLastAnalysis(res, currentIndex);

	// 初始化和显示3D模型
	initAnay();
}

// 收集鞋楦解析结果
function collectResultsFootLastAnalysis(responseJson: any, currentIndex: number) {
	const values = responseJson.values;

	// 确保当前索引对应的鞋楦有解析结果对象
	if (!FootLastAnalysisList.value[currentIndex]) {
		FootLastAnalysisList.value[currentIndex] = {
			CockingForward: '',
			LastLong: '',
			LengthOfLast: '',
			WidthOfLast: '',
			SoleGith: '',
			PossibleShoeSize: {},
			ShoeToeShape: '',
		};
	}

	// 解析数据映射
	const dataMapping: { [key: string]: { path: string; treePath: string; simplePath: string } } = {
		0: {
			path: 'CockingForward',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		1: {
			path: 'LastLong',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		2: {
			path: 'LengthOfLast',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		3: {
			path: 'WidthOfLast',
			treePath: '{0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		5: {
			path: 'SoleGith',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		6: {
			path: 'PostiveShoeLast',
			treePath: '{0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		7: {
			path: 'LastBottomSideLine',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		8: {
			path: 'SoleCirLine',
			treePath: '{0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
		9: {
			path: 'ShoeToeShape',
			treePath: '{0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0;0}',
			simplePath: '{0}',
		},
	};

	// 使用映射处理数据
	Object.entries(dataMapping).forEach(([index, config]) => {
		const value = values[parseInt(index)]; // Ensure index is number for accessing values array
		const branch = value?.InnerTree[config.treePath];
		if (value?.InnerTree) {
			// 首先尝试使用完整路径
			if (value.InnerTree[config.treePath]?.[0]?.data) {
				FootLastAnalysisList.value[currentIndex][config.path] = value.InnerTree[config.treePath][0].data;
			}
			// 如果完整路径没有数据，尝试使用简单路径
			else if (value.InnerTree[config.simplePath]?.[0]?.data) {
				FootLastAnalysisList.value[currentIndex][config.path] = value.InnerTree[config.simplePath][0].data;
			}
		}
		if (branch) {
			for (let j = 0; j < branch.length; j++) {
				// ...load rhino geometry into doc
				const rhinoObject = decodeItem(branch[j]);
				if (rhinoObject !== null) {
					anaDoc.objects().add(rhinoObject, null);
					// anaDoc.objects().delete(rhinoObject); // REMOVED as per previous discussion
				}
			}
		}
	});

	// 特殊处理values[4]，保存鞋码数据
	if (values[4]) {
		// 初始化一个对象来按路径存储鞋码值
		const shoeSizeGroups: { [key: string]: string[] } = {};

		// 遍历values[4]的InnerTree中的所有路径
		for (const path in values[4].InnerTree) {
			const dataArray = values[4].InnerTree[path];
			// 为每个路径创建一个数组
			shoeSizeGroups[path] = [];

			// 遍历该路径下的所有数据
			for (const item of dataArray) {
				if (item?.data !== undefined) {
					shoeSizeGroups[path].push(item.data);
				}
			}
		}

		// 将分组后的鞋码值存储到当前鞋楦的解析结果中
		FootLastAnalysisList.value[currentIndex].PossibleShoeSize = shoeSizeGroups;
	}

	// MODIFIED: Store byte array instead of File3dm object
	// Check if there are any valid results before attempting to store
	const currentResult = FootLastAnalysisList.value[currentIndex];
	const hasResult =
		currentResult.CockingForward ||
		currentResult.LastLong ||
		currentResult.LengthOfLast ||
		currentResult.WidthOfLast ||
		currentResult.SoleGith ||
		currentResult.ShoeToeShape ||
		(currentResult.PossibleShoeSize && Object.keys(currentResult.PossibleShoeSize).length > 0);

	if (hasResult && anaDoc) {
		// anaDoc is the global one populated with results
		try {
			// MODIFICATION: Store byte array instead of File3dm object
			const docBytes = anaDoc.toByteArray();
			anaDocMap.value[currentIndex] = docBytes; // Store raw bytes

			// 在下一个周期更新UI显示新的解析结果
			nextTick(() => {
				initAnay();
			});
		} catch (error) {
			console.error('存储解析字节数据失败:', error); // 更具体的错误信息
		}
	}
}

// 添加事件监听器清理
onBeforeUnmount(() => {
	console.log('组件即将卸载，开始清理资源');

	// 首先停止所有动画循环
	isStlAnimating.value = false;
	isAnayAnimating.value = false;

	// 安全地取消所有动画帧请求
	if (window.stlAnimationId) {
		cancelAnimationFrame(window.stlAnimationId);
		window.stlAnimationId = null;
	}

	if (window.anayAnimationId) {
		cancelAnimationFrame(window.anayAnimationId);
		window.anayAnimationId = null;
	}

	// 立即清理资源，不使用延迟
	// 移除鞋楦解析相关的事件监听器
	window.removeEventListener('resize', onWindowResizeAnay);
	window.removeEventListener('resize', onWindowResizeStl);

	try {
		// 清理所有鞋楦的3D对象
		Object.keys(loadedObjectsAnaMap.value).forEach((key) => {
			const objList = loadedObjectsAnaMap.value[parseInt(key)];
			if (Array.isArray(objList)) {
				objList.forEach((obj) => {
					if (obj && obj.geometry) obj.geometry.dispose();
					if (obj && obj.material) {
						if (Array.isArray(obj.material)) {
							obj.material.forEach((mat) => mat.dispose());
						} else {
							obj.material.dispose();
						}
					}
				});
			}
		});

		// 清理STL模型与场景映射中的每个模型对象
		Object.values(stlModelsMap.value).forEach((model) => {
			if (model) {
				if (model.geometry) model.geometry.dispose();
				if (model.material) {
					if (Array.isArray(model.material)) {
						model.material.forEach((mat) => mat.dispose());
					} else {
						model.material.dispose();
					}
				}
			}
		});

		// 安全地释放渲染器
		if (rendererAnay) {
			rendererAnay.dispose();
			rendererAnay.forceContextLoss();
			if (rendererAnay.domElement && rendererAnay.domElement.parentNode) {
				rendererAnay.domElement.parentNode.removeChild(rendererAnay.domElement);
			}
			rendererAnay = null;
		}

		if (rendererStl) {
			rendererStl.dispose();
			rendererStl.forceContextLoss();
			if (rendererStl.domElement && rendererStl.domElement.parentNode) {
				rendererStl.domElement.parentNode.removeChild(rendererStl.domElement);
			}
			rendererStl = null;
		}

		// 清理所有场景
		if (sceneAnay) {
			sceneAnay.traverse((obj) => {
				if (obj.geometry) obj.geometry.dispose();
				if (obj.material) {
					if (Array.isArray(obj.material)) {
						obj.material.forEach((mat) => mat.dispose());
					} else {
						obj.material.dispose();
					}
				}
			});
			sceneAnay.clear();
			sceneAnay = null;
		}

		if (sceneStl) {
			sceneStl.traverse((obj) => {
				if (obj.geometry) obj.geometry.dispose();
				if (obj.material) {
					if (Array.isArray(obj.material)) {
						obj.material.forEach((mat) => mat.dispose());
					} else {
						obj.material.dispose();
					}
				}
			});
			sceneStl.clear();
			sceneStl = null;
		}

		// 清理控制器
		if (controlsAnay) {
			controlsAnay.dispose();
			controlsAnay = null;
		}

		if (controlsStl) {
			controlsStl.dispose();
			controlsStl = null;
		}

		// 清空相机引用
		cameraAnay = null;
		cameraStl = null;

		// 清理Three.js全局缓存
		THREE.Cache.clear();

		// 清理鞋楦解析相关的rhino3dm资源
		if (anaDoc) {
			try {
				anaDoc.delete();
				anaDoc = null;
			} catch (error) {
				console.error('清理anaDoc失败:', error);
			}
		}

		// 重置映射对象
		stlModelsMap.value = {};
		stlScenesMap.value = {};
		stlAddressMap.value = {};
		loadedObjectsAnaMap.value = {};
		loadedObjectAnaMap.value = {};
	} catch (error) {
		console.error('清理资源时发生错误:', error);
	}
});

// 初始化鞋楦解析模型的显示
async function initAnay() {
	if (activeIndex.value === null || activeIndex.value === undefined) {
		console.warn('initAnay: 没有选中的鞋楦索引');
		return;
	}

	const currentIndex = activeIndex.value;
	const container = document.querySelector('.square');

	// 清理旧的渲染器和场景 (如果存在)
	if (rendererAnay) {
		if (container && container.contains(rendererAnay.domElement)) {
			container.removeChild(rendererAnay.domElement);
		}
		rendererAnay.dispose();
		rendererAnay.forceContextLoss(); // 强制释放 WebGL 上下文
		rendererAnay = null;
	}
	if (sceneAnay) {
		while (sceneAnay.children.length > 0) {
			const child = sceneAnay.children[0];
			sceneAnay.remove(child);
			// 彻底清理子对象的几何体和材质
			if (child.geometry) child.geometry.dispose();
			if (child.material) {
				if (Array.isArray(child.material)) {
					child.material.forEach((mat) => mat.dispose());
				} else {
					child.material.dispose();
				}
			}
		}
		sceneAnay.clear(); // 清理场景
		sceneAnay = null;
	}
	if (controlsAnay) {
		controlsAnay.dispose();
		controlsAnay = null;
	}
	if (window.anayAnimationId) {
		cancelAnimationFrame(window.anayAnimationId);
		window.anayAnimationId = null;
	}
	isAnayAnimating.value = true; // 重置动画标志

	const modelUint8Array = anaDocMap.value[currentIndex];

	if (!modelUint8Array || modelUint8Array.length === 0) {
		console.warn(`initAnay: No analysis data (Uint8Array) found or it's empty for index ${currentIndex}. Clearing display.`);
		if (container) container.innerHTML = '';
		if (sceneAnay) {
			sceneAnay = new THREE.Scene();
			const canvas = document.createElement('canvas');
			canvas.width = 250;
			canvas.height = 250;
			const context = canvas.getContext('2d');
			if (context) {
				// MODIFIED: Consistent background grid
				const color1 = '#d0d0d0';
				const color2 = '#ffffff';
				for (let x = 0; x < canvas.width; x += 20) {
					for (let y = 0; y < canvas.height; y += 20) {
						const color = (x + y) % 40 === 0 ? color2 : color1;
						context.fillStyle = color;
						context.fillRect(x, y, 20, 20);
					}
				}
			}
			sceneAnay.background = new THREE.CanvasTexture(canvas);
		}
		if (rendererAnay && sceneAnay && cameraAnay) rendererAnay.render(sceneAnay, cameraAnay);
		return;
	}

	if (!rhino) {
		console.error('initAnay: Rhino3dm not initialized!');
		ElMessage.error('核心模块未准备好，无法显示解析模型。');
		if (container) container.innerHTML = '';
		return;
	}

	const bufferForLoader = modelUint8Array.buffer.slice(modelUint8Array.byteOffset, modelUint8Array.byteLength + modelUint8Array.byteOffset);

	sceneAnay = new THREE.Scene();
	const canvasBg = document.createElement('canvas');
	canvasBg.width = 250;
	canvasBg.height = 250;
	const contextBg = canvasBg.getContext('2d');
	if (contextBg) {
		// MODIFIED: Consistent background grid
		const color1 = '#d0d0d0';
		const color2 = '#ffffff';
		for (let x = 0; x < canvasBg.width; x += 20) {
			for (let y = 0; y < canvasBg.height; y += 20) {
				const color = (x + y) % 40 === 0 ? color2 : color1;
				contextBg.fillStyle = color;
				contextBg.fillRect(x, y, 20, 20);
			}
		}
	}
	sceneAnay.background = new THREE.CanvasTexture(canvasBg);

	cameraAnay = new THREE.PerspectiveCamera(60, 250 / 250, 0.1, 10000);
	cameraAnay.position.set(15, -20, 5); // MODIFIED: Camera position

	rendererAnay = new THREE.WebGLRenderer({ antialias: true });
	rendererAnay.setPixelRatio(window.devicePixelRatio);
	rendererAnay.setSize(250, 250);

	if (container) {
		container.innerHTML = '';
		container.appendChild(rendererAnay.domElement);
	} else {
		console.error("initAnay: '.square' container not found!");
		return;
	}

	controlsAnay = new OrbitControls(cameraAnay, rendererAnay.domElement);
	controlsAnay.minDistance = 30; // MODIFIED: Controls minDistance
	controlsAnay.enablePan = true;

	const directionalLight = new THREE.DirectionalLight(0xffffff, 2); // MODIFIED: Light intensity from FootCompute
	directionalLight.target.position.set(0, 0, 0);
	sceneAnay.add(directionalLight);

	const ambientLight = new THREE.AmbientLight(); // MODIFIED: Simpler ambient light from FootCompute
	sceneAnay.add(ambientLight);

	const loader = new Rhino3dmLoader();
	loader.setLibraryPath('https://cdn.jsdelivr.net/npm/rhino3dm@8.4.0/');

	loader.parse(
		bufferForLoader,
		function (object) {
			if (!sceneAnay) {
				console.warn('initAnay: sceneAnay was cleared before model could be added.');
				return;
			}
			if (loadedObjectAnaMap.value[currentIndex]) {
				sceneAnay.remove(loadedObjectAnaMap.value[currentIndex]);
			}
			loadedObjectAnaMap.value[currentIndex] = object;

			// MODIFIED: Traverse and apply materials similar to FootCompute.vue
			object.traverse((child) => {
				if (child.isMesh) {
					const meshMaterial = new THREE.MeshPhysicalMaterial({
						// MODIFIED: MeshPhysicalMaterial
						side: THREE.DoubleSide,
						roughness: 1,
						clearcoat: 1.0,
						clearcoatRoughness: 0.1,
						polygonOffset: true,
						polygonOffsetFactor: 1,
						polygonOffsetUnits: 1,
					});
					child.material = meshMaterial;
				} else if (child.type === 'NurbsCurve' || child.type === 'PolylineCurve' || child.isLine) {
					// Added child.isLine for broader line detection
					const points = [];
					if (child.geometry && child.geometry.attributes && child.geometry.attributes.position) {
						const positions = child.geometry.attributes.position.array;
						for (let i = 0; i < positions.length; i += 3) {
							points.push(new THREE.Vector3(positions[i], positions[i + 1], positions[i + 2]));
						}
					} else if (typeof child.getPoint === 'function') {
						// For NurbsCurve
						const divisions = 100;
						for (let i = 0; i <= divisions; i++) {
							const t = i / divisions;
							const point = child.getPoint(t);
							points.push(point);
						}
					}

					if (points.length > 1) {
						const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
						const lineMaterial = new THREE.LineDashedMaterial({
							// MODIFIED: LineDashedMaterial
							color: 0xff0000, // Red color
							dashSize: 3,
							gapSize: 1,
							linewidth: 2, // Adjusted linewidth
						});
						const line = new THREE.Line(lineGeometry, lineMaterial);
						line.computeLineDistances(); // Important for dashed lines

						// Replace child with the new line
						if (child.parent) {
							child.parent.add(line);
							child.parent.remove(child);
							// Clean up old geometry and material if they exist
							if (child.geometry) child.geometry.dispose();
							if (child.material) {
								if (Array.isArray(child.material)) {
									child.material.forEach((mat) => mat.dispose());
								} else {
									child.material.dispose();
								}
							}
						} else {
							// Fallback if no parent, directly add to object (less ideal)
							object.add(line);
						}
					} else if (child.parent) {
						// If no points, remove the original line object
						child.parent.remove(child);
						if (child.geometry) child.geometry.dispose();
						if (child.material) {
							if (Array.isArray(child.material)) {
								child.material.forEach((mat) => mat.dispose());
							} else {
								child.material.dispose();
							}
						}
					}
				}
			});

			object.scale.set(0.1, 0.1, 0.1); // MODIFIED: Scale the entire object group

			sceneAnay.add(object);

			const box = new THREE.Box3().setFromObject(object);
			const center = box.getCenter(new THREE.Vector3());
			const size = box.getSize(new THREE.Vector3());
			const maxDim = Math.max(size.x, size.y, size.z);
			const fov = cameraAnay.fov * (Math.PI / 180);
			let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
			cameraZ *= 1.5;

			cameraAnay.position.set(center.x, center.y - cameraZ, center.z);

			controlsAnay.target.copy(center);
			controlsAnay.update();

			animateAnay();
		},
		function (error) {
			console.error(`initAnay: Error parsing Rhino model for index ${currentIndex}:`, error);
			ElMessage.error('解析后的模型加载失败。');
		}
	);
	window.addEventListener('resize', onWindowResizeAnay, false);
	animateAnay();
}

// 工具函数
function onWindowResizeAnay() {
	if (cameraAnay) {
		cameraAnay.aspect = 250 / 250;
		cameraAnay.updateProjectionMatrix();
	}
	if (rendererAnay) {
		rendererAnay.setSize(250, 250);
	}
	animateAnay();
}

// 控制解析动画循环的标志
const isAnayAnimating = ref(true);

function animateAnay() {
	if (!isAnayAnimating.value) return;

	// 所有必要对象都不存在时不继续
	if (!rendererAnay || !sceneAnay || !cameraAnay) return;

	// 安全地请求下一帧
	const animationId = requestAnimationFrame(animateAnay);

	// 存储动画ID
	window.anayAnimationId = animationId;

	// 更新控制
	if (controlsAnay) controlsAnay.update();

	// 安全地尝试渲染
	try {
		rendererAnay.render(sceneAnay, cameraAnay);
	} catch (error) {
		console.error('渲染解析模型出错:', error);
		// 出错时停止动画循环
		isAnayAnimating.value = false;
		cancelAnimationFrame(window.anayAnimationId);
	}
}

// 添加一个函数来加载资源
const loadDefinitionResource = async () => {
	try {
		// 使用后端代理API获取设计方式文件，根据实际的鞋楦数量获取对应的文件类型
		const actualDefinitionName = getActualDefinitionName();
		const response = await downloadDefinitionFile(actualDefinitionName);

		// 处理响应，判断是直接的ArrayBuffer还是包含data的响应对象
		let arrayBuffer;
		if (response instanceof ArrayBuffer) {
			// 直接是ArrayBuffer
			arrayBuffer = response;
		} else if (response && response.data) {
			// 响应对象包含data字段
			arrayBuffer = response.data;
		} else {
			// 其他情况，尝试直接使用response
			arrayBuffer = response;
		}

		definition.value = new Uint8Array(arrayBuffer);
		if (arrayBuffer || definition.value) {
			console.log('计算之前-------');
			// init();
		} else {
			ElMessage.error('加载错误,Rhino计算服务未开启');
		}
	} catch (error) {
		console.error('加载资源出错:', error);
		ElMessage.error('加载资源失败，请检查网络连接');
	}
};

// 添加一个标志来防止循环更新
const isUpdatingDefinitionName = ref(false);

// 监听文件数量变化，自动更新楦融合的definitionName
watch(
	() => fileInputs.value.length,
	(newCount) => {
		// 只在楦融合模式下更新，且不在手动更新过程中
		if (!isQiaoDuAdjustMode.value && !isUpdatingDefinitionName.value && newCount >= 2 && newCount <= 6) {
			definitionName.value = `xuanronghhe${newCount}`;
		}
	}
);

// 监听definitionName的变化
watch(definitionName, async (newValue, oldValue) => {
	if (newValue !== oldValue && newValue) {
		// console.log('生成方式已变更为:', newValue);
		// 更新selectedType的值，使其匹配当前选中的选项
		const optionIndex = deFinitOptions.findIndex((option) => option.value === newValue);
		if (optionIndex !== -1) {
			selectedType.value = optionIndex;
		}
		await loadDefinitionResource();
	}
});
// 新的更新函数 - 楦融合模式权重参数
const updateDataFormValue = (index: number, value: number) => {
	DataForm.value[`data${index}`] = value;
};

const updateDataFormSlider = (index: number, value: number) => {
	DataForm.value[`data${index}`] = value;
};

// 新的更新函数 - 翘度调整模式参数
const updateBFTJCValue = (value: number) => {
	BFTJC.value = value;
	BFTJC1.value = value.toFixed(1);
};

const updateBFTJCSlider = (value: number) => {
	BFTJC.value = value;
	BFTJC1.value = value.toFixed(1);
};

const updateHeightAdjustValue = (value: number) => {
	HeightAdjust.value = value;
	HeightAdjust1.value = value.toFixed(1);
};

const updateHeightAdjustSlider = (value: number) => {
	HeightAdjust.value = value;
	HeightAdjust1.value = value.toFixed(1);
};

const updateForwardCurlValue = (value: number) => {
	ForwardCurl.value = value;
	ForwardCurl1.value = value.toFixed(1);
};

const updateForwardCurlSlider = (value: number) => {
	ForwardCurl.value = value;
	ForwardCurl1.value = value.toFixed(1);
};

// 新的更新函数 - 鞋楦解析跟高调节
const updateGgtjcsValue = (value: number) => {
	if (activeIndex.value === null) return;
	const idx = activeIndex.value;
	ggtjcsList.value[idx] = value;
	ggtjcs1List.value[idx] = value.toFixed(1);
};

const updateGgtjcsSlider = (value: number) => {
	if (activeIndex.value === null) return;
	const idx = activeIndex.value;
	ggtjcsList.value[idx] = value;
	ggtjcs1List.value[idx] = value.toFixed(1);
};

// 清理多余文件的函数
const cleanupExtraFiles = () => {
	if (fileInputs.value.length > 1) {
		// 只保留第一个标签和文件
		fileInputs.value = fileInputs.value.slice(0, 1);

		// 保留第一个文件，清空其他文件
		if (stlFile.value.length > 1) {
			const firstFile = stlFile.value[0];
			stlFile.value = [firstFile];
		}

		// 清理stlconfig中多余的文件路径
		stlconfig.value = {
			mouldfile1: stlFile.value[0]?.data || '',
			mouldfile2: '',
			mouldfile3: '',
			mouldfile4: '',
			mouldfile5: '',
			mouldfile6: '',
		};

		// 清理footLastFile中多余的文件路径
		footLastFile.value = {
			FootLast1: footLastFile.value.FootLast1 || '',
			FootLast2: '',
			FootLast3: '',
			FootLast4: '',
			FootLast5: '',
			FootLast6: '',
			LastFile: footLastFile.value.FootLast1 || '',
		};

		// 清理DataForm中多余的数据
		DataForm.value = {
			data1: DataForm.value.data1 || 0,
			data2: 0,
			data3: 0,
			data4: 0,
			data5: 0,
			data6: 0,
		};

		// 清理buttonLabels中多余的文件名（对应uploadedFileNames）
		if (buttonLabels.value && Object.keys(buttonLabels.value).length > 1) {
			const firstLabel = buttonLabels.value['0'] || buttonLabels.value[0];
			buttonLabels.value = {
				'0': firstLabel,
			};
		}

		// 设置活动索引为0
		setActiveIndex(0);
	}
};

watch(
	() => isQiaoDuAdjustMode.value,
	(newVal) => {
		if (newVal) {
			// 切换到翘度调整模式，强制清理多余文件
			cleanupExtraFiles();
		}
	}
);
</script>

<style scoped>
.headerC {
	height: 60px;
	width: 100%;
	background: #ffffff;
	display: flex;
	align-items: center;
}

.content {
	/* background: linear-gradient(to bottom right, #e0e0fe, #e0e1fc); */
	width: 100%;
	height: 100vh;
}

.imgS {
	width: 18px;
	height: 18px;
	margin-top: 2px;
	margin-right: 4px;
}

.toptext {
	margin-left: 50px;
	font-family: Alibaba PuHuiTi 3;
	font-size: 16px;
	font-weight: 500;
}

.righttext {
	position: absolute;
	display: flex;
	right: 40px;
	align-content: center;
	background-color: #ffffff;
	z-index: 2;
}

.logolayout {
	display: flex;
	font-size: 17px;
	margin-right: 10px;
}

.avatarl {
	border-radius: 40px;
	width: 25px;
	height: 25px;
	margin-top: -4px;
	margin-right: 2px;
}

.loginlogo {
	width: 169px;
	height: 32px;
}

.content {
	display: flex;
}

.aside {
	max-width: 280px;
	background-color: #f9f9ff;
}

.active-main {
	/* flex: 4 !important;
  width: 80% !important; */
	background: linear-gradient(to bottom right, #e8dff7, #e3e9ee) !important;
}
.Mdropdown {
	position: relative;
	display: inline-block;
}

.model-button {
	position: relative;
	border: 1px solid #ccc;
	padding: 8px 16px;
	width: 100vw;
	max-width: 250px;
	background-color: white;
	cursor: pointer;
}

.model-button::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	width: 4px;
	background-color: #6463ff;
}

.no-purple {
	width: 150px;
	background: rgba(100, 99, 255, 0.2);
	box-sizing: border-box;
	/* 填充/主色 */
	border: 1px solid #6463ff;
	color: #6463ff;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 800;
	margin-left: -5px;
	margin-top: 10px;
	cursor: pointer;
}

.no-purple::before {
	content: none;
}

.Mdropdown-content {
	display: flex;
	flex-direction: column;
	justify-content: center;
	padding: 10px 0px;
	margin-left: 20px;
}

.purple {
	background: #6463ff;
	border-radius: 5px;
	justify-content: center;
	align-items: center;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: normal;
	color: #ffffff;
	width: 180px;
	margin-top: 10px;
	margin-left: -2px;
}

.Pdropdown {
	position: relative;
	display: inline-block;
}

.Pdropdown-content {
	display: flex;
	flex-direction: column;
	overflow: auto;
	max-height: 450px;
	max-width: 260px;
	padding: 10px;
}
.overflow {
	max-height: 250px;
	max-width: 250px;
	overflow: auto;
}

/* @media only screen and (max-width: 520px) {
	.Pdropdown-content {
		max-height: 100px;
	}
}

@media only screen and (min-width: 520px) {
	.Pdropdown-content {
		max-height: 200px;
	}
} */
@media only screen and (max-width: 520px) {
	.Pdropdown-content {
		max-height: 50px;
		height: 100%;
	}
	/* .analysis-result {
		max-height: 70px;
	} */
	.squareAna-info {
		max-height: 70px;
	}
}

@media only screen and (min-width: 521px) and (max-width: 1119px) {
	.Pdropdown-content {
		max-height: 180px;
		height: 100%;
	}
	/* .analysis-result {
		max-height: 150px;
	} */
	.squareAna-info {
		max-height: 150px;
	}
}

@media only screen and (min-width: 1120px) and (max-width: 1569px) {
	.Pdropdown-content {
		max-height: 350px;
		height: 100%;
	}
	/* .analysis-result {
		max-height: 250px;
	} */
	.squareAna-info {
		max-height: 200px;
	}
}

@media only screen and (min-width: 1370px) and (max-width: 1500px) {
	.squareAna-info {
		max-height: 280px;
		height: 100%;
	}
}
@media only screen and (min-width: 1500px) and (max-width: 1699px) {
	.squareAna-info {
		max-height: 400px;
		height: 100%;
	}
}
@media only screen and (min-width: 1570px) {
	.bgbutton {
		top: 50px;
		z-index: 2;
	}
	.Pdropdown-content {
		max-height: 340px;
	}
}
@media only screen and (min-width: 1756px) {
	.squareAna-info {
		max-height: 460px;
		height: 100%;
	}
}
/* 缩放100 */
@media only screen and (min-width: 1860px) {
	.bgbutton {
		top: 50px;
		z-index: 2;
	}
	.Pdropdown-content {
		max-height: 450px;
	}
}
/* 缩放125 */
@media only screen and (min-width: 1920px) {
	.Pdropdown-content {
		max-height: 550px;
	}
}
/* @media only screen and (min-width: 1660px) {
	.bgbutton {
		top: 50px;
		z-index: 2;
	}
	.Pdropdown-content {
		max-height: 450px;
	}
} */

.computeC {
	position: fixed;
	width: 220px;
	background-color: #6463ff;
	color: #ffffff;
	left: 15px;
	bottom: 15px;
}

.bgbutton {
	background-color: #ffffff;
	position: absolute;
	margin-left: 248px !important;
	margin-top: 450px !important;
	z-index: 2;
}

.flex {
	padding: 20px 0;
}

.inputS {
	width: 50px;
	height: 30px;
	border: 1px solid #e5e5e5;
	border-radius: 5px;
	margin-left: 20px;
	/* margin-top: -20px; */
	font-family: Alibaba PuHuiTi 3;
	font-size: 12px;
	font-weight: normal;
	text-align: center;
}

.container {
	position: relative;
}

.overlay {
	z-index: 1;
	width: 100%;
}

.header-button {
	display: flex;
	position: absolute;
	top: 70px;
	right: 10px;
	background-color: #ffffff;
	width: 240px;
	justify-content: space-between;
	padding: 4px 20px;
	align-items: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	/* 添加阴影效果 */
	border-left: 2px solid #6463ff;
	cursor: pointer;
	/* 左侧边框为紫色，宽度为4像素 */
}

.text-result {
	font-family: Alibaba PuHuiTi 3;
	font-size: 15px;
	font-weight: 600;
	cursor: pointer;
}

.result-text {
	/* margin-right: 17px; */
	margin-top: 5px;
	color: #7472f1;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 600;
	padding: 5px 0;
	width: 80px;
}

.load-button {
	background: #ffffff;
	box-sizing: border-box;
	/* 描边/浅紫 */
	border: 1px solid #9795f5;
	color: #9795f5;
	width: 55px;
	height: 20px;
	margin-top: 10px;
	margin-left: 5px;
	display: flex;
	justify-content: center;
	align-items: center;
	/* 创建一个内阴影效果，边框颜色为 #3d3d3d */
}

.textR {
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 550;
}

.load-button-all {
	background: #ffffff;
	color: #9795f5;
	box-sizing: border-box;
	border: 1px solid #9795f5;
	margin-top: 5px;
	width: 110px;
	height: 25px;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 5px;
}

.body {
	margin: 0;
	font-family: Arial, Helvetica, sans-serif;
}

.loader {
	border: 5px solid #f3f3f3;
	border-top: 5px solid #3d3d3d;
	border-radius: 50%;
	width: 40px;
	height: 40px;
	animation: spin 1s linear infinite;
	position: absolute;
	top: 50%;
	left: 63%;
	z-index: 2;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.buttonS {
	border: 1px solid transparent;
	border-radius: 2px;
	color: #6463ff;
	/* 设置文字颜色为 #6463FF */
	box-shadow: inset 0 0 0 1px #6463ff;
	width: 100px;
	height: 25px;
	/* background-color: transparent; */
}

.bgc {
	background-color: #e8def7;
}

.boder {
	/* position: absolute; */
	height: 30px;
	width: 150px;
	border: 1px solid var(--el-border-color);
	border-radius: 5px;
	/* margin-top: 20px;
  margin-right: 10px; */
}

.side-style {
	display: flex;
	flex-direction: column;
	line-height: 10px;
}

.testl {
	margin-left: 10px;
}

.flex2 {
	display: flex;
	align-items: center;
	margin-top: 10px;
}

.result-values {
	display: flex;
	flex-direction: column;
	position: absolute;
	top: 70px;
	right: 10px;
	background-color: #ffffff;
	width: 240px;
	/* height: 40px; */
	/* justify-content: space-between; */
	padding: 4px 10px;
	/* align-items: center; */
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	/* 添加阴影效果 */
	border-left: 2px solid #6463ff;
}

.text-resultbox {
	display: flex;
	flex-direction: column;
	margin-left: 5px;
}
.resultText {
	/* position: absolute; */
	/* transition: opacity 0.2s ease, visibility 0.2s ease; */
}

.resultText.hidden {
	opacity: 0;
	visibility: hidden;
}

.resultText.visible {
	opacity: 1;
	visibility: visible;
}
.moudle-region {
	background: linear-gradient(270deg, #ffffff 0%, #f5f7ff 100%);
	width: 300px;
	height: 100vh;
	box-sizing: border-box;
	border-width: 0px 2px 0px 0px;
	border-style: solid;
	position: absolute;
	border-color: #ffffff;
	margin-left: 250px;
}

.m-region {
	/* max-width: 300px; */
	/* width: 1500px; */
	height: 964px;
	padding: 20px 25px;
	border-left: 1px solid #ccc;
	overflow: auto;
	max-height: 940px;
}

.regin-box {
	width: 250px;
	height: 300px;
	margin-top: 10px;
	display: inline-block;
	margin: 10px;
}

.square {
	width: 250px;
	height: 250px;
	border-radius: 10px;
	background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
		linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
	background-size: 20px 20px, 20px 20px, 20px 20px, 20px 20px;
	background-position: 0px 0px, 0px 10px, 10px 0px, 10px 10px;
}

.img-style {
	position: relative;
	margin-left: -22px;
}

.delete-button {
	width: 150px;
	margin-left: -5px;
	margin-top: 8px;
}

.row {
	display: flex;
}

.mould-names {
	display: flex;
	flex-wrap: wrap;
}

.mould-name {
	margin-right: 40px;
	margin-bottom: 10px;
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: 600;
	cursor: pointer;
}
.switchTo {
	background: transparent;
	border: none;
	margin-left: 10px;
	padding: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #7472f1;
}
.switchTo:hover {
	background: rgba(100, 99, 255, 0.2);
	border-radius: 5px;
}
.switchStyle {
	position: absolute;
	display: flex;
	margin-top: -40px;
	width: 100vw;
	max-width: 1800px;
	justify-content: center;
}
.button-icon {
	margin-right: 5px; /* 调整图像与文本之间的间距 */
}
.active {
	color: #6463ff;
}
.file-label {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 120px;
	display: inline-block;
	cursor: pointer;
}
.text1 {
	color: #6463ff; /* 设置文字颜色 */
	cursor: pointer; /* 设置鼠标悬停时的手指效果 */
	transition: color 0.3s, text-decoration 0.3s; /* 添加过渡效果 */
	/* margin-top: 15px; */
	/* margin-bottom: -20px; */
	margin-left: 5px;
	/* position: absolute; */
}
.text2 {
	margin-top: -25px;
	margin-left: 143px;
	/* margin-bottom: -20px; */
}

.text1:hover {
	color: #4a4aff; /* 鼠标悬停时文字颜色变化 */
	text-decoration: underline; /* 添加下划线 */
}
.custom-dialog .el-dialog__wrapper {
	position: fixed;
	top: 100px; /* Adjust as needed */
	right: 100px; /* Adjust as needed */
	bottom: auto; /* Adjust as needed */
	left: auto; /* Adjust as needed */
	height: 500px;
}
.dialog-content {
	display: flex;
	height: 580px;
	margin-top: -20px;
}
.el-overlay {
	display: none; /* Hide the overlay */
}

.left-section {
	flex: 3;
	padding: 5px;
	border-right: 1px solid #ddd; /* Optional for visual separation */
	/* background: #000; */
}

.right-section {
	flex: 7;
	padding: 5px;
	width: 100%;
	/* background: #cb5151; */
}

.dialog-footer {
	/* padding: 10px; */
	text-align: right;
	/* margin-top: -40px; */
}
.search-style {
	margin-right: 700px;
}
.reset-style {
	left: 150px;
	position: absolute;
}
.select-style {
	width: 200px;

	/* margin-top: 20px; */
}
.item {
	margin-bottom: 10px;
}

.label {
	display: block;
	margin-bottom: 5px;
}
.container {
	display: flex;
	align-items: center;
	gap: 20px;
	margin-top: 20px;
}

.label-column {
	flex: 3.5;
	margin-top: 10px;
}
.select-column {
	flex: 6.5;
}

.label-item,
.select-item {
	display: flex;
	align-items: center;
}
.label-item {
	display: flex;
	align-items: center;
	margin-bottom: 30px;
	/* padding: 2px 0; */
}
.el-select .el-select__tags {
	overflow: hidden;
	text-overflow: ellipsis;
}
.select-item {
	margin-bottom: 20px;
}
.right-section {
	padding: 16px;
}

.image-gallery {
	display: flex;
	flex-direction: column;
}

.image-row {
	display: flex;
	margin-bottom: 8px;
}

/* .gallery-image:last-child {
	margin-right: 0;
} */
.el-pagination {
	/* margin-top: 16px; */
	display: flex;
	justify-content: center;
}
.gallery-grid {
	display: flex;
	flex-direction: column; /* 垂直排列每行 */
	gap: 10px;
}
.gallery-row {
	display: flex; /* 横向排列图片 */
	gap: 10px; /* 图片之间的间距 */
}
.gallery-image {
	width: 200px;
	height: 130px;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
}
.gallery-image.selected {
	border: 2px solid #007bff; /* 选中的边框颜色 */
}
.pagina-tyle {
	position: absolute;
	top: 530px;
	right: 0;
}
.top-degin {
	display: flex;
	justify-content: center;
	width: 1000px;
	position: absolute;
	top: 30px;
}
.el-button.active {
	background-color: #6463ff; /* 按钮的背景颜色 */
	color: white; /* 按钮文字颜色 */
}
.center {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 200px;
	margin-bottom: 200px;
}
.image-container {
	position: relative;
	display: inline-block;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
	width: 200px;
	height: 130px;
	margin: 10px 0; /* 10px 的上下间距 */
}

.gallery-image {
	width: 200px;
	height: 130px;
	object-fit: fill;
	cursor: pointer;
	border: 2px solid transparent;
	transition: border 0.3s ease;
}

.delete-buttonF {
	position: absolute;
	top: -7px; /* 调整按钮的位置 */
	right: 1px;
	/* background: red;
	color: white;
	border: none;
	border-radius: 100%;
	padding: 5px;
	cursor: pointer;
	width: 20px;
	height: 20px;
	display: flex;
	justify-content: center;
	align-items: center; */
}
.update-buttonF {
	position: absolute;
	top: -7px; /* 调整按钮的位置 */
	right: 17px;
	/* width: 10px;
	height: 10px; */
}

/* .delete-buttonF:hover {
	background: darkred;
} */
.add-style {
	left: 350px;
	position: absolute;
}
.desc-style {
	display: flex;
	justify-content: center;
	align-content: center;
	margin-top: 100px;
}
.filename-style {
	/* width: 200px; */
	display: inline-block;
	/* justify-content: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis; */
}
.file-name {
	width: 200px;
	display: flex;
	justify-content: center;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.demo-progress {
	position: absolute;
	top: 50%;
	left: 63%;
	font-size: 28px;
}
.el-collapse {
	--el-collapse-header-height: 30px;
}
.header-icon {
	margin-bottom: 5px;
}

.file-label {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 130px;
	display: inline-block;
}

/* 鞋楦解析相关样式 */
/* 调整loading spinner的颜色 */
.analysis-button :deep(.el-loading-spinner) {
	color: #ffffff;
}

/* 调整icon的垂直对齐 */
.analysis-button .el-icon {
	vertical-align: middle;
	margin-top: -2px;
}

/* 从FootCompute.vue复制过来的鞋楦解析样式 */

.analysis-container {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-top: 15px;
	width: 100%;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 4px;
	font-size: 14px;
	margin-left: 10px;
}

.success-icon {
	color: #67c23a;
	margin-right: 5px;
}

.error-icon {
	color: #f56c6c;
	margin-right: 5px;
}

.success-text {
	color: #67c23a;
}

.error-text {
	color: #f56c6c;
}

.is-loading {
	animation: rotating 2s linear infinite;
	color: #409eff;
	font-size: 16px;
}

@keyframes rotating {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.analysis-group {
	display: flex;
	align-items: center;
	gap: 12px;
	width: 100%;
	position: relative;
}

.analysis-content {
	display: flex;
	flex-direction: column;
}

.button-status-group {
	display: flex;
	margin-top: -10px;
}

.analysis-button {
	margin-right: 10px;
}

.squareAna-info {
	width: 300px;
	height: 460px;
	overflow: auto;
	margin-left: -30px;
	margin-top: 10px;
	background: #f9f9ff;
	border-radius: 5px;
	padding: 8px;
}

.analysis-result {
	margin-left: -10px;
	margin-top: -15px;
	padding: 8px;
	background: #f8f9fa;
	border-radius: 4px;
	width: 100%; /* 改为100%宽度，使其自适应父容器 */
	min-width: 310px; /* 设置最小宽度 */
	max-width: 100%; /* 确保不会超出父容器 */
	overflow-x: auto; /* 添加水平滚动条 */
}

.result-table {
	display: flex;
	flex-direction: column;
	gap: 4px;
	min-width: 280px; /* 设置最小宽度确保内容不会挤压 */
}

.table-row {
	display: flex;
	align-items: center;
	padding: 5px 0;
	width: 100%;
}

.table-cell {
	padding: 1px 2px;
	margin-bottom: 5px;
	font-size: 12px;
	white-space: nowrap; /* 防止文字换行 */
	margin-left: 15px;
}

.table-cell.label {
	min-width: 70px; /* 改为最小宽度 */
	flex-shrink: 0; /* 防止标签被压缩 */
}

.size-table {
	margin-top: 8px;
	width: 100%;
	position: relative; /* 添加相对定位 */
	overflow: hidden !important;
}

.size-table :deep(.el-table) {
	font-size: 11px;
	width: 100% !important;
	overflow: hidden !important; /* 隐藏所有滚动条 */
}

.size-table :deep(.el-table__body-wrapper) {
	overflow: hidden !important; /* 隐藏所有滚动条 */
	max-height: 300px;
	min-height: 100px; /* 添加最小高度 */
}

/* 自定义滚动条样式 */
.size-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
	width: 0 !important;
	height: 0 !important;
	display: none !important;
}

.size-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
	background: transparent !important;
	width: 0 !important;
	height: 0 !important;
}

.size-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
	background: transparent !important;
	width: 0 !important;
	height: 0 !important;
}

/* 隐藏Element Plus表格的滚动条 */
.size-table :deep(.el-scrollbar__bar) {
	display: none !important;
}

.size-table :deep(.el-scrollbar__wrap) {
	overflow: hidden !important;
	scrollbar-width: none !important; /* Firefox */
	-ms-overflow-style: none !important; /* IE and Edge */
}

/* 确保表格列不会被挤压 */
.size-table :deep(.el-table__header) {
	table-layout: fixed;
}

.size-table :deep(.el-table__body) {
	table-layout: fixed;
}

/* 确保表格单元格内容不会被截断 */
.size-table :deep(.el-table .cell) {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	line-height: 1.2; /* 调整行高使文字更紧凑 */
}

/* 调整表格头部样式 */
.size-table :deep(.el-table__header-wrapper) {
	overflow: visible !important; /* 确保表头提示不会被截断 */
}

.size-table .el-table th {
	background-color: #f5f7fa !important;
	color: #606266 !important;
	font-weight: bold;
}

.size-table .el-table__row:hover > td {
	background-color: #f0f2ff !important;
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
	.analysis-result {
		margin-left: 0;
		width: 100%;
	}

	.size-table :deep(.el-table) {
		font-size: 10px; /* 在小屏幕上进一步减小字体 */
	}
}

@media screen and (max-width: 768px) {
	.table-cell {
		font-size: 11px;
	}

	.size-table :deep(.el-table) {
		font-size: 9px;
	}
}

.section-title {
	font-size: 13px;
	font-weight: 600;
	color: #6463ff;
	margin: 4px 0 8px 0;
	padding-left: 8px;
	border-left: 3px solid #6463ff;
}

.empty-data {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 150px;
	background-color: white;
	border-radius: 5px;
	margin-top: 10px;
}

.empty-data .el-empty__description {
	color: #909399;
}

/* 保持这个rotate动画用于兼容现有代码 */
@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

/* 全局隐藏滚动条 */
::-webkit-scrollbar {
	display: none !important;
	width: 0 !important;
	height: 0 !important;
}

/* 新的滑动控件样式 */
.slider-container {
	margin-bottom: 15px;
}

.slider-label {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}

.slider-label p {
	margin: 0;
	font-size: 14px;
	color: #606266;
}

/* 自定义el-slider样式 */
.slider-container :deep(.el-slider) {
	margin-top: 8px;
}

.slider-container :deep(.el-slider__runway) {
	background-color: #e4e7ed;
}

.slider-container :deep(.el-slider__bar) {
	background-color: #409eff;
}

.slider-container :deep(.el-slider__button) {
	border-color: #409eff;
}

.slider-container :deep(.el-slider__button:hover) {
	transform: scale(1.2);
}

/* 自定义el-input-number样式 */
.slider-container :deep(.el-input-number) {
	width: 120px;
}

.slider-container :deep(.el-input-number .el-input__inner) {
	text-align: center;
}

::-webkit-scrollbar-thumb {
	background: transparent !important;
	width: 0 !important;
	height: 0 !important;
}

::-webkit-scrollbar-track {
	background: transparent !important;
	width: 0 !important;
	height: 0 !important;
}

.size-table :deep(*) {
	scrollbar-width: none !important;
	-ms-overflow-style: none !important;
}
.dialog-main-content {
	display: flex;
	justify-content: center;
}
.right-preview {
	margin-left: 20px;
	width: 220px;
}
.center-section {
	width: 620px;
}
.design-style {
	padding: 0 0 15px 0;
}
</style>
