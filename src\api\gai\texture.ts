import request from '/@/utils/request';

// ---------------- 纹理分组管理 ----------------

/**
 * 获取纹理分组列表
 * @param params 
 * @returns 
 */
export function getTextureGroups(params: any) {
  return request({
    url: '/gai/texture/groups',
    method: 'get',
    params,
  });
}

/**
 * 新增纹理分组
 * @param data 
 * @returns 
 */
export function addTextureGroup(data: any) {
  return request({
    url: '/gai/texture/groups',
    method: 'post',
    data,
  });
}

/**
 * 修改纹理分组
 * @param id 
 * @param data 
 * @returns 
 */
export function updateTextureGroup(id: string, data: any) {
  return request({
    url: `/gai/texture/groups/${id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除纹理分组
 * @param id 
 * @returns 
 */
export function deleteTextureGroup(id: string) {
  return request({
    url: `/gai/texture/groups/${id}`,
    method: 'delete',
  });
}

// ---------------- 纹理图片管理 ----------------

/**
 * 搜索纹理图片
 * @param page
 * @param params 
 * @returns 
 */
export function searchTexture(page: any, params: any) {
  return request({
    url: '/gai/texture/searchTextures',
    method: 'get',
    params: { ...page, ...params },
  });
}

/**
 * 新增纹理图片
 * @param data 
 * @returns 
 */
export function addTexture(data: any) {
  return request({
    url: '/gai/texture/addTexture',
    method: 'post',
    data,
  });
}

/**
 * 修改纹理信息
 * @param id 
 * @param data 
 * @returns 
 */
export function updateTexture(id: string, data: any) {
  return request({
    url: `/gai/texture/updateTexture/${id}`,
    method: 'put',
    data,
  });
}

/**
 * 删除纹理图片
 * @param ids 
 * @returns 
 */
export function deleteTexture(ids: string[]) {
  return request({
    url: `/gai/texture/deleteTexture/${ids.join(',')}`,
    method: 'delete',
  });
}
/**
 * 获取单个纹理信息
 * @param id 
 * @returns 
 */
export function getTextureById(id: string) {
  return request({
    url: `/gai/texture/${id}`,
    method: 'get',
  });
}


