<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" :title="dataForm.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible">
			<el-form :model="dataForm" :rules="dataRules" label-width="90px" ref="dataFormRef" v-loading="loading">
				<el-row :gutter="20">
					<el-col :span="12" class="mb20">
						<el-form-item label="文件名称" prop="fileName">
							<el-input v-model="dataForm.fileName" placeholder="请输入文件名称" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="类别" prop="category">
							<el-select v-model="dataForm.category" placeholder="请选择类别">
								<el-option v-for="item in DropDownData.category" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="品牌" prop="brand">
							<el-input v-model="dataForm.brand" placeholder="请输入品牌" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="文件" prop="file">
							<el-upload
								class="upload-demo"
								:action="uploadUrl"
								:show-file-list="false"
								:auto-upload="false"
								:on-change="(file) => handleSuccess(file)"
							>
								<el-button type="primary">上传文件</el-button>
							</el-upload>
							<div v-if="fileName" class="file-name">文件名称: {{ fileName }}</div>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="图片" prop="image">
							<el-upload
								ref="uploadRef"
								:auto-upload="false"
								class="avatar-uploader"
								:show-file-list="false"
								:on-change="(file) => onSelectFile(imageFile, file)"
								:action="imageFile.image"
							>
								<img v-if="imageFile.imgFile" :src="imageFile.imgFile" class="avatar" />
								<img v-else-if="imageFile.image" :src="imageFile.image" class="avatar" />
								<el-icon v-else class="avatar-uploader-icon">
									<Plus />
								</el-icon>
							</el-upload>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
					<el-button @click="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="insolesDialog" setup>
import { updateByIdInsoles, getInsolesById, saveInsoles, uploadObj, getDataType } from '/@/api/gai/foot';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { ElMessage } from 'element-plus';
import { userList } from '/@/api/admin/user';
const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const fileName = ref();
const uploadUrl = ref();
const fileUrl = ref();

const imageFile = ref({
	image: '',
	imgFile: '',
});

const dataForm = ref({
	id: '',
	userId: '',
	category: '',
	brand: '',
	file: '',
	image: '',
	fileName: '',
});

const dataRules = ref({
	category: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
	shoeType: [{ required: true, message: '品类不能为空', trigger: 'blur' }],
	fileName: [
		{ required: true, message: '文件名称不能为空', trigger: 'blur' },
		{ max: 10, message: '文件名称不能超过15个字符', trigger: 'change' },
	],
});

// 打开弹窗
const userIds = ref();
const typeData = ref();
const DropDownData = ref({
	category: [],
	shoeType: '',
	sportsShoesType: [],
	brand: [],
	headShape: [],
	shoeSize: [],
	shoeShape: [],
	style: [],
});

const openDialog = async (id: string, userId?: number) => {
	visible.value = true;
	loading.value = true;

	// 重置表单数据
	dataForm.value = {
		id: '',
		userId: '',
		category: '',
		brand: '',
		file: '',
		image: '',
		fileName: '',
	};

	// 重置文件相关状态
	fileName.value = '';
	fileUrl.value = '';
	imageFile.value = {
		image: '',
		imgFile: '',
	};

	userIds.value = userId;

	try {
		// 获取下拉选项数据 - 鞋垫类型为2
		const result = await getDataType('2');
		typeData.value = result.data;

		// 处理下拉数据
		DropDownData.value = {
			category: [],
			brand: [],
		};

		typeData.value.forEach((item) => {
			if (item.ename && DropDownData.value[item.ename]) {
				DropDownData.value[item.ename] = item.children || [];
			}
		});

		// 如果是编辑模式，获取数据
		if (id) {
			const res = await getInsolesById(id);
			if (res.ok && res.data) {
				// 设置图片和文件数据
				imageFile.value.image = res.data.image;
				fileName.value = res.data.file ? res.data.file.split('/').pop() : res.data.fileName;
				fileUrl.value = res.data.file;

				// 设置表单数据
				dataForm.value = { ...res.data };
				dataForm.value.file = fileUrl.value;
				dataForm.value.image = imageFile.value.image;
			}
		}
	} catch (error) {
		ElMessage.error('加载数据失败');
	} finally {
		loading.value = false;
	}
};

// 图片选择处理
const onSelectFile = (obj: any, file: any) => {
	// 检查文件类型
	const fileType = file.raw.type;
	const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml'];

	if (!validImageTypes.includes(fileType)) {
		ElMessage({
			message: '请上传正确的图片格式！',
			type: 'warning',
		});
		return;
	}

	// 基于 FileReader 读取图片做预览
	const reader = new FileReader();
	reader.onload = (e) => {
		obj.imgFile = e?.target?.result;

		const formData = new FormData();
		formData.append('mouldFile', file.raw);

		uploadObj(formData)
			.then((response) => {
				obj.image = response.data;
			})
			.catch((error) => {
				ElMessage({
					message: '上传失败，请重试！',
					type: 'info',
				});
				return;
			});
	};

	reader.readAsDataURL(file.raw);
};

// 文件上传处理
const handleSuccess = async (file: any) => {
	// 检查文件类型
	const validFileExtensions = ['obj', 'stl', '3dm', 'stp', 'step', 'mtl'];
	const fileExtension = file.name.split('.').pop().toLowerCase();

	if (!validFileExtensions.includes(fileExtension)) {
		ElMessage({
			message: '请上传正确的文件格式！例如：.obj, .stl, .3dm, .stp, .step, .mtl',
			type: 'warning',
		});
		return;
	}

	// 创建 FormData 并上传文件
	const formData = new FormData();
	formData.append('mouldFile', file.raw);

	try {
		const result = await uploadObj(formData);
		if (result.data) {
			fileUrl.value = result.data;
			fileName.value = file.name;
			dataForm.value.file = result.data;
			ElMessage.success('文件上传成功');
		}
	} catch (error) {
		ElMessage({
			message: '上传失败，请重试！',
			type: 'info',
		});
	}
};

// 提交表单
const onSubmit = () => {
	dataFormRef.value.validate(async (valid: boolean) => {
		if (!valid) return;

		// 文件和图片验证
		if (!fileUrl.value) {
			ElMessage.error('请上传必要的文件！');
			return;
		}
		if (!imageFile.value.image) {
			ElMessage.error('请上传必要的图片');
			return;
		}

		loading.value = true;

		try {
			// 设置文件和图片URL
			dataForm.value.file = fileUrl.value;
			dataForm.value.image = imageFile.value.image;
			dataForm.value.userId = userIds.value;

			let result;
			if (dataForm.value.id) {
				// 编辑
				result = await updateByIdInsoles(dataForm.value);
			} else {
				// 新增
				result = await saveInsoles(dataForm.value);
			}

			if (result.ok || result) {
				ElMessage.success(dataForm.value.id ? '修改成功' : '新增成功');
				visible.value = false;
				emit('refresh');
			} else {
				ElMessage.error('操作失败');
			}
		} catch (error: any) {
			ElMessage.error(error.msg || '操作失败');
		} finally {
			loading.value = false;
		}
	});
};

// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.avatar-uploader {
	:deep(.el-upload) {
		border: 1px dashed var(--el-border-color);
		border-radius: 6px;
		cursor: pointer;
		position: relative;
		overflow: hidden;
		transition: var(--el-transition-duration-fast);
	}

	:deep(.el-upload:hover) {
		border-color: var(--el-color-primary);
	}
}

.avatar-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 178px;
	height: 178px;
	text-align: center;
}

.avatar {
	width: 178px;
	height: 178px;
	display: block;
}

.file-name {
	margin-top: 8px;
	font-size: 12px;
	color: #666;
}
</style>
