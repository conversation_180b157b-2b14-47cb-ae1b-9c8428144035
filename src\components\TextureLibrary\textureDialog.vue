<template>
	<el-dialog v-model="visible" :title="isEdit ? '编辑纹理' : '新增纹理'" width="500px" :close-on-click-modal="false">
		<el-form :model="form" ref="formRef" :rules="rules" label-width="80px">
			<el-form-item label="纹理名称" prop="textureName">
				<el-input v-model="form.textureName" placeholder="请输入纹理名称" />
			</el-form-item>
			<el-form-item label="纹理分组" prop="groupId">
				<el-tree-select
					v-model="form.groupId"
					:data="props.groups"
					node-key="id"
					:props="{ value: 'id', children: 'children', label: 'groupName' }"
					check-strictly
					:render-after-expand="false"
					placeholder="请选择纹理分组"
					style="width: 100%"
				/>
			</el-form-item>
			<el-form-item label="上传图片" prop="imageUrl">
				<el-upload class="texture-uploader" :show-file-list="false" :before-upload="beforeTextureUpload" :http-request="uploadTexture">
					<img v-if="form.imageUrl" :src="form.imageUrl" class="texture" />
					<el-icon v-else class="texture-uploader-icon"><Plus /></el-icon>
				</el-upload>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="handleSubmit">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, computed } from 'vue';

const props = defineProps({
	groups: {
		type: Array,
		default: () => [],
	},
});
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { addTexture, updateTexture, getTextureById } from '/@/api/gai/texture';
import { uploadStl } from '/@/api/gai/foot';

const visible = ref(false);
const isEdit = ref(false);
const formRef = ref();

const form = reactive({
	id: '',
	textureName: '',
	groupId: '',
	imageUrl: '',
	userId: 1,
});

const rules = {
	textureName: [{ required: true, message: '请输入纹理名称', trigger: 'blur' }],
	imageUrl: [{ required: true, message: '请上传纹理图片', trigger: 'change' }],
};

const emit = defineEmits(['refresh']);

const openDialog = async (id: string, userId: number) => {
	visible.value = true;
	isEdit.value = !!id;
	form.userId = userId;

	if (isEdit.value) {
		const res: any = await getTextureById(id);
		if (res.ok) {
			Object.assign(form, res.data);
		}
	} else {
		Object.assign(form, { id: '', textureName: '', groupId: '', imageUrl: '' });
	}
};

const beforeTextureUpload = (file: any) => {
	const isImage = file.type.startsWith('image/');
	if (!isImage) {
		ElMessage.error('只能上传图片文件!');
	}
	const isLt2M = file.size / 1024 / 1024 < 2;
	if (!isLt2M) {
		ElMessage.error('上传图片大小不能超过 2MB!');
	}
	return isImage && isLt2M;
};

const uploadTexture = async (options: any) => {
	const formData = new FormData();
	formData.append('mouldFile', options.file);
	const res: any = await uploadStl(formData);
	if (res.ok) {
		form.imageUrl = res.data;
	}
};

const handleSubmit = () => {
	formRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const findGroup = (groups: any[], groupId: string): any => {
				for (const group of groups) {
					if (group.id === groupId) return group;
					if (group.children) {
						const found = findGroup(group.children, groupId);
						if (found) return found;
					}
				}
				return null;
			};

			const selectedGroup = findGroup(props.groups, form.groupId);
			const submissionForm = {
				...form,
				groupName: selectedGroup ? selectedGroup.groupName : '',
			};

			const res: any = isEdit.value ? await updateTexture(submissionForm.id, submissionForm) : await addTexture(submissionForm);
			if (res.ok) {
				ElMessage.success(isEdit.value ? '修改成功' : '新增成功');
				visible.value = false;
				emit('refresh');
			}
		}
	});
};

defineExpose({ openDialog });
</script>

<style scoped>
.texture-uploader .texture {
	width: 178px;
	height: 178px;
	display: block;
}
.texture-uploader-icon {
	font-size: 28px;
	color: #8c939d;
	width: 178px;
	height: 178px;
	text-align: center;
	border: 1px dashed #d9d9d9;
}
</style>
