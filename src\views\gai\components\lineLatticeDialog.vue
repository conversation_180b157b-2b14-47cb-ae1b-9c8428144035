<template>
	<div class="line-lattice-dialog-container">
		<el-dialog :close-on-click-modal="false" title="线晶格库选择" draggable v-model="visible" width="40%" @close="handleClose">
			<div class="dialog-content" v-loading="loading">
				<!-- 搜索区域 -->
				<div class="search-section">
					<el-row :gutter="20" align="middle">
						<el-col :span="6">
							<el-input v-model="searchParams.fileName" placeholder="请输入文件名称" clearable @clear="handleSearch">
								<template #prefix>
									<el-icon><Search /></el-icon>
								</template>
							</el-input>
						</el-col>
						<el-col :span="4">
							<el-button type="primary" @click="handleSearch" :loading="loading">
								<el-icon><Search /></el-icon>
								搜索
							</el-button>
						</el-col>
					</el-row>
				</div>

				<!-- 数据表格 -->
				<div class="table-section">
					<el-table :data="tableData" style="width: 100%" height="400px" @current-change="handleCurrentRowChange" highlight-current-row>
						<el-table-column width="55">
							<template #default="scope">
								<el-radio v-model="selectedRowId" :label="scope.row.id" @change="handleRadioChange(scope.row)">
									<span></span>
								</el-radio>
							</template>
						</el-table-column>
						<el-table-column prop="fileName" label="文件名称" />
						<el-table-column label="图片预览" width="150">
							<template #default="scope">
								<div class="image-preview" v-if="scope.row.image">
									<img
										:src="scope.row.image"
										@click="openImagePreview(scope.row.image)"
										style="width: 80px; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer; border: 1px solid #dcdfe6"
										@error="handleImageError"
									/>
								</div>
								<div v-else class="no-image">
									<el-icon style="font-size: 24px; color: #ccc"><Picture /></el-icon>
									<span style="font-size: 12px; color: #999">暂无图片</span>
								</div>
							</template>
						</el-table-column>
					</el-table>
				</div>

				<!-- 分页组件 -->
				<div class="pagination-section">
					<el-pagination
						v-model:current-page="pagination.page"
						v-model:page-size="pagination.limit"
						:page-sizes="[10, 20, 50, 100]"
						:total="pagination.total"
						layout="total, sizes, prev, pager, next, jumper"
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
					/>
				</div>
			</div>

			<!-- 底部按钮 -->
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="handleCancel">取消</el-button>
					<el-button type="primary" @click="handleConfirm" :disabled="!selectedRow"> 确定选择 </el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 自定义图片预览弹窗 -->
		<el-dialog v-model="imagePreviewVisible" title="图片预览" width="60%" :close-on-click-modal="true" :show-close="true">
			<div class="image-preview-container">
				<img :src="previewImageUrl" style="width: 100%; height: auto; max-height: 70vh; object-fit: contain" @error="handleImageError" />
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Picture } from '@element-plus/icons-vue';
import { getLineLatticeList } from '../../../api/gai/lineLattice';

// 类型定义
interface LineLatticeItem {
	id: string;
	userId: string;
	fileName: string;
	file: string;
	image: string;
	delFlag: number;
	createTime: string;
	updateTime: string;
}

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const tableData = ref<LineLatticeItem[]>([]);
const selectedRow = ref<LineLatticeItem | null>(null);
const selectedRowId = ref<string>('');

// 图片预览相关
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

// 搜索参数
const searchParams = reactive({
	fileName: '',
	userId: undefined as number | undefined,
});

// 分页参数
const pagination = reactive({
	page: 1,
	limit: 10,
	total: 0,
});

// 组件方法
const emit = defineEmits(['confirm', 'cancel']);

// 打开弹窗
const openDialog = (userId?: number, preSelectedFile?: string) => {
	visible.value = true;
	searchParams.userId = userId;
	resetData();
	fetchData(preSelectedFile);
};

// 重置数据
const resetData = () => {
	tableData.value = [];
	selectedRow.value = null;
	selectedRowId.value = '';
	searchParams.fileName = '';
	pagination.page = 1;
	pagination.total = 0;
};

// 获取数据
const fetchData = async (preSelectedFile?: string) => {
	loading.value = true;
	try {
		const params = {
			page: pagination.page,
			limit: pagination.limit,
			fileName: searchParams.fileName || undefined,
		};

		const result = await getLineLatticeList(params);

		if (result.ok && result.data) {
			// 过滤已删除的数据 (delFlag = 1 表示已删除)
			tableData.value = result.data.records.filter((item: LineLatticeItem) => item.delFlag == '0');
			pagination.total = result.data.total;

			// 如果有预选择的文件，尝试匹配并选中
			if (preSelectedFile) {
				// 路径标准化函数，统一路径格式进行比较
				const normalizePath = (path: string): string => {
					if (!path) return '';
					return path.replace(/\\/g, '/');
				};

				const normalizedPreSelected = normalizePath(preSelectedFile);
				const matchedItem = tableData.value.find((item) => normalizePath(item.file) === normalizedPreSelected);
				if (matchedItem) {
					selectedRow.value = matchedItem;
					selectedRowId.value = matchedItem.id;
				}
			}
		} else {
			ElMessage.error('获取线晶格库数据失败');
		}
	} catch (error) {
		ElMessage.error('获取线晶格库数据失败');
	} finally {
		loading.value = false;
	}
};

// 搜索处理
const handleSearch = () => {
	pagination.page = 1; // 重置到第一页
	fetchData();
};

// 行选择变化处理
const handleCurrentRowChange = (currentRow: LineLatticeItem | null) => {
	selectedRow.value = currentRow;
	if (currentRow) {
		selectedRowId.value = currentRow.id;
	}
};

// 单选框变化处理
const handleRadioChange = (row: LineLatticeItem) => {
	selectedRow.value = row;
};

// 分页大小变化
const handleSizeChange = (size: number) => {
	pagination.limit = size;
	pagination.page = 1; // 重置到第一页
	fetchData();
};

// 当前页变化
const handleCurrentChange = (page: number) => {
	pagination.page = page;
	fetchData();
};

// 打开图片预览
const openImagePreview = (imageUrl: string) => {
	previewImageUrl.value = imageUrl;
	imagePreviewVisible.value = true;
};

// 处理图片加载错误
const handleImageError = (event: Event) => {
	const img = event.target as HTMLImageElement;
	img.style.display = 'none';
	// 静默处理图片加载错误，不显示用户提示
};

// 确认选择
const handleConfirm = () => {
	if (!selectedRow.value) {
		ElMessage.warning('请选择一项');
		return;
	}
	emit('confirm', selectedRow.value);
	handleClose();
};

// 取消
const handleCancel = () => {
	emit('cancel');
	handleClose();
};

// 关闭弹窗
const handleClose = () => {
	visible.value = false;
	resetData();
};

// 暴露方法给父组件
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.line-lattice-dialog-container {
	.dialog-content {
		.search-section {
			margin-bottom: 20px;
			padding: 16px;
			background-color: #f8f9fa;
			border-radius: 6px;
		}

		.table-section {
			margin-bottom: 20px;
		}

		.pagination-section {
			display: flex;
			justify-content: center;
			margin-top: 20px;
		}
	}

	.dialog-footer {
		display: flex;
		justify-content: flex-end;
		gap: 12px;
	}

	.image-preview {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 8px;
	}

	.no-image {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 80px;
		color: #999;
		background-color: #f5f5f5;
		border-radius: 4px;
		border: 1px solid #dcdfe6;
	}

	.image-preview-container {
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 8px;
		padding: 20px;
	}
}

:deep(.el-dialog) {
	.el-dialog__header {
		padding: 20px 20px 10px;
		border-bottom: 1px solid #ebeef5;
	}

	.el-dialog__body {
		padding: 20px;
	}

	.el-dialog__footer {
		padding: 10px 20px 20px;
		border-top: 1px solid #ebeef5;
	}
}

:deep(.el-table) {
	.el-table__header {
		background-color: #f5f7fa;
	}
}

:deep(.el-image) {
	border-radius: 4px;
	border: 1px solid #dcdfe6;
}
</style>
