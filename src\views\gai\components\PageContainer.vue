<template>
	<el-card class="page-container" :shadow="shadowType">
		<template #header>
			<div class="header">
				<span>{{ title }}</span>
				<div class="extra">
					<slot name="button"></slot>
				</div>
			</div>
		</template>
		<slot></slot>
	</el-card>
</template>

<script setup>
defineProps({
	title: {
		required: true,
		type: String,
	},
});
const shadowType = 'never';
</script>

<style lang="scss" scoped>
.page-container {
	// width: 90vw;
	// height: 20vw;
	max-width: 1400px;
	max-height: 70px;
	border: none !important;
	border-radius: 4px;
	display: flex;
	overflow: hidden;

	.el-card__body {
		padding-bottom: 0 !important;
		border-bottom: none !important;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 0;
		padding: 0;
	}
}
</style>
