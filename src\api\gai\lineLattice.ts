import request from '/@/utils/request';

/**
 * 线晶格库API接口
 */

// 线晶格库实体类型
export interface LineLatticeEntity {
  id: string;
  userId: string;
  fileName: string;
  file: string;
  image: string;
  delFlag: number; // 标识符（1是已删除，0是正常）
  createTime: string;
  updateTime: string;
}

// 分页查询参数
export interface LineLatticePageParams {
  page: number;
  limit: number;
  fileName?: string;
  userId?: number;
}

// 分页结果
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 分页查询线晶格库数据
 * @param params 查询参数
 * @returns 分页结果
 */
export const getLineLatticeList = (params: LineLatticePageParams) => {
  return request({
    url: '/gai/lineLattice/page',
    method: 'get',
    params
  });
};

/**
 * 根据ID获取线晶格库详情
 * @param id 线晶格库ID
 * @returns 线晶格库详情
 */
export const getLineLatticeById = (id: string) => {
  return request({
    url: `/gai/lineLattice/${id}`,
    method: 'get'
  });
};

/**
 * 新增线晶格库
 * @param data 线晶格库数据
 * @returns 操作结果
 */
export const addLineLattice = (data: Partial<LineLatticeEntity>) => {
  return request({
    url: '/gai/lineLattice',
    method: 'post',
    data
  });
};

/**
 * 更新线晶格库
 * @param data 线晶格库数据
 * @returns 操作结果
 */
export const updateLineLattice = (data: LineLatticeEntity) => {
  return request({
    url: '/gai/lineLattice',
    method: 'put',
    data
  });
};

/**
 * 删除线晶格库
 * @param id 线晶格库ID
 * @returns 操作结果
 */
export const deleteLineLattice = (id: string) => {
  return request({
    url: `/gai/lineLattice/${id}`,
    method: 'delete'
  });
};

/**
 * 批量删除线晶格库
 * @param ids 线晶格库ID数组
 * @returns 操作结果
 */
export const batchDeleteLineLattice = (ids: string[]) => {
  return request({
    url: '/gai/lineLattice/batch',
    method: 'delete',
    data: ids
  });
};
