// 定义通用内容
export default {
	flow: {
		allUser: 'all user',
		end: 'end',
		initiator: 'initiator',
		approver: 'approver',
		carbonCopyRecipient: 'carbonCopy',
		conditionalBranching: 'conditional',
		parallelBranch: 'parallel',
		basicInformation: 'basic information',
		formDesign: 'form design',
		processDesign: 'process design',
		processCheck: 'process check',
		publish: 'publish',
		submit: 'submit',
		checkSuccess: 'check success',
		checkSubSuccess: 'check success,submit?',
		checkIng: 'check ing',
		checkSubIng: 'check ing',
		logo: 'logo',
		name: 'name',
		remark: 'remark',
		group: 'group',
		admin: 'admin',
		groupTips: 'please select a group',
		emptyComponent: 'please click on the left component and drag it here	',
		createTime: 'createTime',
		scopeOfUse: 'scopeOfUse',
		creationProcess: 'creation process',
		creationGroup: 'creation group',
		componentLibrary: 'ComponentLibrary',
		title: 'title',
		minLength: 'min length',
		maxLength: 'max length',
		regularExpression: 'regularExpression',
		regularExpressionTip: 'regular expression tip',
		inputErrorRegularExpressionTip: 'input regular expression tip',
		defaultTip: 'default',
		enTitle: 'en title',
		required: 'required',
		other: 'other',
		tips: 'tips',
		servicePrefix: 'service prefix',
		servicePrefixTips: 'input tips',
	},
};
