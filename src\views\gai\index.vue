<template>
	<div class="containerGai">
		<div class="header">
			<div class="menu">
				<div class="lefttext">
					<div v-if="isLiningDomain" class="lining-brand">
						<div class="brand-logo">李宁</div>
						<p class="logoText">脚转楦定制化系统</p>
					</div>
					<div v-else class="default-brand">
						<img src="/@/assets/login/LOGO.png" class="logo" />
						<p class="logoText">大家好，这是我的新公司工作台</p>
					</div>
				</div>
				<router-link :to="{ path: '/FootLast' }" class="menu-items" @click="setActive('FootLast')" :class="{ active: activeItem === 'FootLast' }">
					<img v-if="activeItem === 'FootLast'" class="imgl1" src="/@/assets/leftA.png" />
					<img v-else class="imgl" src="/@/assets/left.png" />脚转楦
				</router-link>

				<router-link
					:to="{ path: '/FunsionLast' }"
					class="menu-items"
					@click="setActive('FunsionLast')"
					:class="{ active: activeItem === 'FunsionLast' }"
				>
					<img v-if="activeItem === 'FunsionLast'" class="imgl" src="/@/assets/rightA.png" />
					<img v-else class="imgl" src="/@/assets/right.png" />楦生成
				</router-link>

				<router-link
					:to="{ path: '/InsolesLast' }"
					class="menu-items"
					@click="setActive('InsolesLast')"
					:class="{ active: activeItem === 'InsolesLast' }"
				>
					<img v-if="activeItem === 'InsolesLast'" class="imgInsoles" src="/@/assets/im-footprint-active.png" />
					<img v-else class="imgInsoles" src="/@/assets/im-footprint.png" />鞋垫生成
				</router-link>

				<router-link :to="{ path: '/Stomata' }" class="menu-items" @click="setActive('Stomata')" :class="{ active: activeItem === 'Stomata' }">
					<img v-if="activeItem === 'Stomata'" class="imgInsoles" src="https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/stomata-sl.png" />
					<img v-else class="imgInsoles" src="https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/stomata-un.png" />气孔
				</router-link>

				<router-link :to="{ path: '/Applique' }" class="menu-items" @click="setActive('Applique')" :class="{ active: activeItem === 'Applique' }">
					<img v-if="activeItem === 'Applique'" class="imgInsoles" src="https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/applique-sl.png" />
					<img v-else class="imgInsoles" src="https://gai-clouds.obs.cn-east-3.myhuaweicloud.com/applique-un.png" />贴花
				</router-link>

				<div class="righttext">
					<el-dropdown :show-timeout="70" :hide-timeout="50" @command="onHandleCommandClick">
						<span class="logolayout">
							<img src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" class="avatarl" />
							{{ username }}
						</span>
						<template #dropdown>
							<el-dropdown-menu>
								<!-- <el-dropdown-item command="/FootLast">首页</el-dropdown-item> -->
								<el-dropdown-item command="personal">{{ $t('user.dropdown2') }}</el-dropdown-item>
								<el-dropdown-item divided command="logOut">{{ $t('user.dropdown5') }}</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<personal-drawer ref="personalDrawerRef"></personal-drawer>
				</div>
			</div>
		</div>
		<el-main>
			<div class="contentGai">
				<router-view />
			</div>
		</el-main>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { NextLoading } from '/@/utils/loading';
import { ElMessageBox } from 'element-plus';
import mittBus from '/@/utils/mitt';
import { logout } from '/@/api/login';
import { Session } from '/@/utils/storage';
import { useI18n } from 'vue-i18n';
import { userList } from '/@/api/admin/user';
import { useRouter } from 'vue-router';
import { defineAsyncComponent } from 'vue';
import { onBeforeRouteUpdate } from 'vue-router';
const PersonalDrawer = defineAsyncComponent(() => import('/@/views/admin/system/user/personal.vue'));
const { t } = useI18n();
const personalDrawerRef = ref();
const router = useRouter();

// 检查是否为李宁域名
const isLiningDomain = computed(() => {
	const currentDomain = window.location.host;
	return currentDomain.includes('lining.gaicloud.cn');
});

// 定义一个函数来更新菜单激活状态
const updateActiveItem = (path: string) => {
	if (path === '/gai' || path === '/FootLast' || path === '/FootQuest' || path === '/FootCompute') {
		activeItem.value = 'FootLast';
	} else if (path === '/FunsionLast' || path === '/FunsionQuest' || path === '/FunsionCompute') {
		activeItem.value = 'FunsionLast';
	} else if (path === '/InsolesLast' || path === '/InsolesQuest' || path === '/InsolesCompute') {
		activeItem.value = 'InsolesLast';
	} else if (path === '/Stomata' || path === '/StomataQuest' || path === '/StomataCompute') {
		activeItem.value = 'Stomata';
	} else if (path === '/Applique' || path === '/AppliqueQuest' || path === '/AppliqueCompute') {
		activeItem.value = 'Applique';
	} else {
		activeItem.value = 'FootLast'; // 默认激活脚转楦
	}
};

const onHandleCommandClick = (path: string) => {
	if (path === 'logOut') {
		ElMessageBox({
			closeOnClickModal: false,
			closeOnPressEscape: false,
			title: t('user.logOutTitle'),
			message: t('user.logOutMessage'),
			showCancelButton: true,
			confirmButtonText: t('user.logOutConfirm'),
			cancelButtonText: t('user.logOutCancel'),
			buttonSize: 'default',
			beforeClose: (action, instance, done) => {
				if (action === 'confirm') {
					instance.confirmButtonLoading = true;
					instance.confirmButtonText = t('user.logOutExit');
					setTimeout(() => {
						done();
						setTimeout(() => {
							instance.confirmButtonLoading = false;
						}, 300);
					}, 700);
				} else {
					done();
				}
			},
		})
			.then(async () => {
				// 关闭全部的标签页
				mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 3, ...router }));
				// 调用后台接口
				await logout();
				// 清除缓存/token等
				Session.clear();
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				// window.location.reload();
				router.push('/login');
			})
			.catch(() => {});
	} else if (path === 'personal') {
		// 打开个人页面
		personalDrawerRef.value.open();
	} else {
		router.push(path);
	}
};
// 菜单激活状态
const activeItem = ref();
const avatar = ref();
const username = ref();

onMounted(async () => {
	const currentRoutePath = router.currentRoute.value.path;
	const result = await userList();
	avatar.value = result.data.sysUser.avatar;
	username.value = result.data.sysUser.username;
	NextLoading.done();
	if (currentRoutePath === '/gai' || currentRoutePath === '/FootLast') {
		router.push({ path: '/FootLast' });
	}

	// 判断当前路径并设置对应的激活菜单项
	updateActiveItem(currentRoutePath);
});

// 监听路由变化，更新菜单激活状态
onBeforeRouteUpdate((to) => {
	updateActiveItem(to.path);
});

// 监听当前路由的变化
watch(
	() => router.currentRoute.value.path,
	(newPath) => {
		updateActiveItem(newPath);
	}
);

const setActive = (item: string) => {
	activeItem.value = item;
};
</script>
<style scoped>
.containerGai {
	position: relative;
	width: 100%;
	height: 100vh;
	overflow: hidden;
	background-image: url('/@/assets/login/bg.png');
	background-size: cover;
	background-position: center;
}

.header {
	display: flex;
	align-items: center;
	background-color: #ffffff;
	height: 60px;
	/* width: 2050px; */
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu {
	display: flex;
	align-items: center;
	margin: auto;
}

.menu-items {
	/* margin-right: auto; */
	margin-left: 10px;
	padding: 8px 16px;
	width: 160px;
	height: 36px;
	border-radius: 200px;
	opacity: 1;
	font-family: Alibaba PuHuiTi 3;
	font-size: 16px;
	font-weight: 800;
	line-height: 28px;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	letter-spacing: 0px;
	transition: background-color 0.3s ease;
	/* 添加过渡动画 */
}

.menu-items.active {
	background: linear-gradient(101deg, #d8d8fd 0%, #e0e9ff 97%);
	color: #6463ff;
}

.leftText {
	align-items: center;
	margin-top: 20px;
}

.contentGai {
	position: fixed;
	bottom: -33%;
	/* margin-top: 21%; */
	left: 50%;
	transform: translate(-50%, -50%);
	width: 90vw;
	max-width: 1500px;
	height: 100vh;
	max-height: 840px;
	border-radius: 10px;
	opacity: 1;
	padding: 24px;
	gap: 32px;
	background: #ffffff;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
/* @media screen and (min-width: 1200px) {
	.contentGai {
		margin-top: 400px;
		width: 1600px;
		height: 750px;
	}
}
*/
@media screen and (max-width: 1910px) {
	.contentGai {
		margin-top: 21%;
		width: 90vw;
		max-width: 1500px;
		height: 80vh;
		max-height: 940px;
	}
}
@media screen and (min-width: 1600px) {
	.contentGai {
		margin-top: 21%;
		width: 90vw;
		max-width: 1500px;
		height: 81vh;
		max-height: 940px;
	}
}
.imgl,
.imgl1,
.imgInsoles {
	margin-right: 6px;
	width: 20px;
	height: 20px;
	object-fit: contain;
}

.logo {
	position: static;
	left: 16px;
	top: 0px;
	width: 48px;
	height: 24px;
	opacity: 1;
}

.logoText {
	font-family: Alibaba PuHuiTi 3;
	font-size: 14px;
	font-weight: normal;
	line-height: 22px;
	letter-spacing: 0px;
	margin-left: 10px;
	color: #555555;
}

.lefttext {
	position: absolute;
	/* margin-right: 500px; */
	display: flex;
	left: 16px;
}

.default-brand {
	display: flex;
	align-items: center;
}

.lining-brand {
	display: flex;
	align-items: center;
}

.brand-logo {
	background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ffd23f 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	font-size: 20px;
	font-weight: bold;
	font-family: 'Microsoft YaHei', sans-serif;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.righttext {
	position: absolute;
	display: flex;
	right: 40px;
	align-content: center;
	/* width: 10px;
  height: 10px; */
	/* border-radius: 50px; */
}

.logolayout {
	display: flex;
	font-size: 17px;
	margin-right: 10px;
}

.avatarl {
	border-radius: 40px;
	width: 25px;
	height: 25px;
	margin-top: -4px;
	margin-right: 2px;
}

.loginlogo {
	width: 169px;
	height: 32px;
}
</style>
