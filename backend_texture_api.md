# 纹理库后端接口开发说明

为了支持前端纹理库的管理功能，后端需要提供以下接口。纹理库分为**通用库**和**私有库**，通过 `userId` 进行区分：

- **通用库**: `userId` 固定为 `1`。
- **私有库**: `userId` 为当前登录用户的 ID。

---

## 1. 纹理分组管理

### 1.1. 获取纹理分组列表

- **功能**: 根据 `userId` 获取对应的纹理分组列表。
- **URL**: `GET /api/texture/groups`
- **请求参数**:
  | 参数名 | 类型 | 是否必传 | 说明 |
  | --- | --- | --- | --- |
  | `userId` | `Integer` | 是 | 用户 ID（`1` 表示通用库） |
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "成功",
  	"data": [
  		{
  			"id": "group-uuid-1",
  			"groupName": "金属材质",
  			"userId": 1
  		},
  		{
  			"id": "group-uuid-2",
  			"groupName": "木质材质",
  			"userId": 1
  		}
  	]
  }
  ```

### 1.2. 新增纹理分组

- **功能**: 为指定用户新增一个纹理分组。
- **URL**: `POST /api/texture/groups`
- **请求体**:
  ```json
  {
  	"groupName": "新的分组名称",
  	"userId": 2
  }
  ```
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "新增成功",
  	"data": {
  		"id": "new-group-uuid",
  		"groupName": "新的分组名称",
  		"userId": 2
  	}
  }
  ```

### 1.3. 修改纹理分组

- **功能**: 修改指定 ID 的分组名称。
- **URL**: `PUT /api/texture/groups/{id}`
- **请求体**:
  ```json
  {
  	"groupName": "修改后的分组名称"
  }
  ```
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "修改成功"
  }
  ```

### 1.4. 删除纹理分组

- **功能**: 删除指定 ID 的分组。如果分组下存在纹理图片，应给出提示或提供迁移选项。
- **URL**: `DELETE /api/texture/groups/{id}`
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "删除成功"
  }
  ```

---

## 2. 纹理图片管理

### 2.1. 搜索纹理图片

- **功能**: 分页、分组查询指定用户的纹理图片列表。
- **URL**: `GET /api/textures`
- **请求参数**:
  | 参数名 | 类型 | 是否必传 | 说明 |
  | --- | --- | --- | --- |
  | `userId` | `Integer` | 是 | 用户 ID（`1` 表示通用库） |
  | `group` | `String` | 否 | 分组名称（查询指定分组下的图片） |
  | `page` | `Integer` | 否 | 当前页码，默认为 `1` |
  | `size` | `Integer` | 否 | 每页数量，默认为 `10` |
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "成功",
  	"data": {
  		"total": 1,
  		"records": [
  			{
  				"id": "texture-uuid-1",
  				"textureName": "金属纹理.jpg",
  				"imageUrl": "http://minio.server/path/to/image.jpg",
  				"group": "金属材质",
  				"userId": 1
  			}
  		]
  	}
  }
  ```

### 2.2. 新增纹理图片

- **功能**: 上传新的纹理图片，并关联到指定用户和分组。
- **URL**: `POST /api/textures`
- **请求体** (form-data):
  | 参数名 | 类型 | 是否必传 | 说明 |
  | --- | --- | --- | --- |
  | `textureName` | `String` | 是 | 纹理名称 |
  | `group` | `String` | 是 | 所属分组 |
  | `userId` | `Integer` | 是 | 用户 ID |
  | `file` | `File` | 是 | 图片文件 |
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "新增成功",
  	"data": {
  		"id": "new-texture-uuid",
  		"textureName": "新纹理.jpg",
  		"imageUrl": "http://minio.server/path/to/new-image.jpg",
  		"group": "金属材质",
  		"userId": 2
  	}
  }
  ```

### 2.3. 修改纹理信息

- **功能**: 修改纹理的名称或所属分组。
- **URL**: `PUT /api/textures/{id}`
- **请求体**:
  ```json
  {
  	"textureName": "修改后的名称.jpg",
  	"group": "修改后的分组"
  }
  ```
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "修改成功"
  }
  ```

### 2.4. 删除纹理图片

- **功能**: 删除指定 ID 的纹理图片（建议软删除）。
- **URL**: `DELETE /api/textures/{ids}` (支持批量删除)
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "删除成功"
  }
  ```

---

## 3. 文件上传接口 (复用)

- **功能**: 此接口已存在，用于上传文件并返回 URL。前端将在新增纹理时调用此接口。
- **URL**: `POST /uploadStl`
- **请求体** (form-data):
  | 参数名 | 类型 | 是否必传 | 说明 |
  | --- | --- | --- | --- |
  | `mouldFile` | `File` | 是 | 文件对象 |
- **返回数据示例**:
  ```json
  {
  	"code": 200,
  	"msg": "成功",
  	"data": "http://minio.gaicloud.cn/gai/your-file-name.jpg"
  }
  ```
