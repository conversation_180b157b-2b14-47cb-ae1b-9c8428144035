// 定义通用内容
export default {
	flow: {
		allUser: '所有人',
		end: '结束',
		initiator: '发起人',
		approver: '审批人',
		carbonCopyRecipient: '抄送人',
		conditionalBranching: '条件分支',
		parallelBranch: '并行分支',
		basicInformation: '基础信息',
		formDesign: '表单设计',
		processDesign: '流程设计',
		processCheck: '流程检查',
		publish: '发布',
		submit: '提交',
		remark: '备注',
		group: '分组',
		groupTips: '请选择分组',
		admin: '管理员',
		checkSuccess: '检查成功',
		checkSubSuccess: '流程检查完成，现在提交？',
		checkIng: '检查中',
		checkSubIng: '正在检查流程信息',
		emptyComponent: '请点击左侧组件拖拽到此处',
		logo: '图标',
		name: '名称',
		createTime: '创建时间',
		scopeOfUse: '使用范围',
		creationProcess: '创建流程',
		creationGroup: '创建分组',
		componentLibrary: '组件库',
		title: '标题',
		enTitle: '英文标题',
		required: '必填',
		other: '其他',
		tips: '提示',
		minLength: '最小长度',
		maxLength: '最大长度',
		regularExpression: '正则表达式',
		regularExpressionTip: '请输入正则表达式',
		inputErrorRegularExpressionTip: '请输入正确的正则表达式',
		defaultTip: '默认',
		servicePrefix: '服务前缀',
		servicePrefixTips: '请输入服务前缀',
	},
};
