<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" :title="dataForm.id ? $t('common.editBtn') : $t('common.addBtn')" draggable v-model="visible">
			<el-form :model="dataForm" :rules="dataRules" label-width="90px" ref="dataFormRef" v-loading="loading">
				<el-row :gutter="20">
					<el-col :span="12" class="mb20">
						<el-form-item label="文件名称" prop="fileName">
							<el-input v-model="dataForm.fileName" placeholder="请输入文件名称" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="类别" prop="category">
							<el-select v-model="dataForm.category" placeholder="请选择类别">
								<el-option v-for="item in DropDownData.category" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="品类" prop="shoeType">
							<el-select v-model="dataForm.shoeType" placeholder="请选择品类">
								<el-option v-for="item in DropDownData.shoeType" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="运动鞋" prop="sportsShoesType">
							<el-select v-model="dataForm.sportsShoesType" placeholder="请选择运动鞋">
								<el-option v-for="item in DropDownData.sportsShoesType" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="品牌" prop="brand">
							<el-select v-model="dataForm.brand" placeholder="请选择品牌">
								<el-option v-for="item in DropDownData.brand" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="头型" prop="headShape">
							<el-select v-model="dataForm.headShape" placeholder="请选择头型">
								<el-option v-for="item in DropDownData.headShape" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="鞋码" prop="shoeSize">
							<el-select v-model="dataForm.shoeSize" placeholder="请选择鞋码(美码)">
								<el-option v-for="item in DropDownData.shoeSize" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="风格" prop="style">
							<el-select v-model="dataForm.style" placeholder="请选择风格">
								<el-option v-for="item in DropDownData.style" :key="item.id" :value="item.nameType"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="皮鞋" prop="leatherShoes">
							<el-input v-model="dataForm.leatherShoes" placeholder="请输入皮鞋" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="楦底样长" prop="grountPattern">
							<el-input v-model="dataForm.grountPattern" placeholder="请输入楦底样长" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="楦跖围" prop="lastBottomGirth">
							<el-input v-model="dataForm.lastBottomGirth" placeholder="请输入楦跖围" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="楦跟高" prop="heelHeight">
							<el-input v-model="dataForm.heelHeight" placeholder="请输入楦跟高" />
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="文件" prop="file">
							<el-upload
								class="upload-demo"
								:action="uploadUrl"
								:show-file-list="false"
								:auto-upload="false"
								:on-change="(file) => handleSuccess(file)"
							>
								<el-button type="primary">上传文件</el-button>
							</el-upload>
							<div v-if="fileName" class="file-name">文件名称: {{ fileName }}</div>
						</el-form-item>
					</el-col>
					<el-col :span="12" class="mb20">
						<el-form-item label="图片" prop="image">
							<el-upload
								ref="uploadRef"
								:auto-upload="false"
								class="avatar-uploader"
								:show-file-list="false"
								:on-change="(file) => onSelectFile(imageFile, file)"
								:action="imageFile.image"
							>
								<img v-if="imageFile.imgFile" :src="imageFile.imgFile" class="avatar" />
								<img v-else-if="imageFile.image" :src="imageFile.image" class="avatar" />
								<el-icon v-else class="avatar-uploader-icon">
									<Plus />
								</el-icon>
							</el-upload>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="visible = false">{{ $t('common.cancelButtonText') }}</el-button>
					<el-button @click="onSubmit" type="primary" :disabled="loading">{{ $t('common.confirmButtonText') }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { updateByIdFootLast, getFootLastById, saveFootLast, uploadObj, getDataType } from '/@/api/gai/foot';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { ElMessage } from 'element-plus';
import { userList } from '/@/api/admin/user';
const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);
// @ts-ignore

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const fileName = ref();
const uploadUrl = ref();
const fileUrl = ref();

const imageFile = ref({
	image: '',
	imgFile: '',
});

const dataForm = ref({
	id: '',
	userId: '',
	category: '',
	shoeType: '',
	sportsShoesType: '',
	brand: '',
	headShape: '',
	shoeSize: '',
	shoeShape: '',
	style: '',
	file: '',
	image: '',
	fileName: '',
	leatherShoes: '',
	grountPattern: '',
	lastBottomGirth: '',
	heelHeight: '',
});
const dataRules = ref({
	category: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
	shoeType: [{ required: true, message: '品类不能为空', trigger: 'blur' }],
	fileName: [
		{ required: true, message: '文件名称不能为空', trigger: 'blur' },
		{ max: 10, message: '文件名称不能超过15个字符', trigger: 'change' },
	],
});

// 打开弹窗
const userIds = ref();
const typeData = ref();
const DropDownData = ref({
	category: [],
	shoeType: '',
	sportsShoesType: [],
	brand: [],
	headShape: [],
	shoeSize: [],
	shoeShape: [],
	style: [],
});
const openDialog = async (id: string, userId?: number) => {
	visible.value = true; // 先显示对话框
	loading.value = true; // 显示加载状态

	// 重置表单数据
	dataForm.value = {
		id: '',
		userId: '',
		category: '',
		shoeType: '',
		sportsShoesType: '',
		brand: '',
		headShape: '',
		shoeSize: '',
		shoeShape: '',
		style: '',
		file: '',
		image: '',
		fileName: '',
		leatherShoes: '',
		grountPattern: '',
		lastBottomGirth: '',
		heelHeight: '',
	};
	imageFile.value.image = '';
	fileName.value = '';

	try {
		// 进行异步操作
		// console.log('ID value before API call:', id);
		const [userResult, typeResult] = await Promise.all([userList(), getDataType('0')]);

		// 如果传入了userId参数，使用传入的值；否则使用API返回的值
		userIds.value = userId || userResult.data.sysUser.userId;

		typeData.value = typeResult.data;
		typeData.value.forEach((item) => {
			if (item.children) {
				DropDownData.value[item.ename] = item.children;
			}
		});

		// 根据 id 进行编辑操作
		if (id) {
			const result = await getFootLastById(id);
			imageFile.value.image = result.data.image;
			fileName.value = result.data.file.split('/').pop();
			fileUrl.value = result.data.file;

			dataForm.value.file = fileUrl.value;
			dataForm.value.image = imageFile.value.image;
			Object.assign(dataForm.value, result.data);
			console.log('result.data', result.data);
		}
	} catch (error) {
		console.error('Error loading data:', error);
	} finally {
		// 数据加载完成后显示对话框
		loading.value = false; // 隐藏加载状态
	}
};

// 提交
const onSubmit = async () => {
	const valid = await dataFormRef.value.validate().catch(() => {});
	if (!valid) return false;

	try {
		const { id } = dataForm.value;

		if (!fileUrl.value) {
			ElMessage.error('请上传必要的文件！');
			return;
		}
		if (!imageFile.value.image) {
			ElMessage.error('请上传必要的图片');
			return;
		}

		loading.value = true;
		dataForm.value.file = fileUrl.value;
		dataForm.value.image = imageFile.value.image;
		dataForm.value.userId = userIds.value;

		if (id) {
			await updateByIdFootLast(dataForm.value);
			useMessage().success(t('common.editSuccessText'));
		} else {
			await saveFootLast(dataForm.value);
			useMessage().success(t('common.addSuccessText'));
		}

		visible.value = false; // 关闭弹窗
		emit('refresh');
	} catch (error: any) {
		useMessage().error(error.msg);
	} finally {
		loading.value = false;
	}
};

const uploadRef = ref();

const onSelectFile = (imageFile, file) => {
	// 检查文件类型
	const fileType = file.raw.type;
	const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/svg+xml'];

	if (!validImageTypes.includes(fileType)) {
		ElMessage({
			message: '请上传正确的图片格式！',
			type: 'warning',
		});
		return;
	}

	// 基于 FileReader 读取图片做预览
	const reader = new FileReader();
	reader.onload = (e) => {
		imageFile.image = e?.target?.result;

		const formData = new FormData();
		formData.append('mouldFile', file.raw);

		uploadObj(formData)
			.then((response) => {
				imageFile.image = response.data;
			})
			.catch((error) => {
				ElMessage({
					message: '上传失败，请重试！',
					type: 'info',
				});
				return;
			});
	};

	reader.readAsDataURL(file.raw);
};

const handleSuccess = async (file: any) => {
	// 检查文件类型
	const validFileExtensions = ['obj', 'stl', '3dm', 'stp', 'step', 'mtl'];
	const fileExtension = file.name.split('.').pop().toLowerCase();

	if (!validFileExtensions.includes(fileExtension)) {
		ElMessage({
			message: '请上传正确的图片格式！，例如：.obj, .stl, .3dm, .stp, .step, .mtl',
			type: 'warning',
		});
		return;
	}

	// 创建 FormData 并上传文件
	const formData = new FormData();
	formData.append('mouldFile', file.raw);

	try {
		const result = await uploadObj(formData);
		if (result.data) {
			fileUrl.value = result.data;
			fileName.value = file.name;
		}
	} catch (error) {
		ElMessage({
			message: '上传失败，请重试！',
			type: 'info',
		});
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.avatar-uploader {
	:deep() {
		.avatar {
			width: 180px;
			height: 125px;
			display: block;
		}

		.el-upload {
			border: 1px dashed var(--el-border-color);
			border-radius: 6px;
			cursor: pointer;
			position: relative;
			overflow: hidden;
			transition: var(--el-transition-duration-fast);
		}

		.el-upload:hover {
			border-color: var(--el-color-primary);
		}

		.el-icon.avatar-uploader-icon {
			font-size: 28px;
			color: #8c939d;
			width: 180px;
			height: 125px;
			text-align: center;
		}
	}
}
.file-name {
	margin-top: 10px;
	color: #409eff;
}
.upload-demo .el-upload {
	display: inline-block;
}
</style>
