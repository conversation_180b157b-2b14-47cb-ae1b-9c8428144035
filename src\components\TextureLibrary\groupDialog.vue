<template>
	<el-dialog v-model="visible" :title="isEdit ? '编辑分组' : '新增分组'" width="400px" :close-on-click-modal="false">
		<el-form :model="form" ref="formRef" :rules="rules" label-width="80px">
			<el-form-item label="分组名称" prop="groupName">
				<el-input v-model="form.groupName" placeholder="请输入分组名称" />
			</el-form-item>
			<el-form-item label="父级分组" prop="parentId">
				<el-tree-select
					v-model="form.parentId"
					:data="groupTree"
					:props="{ value: 'id', label: 'groupName', children: 'children' }"
					check-strictly
					placeholder="请选择父级分组"
					clearable
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="handleSubmit">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { addTextureGroup, updateTextureGroup } from '/@/api/gai/texture';

const props = defineProps({
	groups: {
		type: Array,
		default: () => [],
	},
});

const groupTree = computed(() => {
	const buildTree = (groups: any[], parentId: string | null = null): any[] => {
		return groups
			.filter((group) => group.parentId === parentId)
			.map((group) => ({
				...group,
				children: buildTree(groups, group.id),
			}));
	};
	return buildTree(props.groups as any[]);
});

const visible = ref(false);
const isEdit = ref(false);
const formRef = ref();

const form = reactive({
	id: '',
	groupName: '',
	userId: 1,
	parentId: null,
});

const rules = {
	groupName: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
};

const emit = defineEmits(['refresh']);

const openDialog = (item: any, userId: number) => {
	visible.value = true;
	isEdit.value = !!item.id;
	form.userId = userId;

	if (isEdit.value) {
		Object.assign(form, item);
	} else {
		Object.assign(form, { id: '', groupName: '', parentId: null });
	}
};

const handleSubmit = () => {
	formRef.value.validate(async (valid: boolean) => {
		if (valid) {
			const { id, ...data } = form;
			const res: any = isEdit.value ? await updateTextureGroup(id, data) : await addTextureGroup(data);
			if (res.ok) {
				ElMessage.success(isEdit.value ? '修改成功' : '新增成功');
				visible.value = false;
				emit('refresh');
			}
		}
	});
};

defineExpose({ openDialog });
</script>
