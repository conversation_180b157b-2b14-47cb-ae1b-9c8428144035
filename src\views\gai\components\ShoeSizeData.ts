// 鞋号对照表数据
export interface ShoeSizeData {
  footLength: number;
  toeGirth: number;
}

export interface ShoeSizeTable {
  [key: string]: ShoeSizeData;
}

export interface AllShoeSizeTables {
  [series: string]: ShoeSizeTable;
}

// 根据图片提供的数据建立鞋号对照表
export const shoeSizeData: AllShoeSizeTables = {
  // 世界鞋号1
  world1: {
    '210': { footLength: 210, toeGirth: 215.0 },
    '215': { footLength: 215, toeGirth: 218.5 },
    '220': { footLength: 220, toeGirth: 222.0 },
    '225': { footLength: 225, toeGirth: 225.5 },
    '230': { footLength: 230, toeGirth: 229.0 },
    '235': { footLength: 235, toeGirth: 232.5 },
    '240': { footLength: 240, toeGirth: 236.0 },
    '245': { footLength: 245, toeGirth: 239.5 },
    '250': { footLength: 250, toeGirth: 243.0 },
    '255': { footLength: 255, toeGirth: 246.5 },
    '260': { footLength: 260, toeGirth: 250.0 },
    '265': { footLength: 265, toeGirth: 253.5 },
    '270': { footLength: 270, toeGirth: 257.0 },
    '275': { footLength: 275, toeGirth: 260.5 },
    '280': { footLength: 280, toeGirth: 264.0 },
    '285': { footLength: 285, toeGirth: 267.5 },
    '290': { footLength: 290, toeGirth: 271.0 },
    '295': { footLength: 295, toeGirth: 274.5 },
    '300': { footLength: 300, toeGirth: 278.0 },
    '305': { footLength: 305, toeGirth: 281.5 },
    '310': { footLength: 310, toeGirth: 285.0 },
    '315': { footLength: 315, toeGirth: 288.5 },
    '320': { footLength: 320, toeGirth: 292.0 },
    '325': { footLength: 325, toeGirth: 295.5 },
    '330': { footLength: 330, toeGirth: 299.0 },
    '335': { footLength: 335, toeGirth: 302.5 },
    '340': { footLength: 340, toeGirth: 306.0 },
    '345': { footLength: 345, toeGirth: 309.5 },
    '350': { footLength: 350, toeGirth: 313.0 },
    '355': { footLength: 355, toeGirth: 316.2 },
    '360': { footLength: 360, toeGirth: 319.7 },
  },

  // 世界鞋号2
  world2: {
    '217.5': { footLength: 217.5, toeGirth: 220.2 },
    '225': { footLength: 225, toeGirth: 225.5 },
    '232.5': { footLength: 232.5, toeGirth: 230.8 },
    '240': { footLength: 240, toeGirth: 236.0 },
    '247.5': { footLength: 247.5, toeGirth: 241.3 },
    '255': { footLength: 255, toeGirth: 246.5 },
    '262.5': { footLength: 262.5, toeGirth: 251.8 },
    '270': { footLength: 270, toeGirth: 257.0 },
    '277.5': { footLength: 277.5, toeGirth: 262.3 },
    '285': { footLength: 285, toeGirth: 267.5 },
    '292.5': { footLength: 292.5, toeGirth: 272.8 },
    '300': { footLength: 300, toeGirth: 278.0 },
    '307.5': { footLength: 307.5, toeGirth: 283.4 },
    '315': { footLength: 315, toeGirth: 288.5 },
  },

  // 欧洲鞋号
  euro: {
    '32': { footLength: 200.1, toeGirth: 208.1 },
    '32.5': { footLength: 203.4, toeGirth: 210.4 },
    '33': { footLength: 206.8, toeGirth: 212.8 },
    '33.5': { footLength: 210.1, toeGirth: 215.1 },
    '34': { footLength: 213.4, toeGirth: 217.4 },
    '34.5': { footLength: 216.8, toeGirth: 219.7 },
    '35': { footLength: 220.1, toeGirth: 222.1 },
    '35.5': { footLength: 223.4, toeGirth: 224.4 },
    '36': { footLength: 226.8, toeGirth: 226.8 },
    '36.5': { footLength: 230.1, toeGirth: 229.1 },
    '37': { footLength: 233.5, toeGirth: 231.5 },
    '37.5': { footLength: 236.8, toeGirth: 233.8 },
    '38': { footLength: 240.1, toeGirth: 236.1 },
    '38.5': { footLength: 243.5, toeGirth: 238.5 },
    '39': { footLength: 246.8, toeGirth: 240.8 },
    '39.5': { footLength: 250.1, toeGirth: 243.1 },
    '40': { footLength: 253.5, toeGirth: 245.5 },
    '40.5': { footLength: 256.8, toeGirth: 247.8 },
    '41': { footLength: 260.1, toeGirth: 250.1 },
    '41.5': { footLength: 263.5, toeGirth: 252.5 },
    '42': { footLength: 266.8, toeGirth: 254.8 },
    '42.5': { footLength: 270.1, toeGirth: 257.1 },
    '43': { footLength: 273.5, toeGirth: 259.5 },
    '43.5': { footLength: 276.8, toeGirth: 261.8 },
    '44': { footLength: 280.1, toeGirth: 264.1 },
    '44.5': { footLength: 283.5, toeGirth: 266.4 },
    '45': { footLength: 286.8, toeGirth: 268.8 },
    '45.5': { footLength: 290.1, toeGirth: 271.1 },
    '46': { footLength: 293.5, toeGirth: 273.5 },
    '46.5': { footLength: 296.8, toeGirth: 275.8 },
    '47': { footLength: 300.2, toeGirth: 278.1 },
    '47.5': { footLength: 303.5, toeGirth: 280.5 },
    '48': { footLength: 306.8, toeGirth: 282.8 },
    '48.5': { footLength: 310.2, toeGirth: 285.1 },
    '49': { footLength: 313.5, toeGirth: 287.5 },
    '49.5': { footLength: 316.8, toeGirth: 289.8 },
    '50': { footLength: 320.2, toeGirth: 292.1 },
    '50.5': { footLength: 323.5, toeGirth: 294.5 },
    '51': { footLength: 326.8, toeGirth: 296.8 },
    '51.5': { footLength: 330.2, toeGirth: 299.1 },
    '52': { footLength: 333.5, toeGirth: 301.5 },
    '52.5': { footLength: 336.8, toeGirth: 303.8 },
    '53': { footLength: 340.2, toeGirth: 306.1 },
    '53.5': { footLength: 343.5, toeGirth: 308.5 },
    '54': { footLength: 346.8, toeGirth: 310.8 },
    '54.5': { footLength: 350.2, toeGirth: 313.1 },
    '55': { footLength: 353.5, toeGirth: 314.8 },
    '55.5': { footLength: 356.8, toeGirth: 316.9 },
  },

  // 英国鞋号  
  uk: {
    '0': { footLength: 194.8, toeGirth: 204.4 },
    '0.5': { footLength: 199.0, toeGirth: 207.3 },
    '1': { footLength: 203.3, toeGirth: 210.3 },
    '1.5': { footLength: 207.5, toeGirth: 213.2 },
    '2': { footLength: 211.8, toeGirth: 216.2 },
    '2.5': { footLength: 216.0, toeGirth: 219.2 },
    '3': { footLength: 220.2, toeGirth: 222.1 },
    '3.5': { footLength: 224.5, toeGirth: 225.1 },
    '4': { footLength: 228.7, toeGirth: 228.1 },
    '4.5': { footLength: 232.9, toeGirth: 231.0 },
    '5': { footLength: 237.2, toeGirth: 234.0 },
    '5.5': { footLength: 241.4, toeGirth: 237.0 },
    '6': { footLength: 245.6, toeGirth: 239.9 },
    '6.5': { footLength: 249.9, toeGirth: 242.9 },
    '7': { footLength: 254.1, toeGirth: 245.9 },
    '7.5': { footLength: 258.3, toeGirth: 248.8 },
    '8': { footLength: 262.6, toeGirth: 251.8 },
    '8.5': { footLength: 266.8, toeGirth: 254.8 },
    '9': { footLength: 271.0, toeGirth: 257.7 },
    '9.5': { footLength: 275.3, toeGirth: 260.7 },
    '10': { footLength: 279.5, toeGirth: 263.7 },
    '10.5': { footLength: 283.7, toeGirth: 266.6 },
    '11': { footLength: 288.0, toeGirth: 269.6 },
    '11.5': { footLength: 292.2, toeGirth: 272.6 },
    '12': { footLength: 296.5, toeGirth: 275.6 },
    '12.5': { footLength: 300.7, toeGirth: 278.5 },
    '13': { footLength: 304.9, toeGirth: 281.4 },
    '13.5': { footLength: 309.2, toeGirth: 284.4 },
    '14': { footLength: 313.4, toeGirth: 287.4 },
    '14.5': { footLength: 317.6, toeGirth: 290.3 },
    '15': { footLength: 321.9, toeGirth: 293.3 },
    '15.5': { footLength: 326.1, toeGirth: 296.3 },
    '16': { footLength: 330.3, toeGirth: 299.2 },
    '16.5': { footLength: 334.6, toeGirth: 302.2 },
    '17': { footLength: 338.8, toeGirth: 305.3 },
    '17.5': { footLength: 343.0, toeGirth: 308.1 },
    '18': { footLength: 347.3, toeGirth: 311.1 },
    '18.5': { footLength: 351.5, toeGirth: 314.1 },
    '19': { footLength: 355.7, toeGirth: 316.7 },
    '19.5': { footLength: 360, toeGirth: 319.7 },
    '20': { footLength: 364.4, toeGirth: 323.1 },
    '20.5': { footLength: 368.8, toeGirth: 326.5 },
    '21': { footLength: 373.2, toeGirth: 329.9 },
  },

  // 美国鞋号男
  usMen: {
    '1': { footLength: 194.8, toeGirth: 204.4 },
    '1.5': { footLength: 199.0, toeGirth: 207.3 },
    '2': { footLength: 203.3, toeGirth: 210.3 },
    '2.5': { footLength: 207.5, toeGirth: 213.2 },
    '3': { footLength: 211.8, toeGirth: 216.2 },
    '3.5': { footLength: 216.0, toeGirth: 219.2 },
    '4': { footLength: 220.2, toeGirth: 222.1 },
    '4.5': { footLength: 224.5, toeGirth: 225.1 },
    '5': { footLength: 228.7, toeGirth: 228.1 },
    '5.5': { footLength: 232.9, toeGirth: 231.0 },
    '6': { footLength: 237.2, toeGirth: 234.0 },
    '6.5': { footLength: 241.4, toeGirth: 237.0 },
    '7': { footLength: 245.6, toeGirth: 239.9 },
    '7.5': { footLength: 249.9, toeGirth: 242.9 },
    '8': { footLength: 254.1, toeGirth: 245.9 },
    '8.5': { footLength: 258.3, toeGirth: 248.8 },
    '9': { footLength: 262.6, toeGirth: 251.8 },
    '9.5': { footLength: 266.8, toeGirth: 254.8 },
    '10': { footLength: 271.0, toeGirth: 257.7 },
    '10.5': { footLength: 275.3, toeGirth: 260.7 },
    '11': { footLength: 279.5, toeGirth: 263.7 },
    '11.5': { footLength: 283.7, toeGirth: 266.6 },
    '12': { footLength: 288.0, toeGirth: 269.6 },
    '12.5': { footLength: 292.2, toeGirth: 272.6 },
    '13': { footLength: 296.5, toeGirth: 275.6 },
    '13.5': { footLength: 300.7, toeGirth: 278.5 },
    '14': { footLength: 304.9, toeGirth: 281.4 },
    '14.5': { footLength: 309.2, toeGirth: 284.4 },
    '15': { footLength: 313.4, toeGirth: 287.4 },
    '15.5': { footLength: 317.6, toeGirth: 290.3 },
    '16': { footLength: 321.9, toeGirth: 293.3 },
    '16.5': { footLength: 326.1, toeGirth: 296.3 },
    '17': { footLength: 330.3, toeGirth: 299.2 },
    '17.5': { footLength: 334.6, toeGirth: 302.2 },
    '18': { footLength: 338.8, toeGirth: 305.3 },
    '18.5': { footLength: 343.0, toeGirth: 308.1 },
    '19': { footLength: 347.3, toeGirth: 311.1 },
    '19.5': { footLength: 351.5, toeGirth: 314.1 },
    '20': { footLength: 355.7, toeGirth: 316.7 },
    '20.5': { footLength: 360, toeGirth: 319.7 },
    '21': { footLength: 364.4, toeGirth: 323.1 },
  },

  // 美国鞋号女
  usWomen: {
    '2': { footLength: 194.8, toeGirth: 204.4 },
    '2.5': { footLength: 199.0, toeGirth: 207.3 },
    '3': { footLength: 203.3, toeGirth: 210.3 },
    '3.5': { footLength: 207.5, toeGirth: 213.2 },
    '4': { footLength: 211.8, toeGirth: 216.2 },
    '4.5': { footLength: 216.0, toeGirth: 219.2 },
    '5': { footLength: 220.2, toeGirth: 222.1 },
    '5.5': { footLength: 224.5, toeGirth: 225.1 },
    '6': { footLength: 228.7, toeGirth: 228.1 },
    '6.5': { footLength: 232.9, toeGirth: 231.0 },
    '7': { footLength: 237.2, toeGirth: 234.0 },
    '7.5': { footLength: 241.4, toeGirth: 237.0 },
    '8': { footLength: 245.6, toeGirth: 239.9 },
    '8.5': { footLength: 249.9, toeGirth: 242.9 },
    '9': { footLength: 254.1, toeGirth: 245.9 },
    '9.5': { footLength: 258.3, toeGirth: 248.8 },
    '10': { footLength: 262.6, toeGirth: 251.8 },
    '10.5': { footLength: 266.8, toeGirth: 254.8 },
    '11': { footLength: 271.0, toeGirth: 257.7 },
    '11.5': { footLength: 275.3, toeGirth: 260.7 },
    '12': { footLength: 279.5, toeGirth: 263.7 },
    '12.5': { footLength: 283.7, toeGirth: 266.6 },
    '13': { footLength: 288.0, toeGirth: 269.6 },
    '13.5': { footLength: 292.2, toeGirth: 272.6 },
    '14': { footLength: 296.5, toeGirth: 275.6 },
    '14.5': { footLength: 300.7, toeGirth: 278.5 },
    '15': { footLength: 304.9, toeGirth: 281.4 },
    '15.5': { footLength: 309.2, toeGirth: 284.4 },
    '16': { footLength: 313.4, toeGirth: 287.4 },
    '16.5': { footLength: 317.6, toeGirth: 290.3 },
    '17': { footLength: 321.9, toeGirth: 293.3 },
    '17.5': { footLength: 326.1, toeGirth: 296.3 },
    '18': { footLength: 330.3, toeGirth: 299.2 },
    '18.5': { footLength: 334.6, toeGirth: 302.2 },
    '19': { footLength: 338.8, toeGirth: 305.3 },
    '19.5': { footLength: 343.0, toeGirth: 308.1 },
    '20': { footLength: 347.3, toeGirth: 311.1 },
    '20.5': { footLength: 351.5, toeGirth: 314.1 },
    '21': { footLength: 355.7, toeGirth: 316.7 },
    '21.5': { footLength: 360, toeGirth: 319.7 },
  },
};

// 根据鞋号系列和标准楦鞋号查找脚长和趾围
export function findFootMeasurements(series: string, shoeSize: string): ShoeSizeData | null {
  const seriesData = shoeSizeData[series];
  if (!seriesData) {
    return null;
  }

  // 多种格式尝试匹配
  const inputSize = shoeSize.trim();

  // 1. 直接匹配原始输入
  if (seriesData[inputSize]) {
    return seriesData[inputSize];
  }

  // 2. 转换为数字后再转回字符串（去除多余的0）
  const numericValue = parseFloat(inputSize);
  if (!isNaN(numericValue)) {
    const normalizedSize = numericValue.toString();
    if (seriesData[normalizedSize]) {
      return seriesData[normalizedSize];
    }

    // 3. 如果是整数，也尝试匹配加.0的版本
    if (Number.isInteger(numericValue)) {
      const decimalVersion = numericValue.toFixed(1); // 例如 "7" -> "7.0"
      if (seriesData[decimalVersion]) {
        return seriesData[decimalVersion];
      }
    }
  }

  return null;
} 