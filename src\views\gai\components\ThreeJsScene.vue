<template>
  <div ref="container"></div>
</template>

<script setup lang="ts">
import * as THREE from 'three';
import { ref, onMounted, onUnmounted } from 'vue';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

const scene = ref(null);
const camera = ref(null);
const renderer = ref(null);
const controls = ref(null);

onMounted(() => {
  init();
});

onUnmounted(() => {
  window.removeEventListener('resize', onWindowResize);
});

function init() {
  // 设置 Rhino 模型的默认方向为 z-up
  THREE.Object3D.DefaultUp = new THREE.Vector3(0, 0, 1);

  // 创建场景和相机
  scene.value = new THREE.Scene();
  scene.value.background = new THREE.Color(1, 1, 1);
  camera.value = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
  camera.value.position.y = -30;
  camera.value.position.z = 30;

  // 创建渲染器并将其添加到 HTML 页面中
  renderer.value = new THREE.WebGLRenderer({ antialias: true });
  renderer.value.setPixelRatio(window.devicePixelRatio);
  renderer.value.setSize(window.innerWidth, window.innerHeight);
  document.body.appendChild(renderer.value.domElement);

  // 添加控制器以围绕相机轨道旋转
  controls.value = new OrbitControls(camera.value, renderer.value.domElement);

  // 添加定向光源
  const directionalLight = new THREE.DirectionalLight(0xffffff);
  directionalLight.intensity = 2;
  scene.value.add(directionalLight);

  const ambientLight = new THREE.AmbientLight();
  scene.value.add(ambientLight);

  // 处理窗口大小变化
  window.addEventListener('resize', onWindowResize, false);

  // 开始渲染动画
  animate();
}

function onWindowResize() {
  camera.value.aspect = window.innerWidth / window.innerHeight;
  camera.value.updateProjectionMatrix();
  renderer.value.setSize(window.innerWidth, window.innerHeight);
  animate();
}

function animate() {
  requestAnimationFrame(animate);
  renderer.value.render(scene.value, camera.value);
}
</script>

<style>
#container {
  width: 100%;
  height: 100%;
}
</style>
